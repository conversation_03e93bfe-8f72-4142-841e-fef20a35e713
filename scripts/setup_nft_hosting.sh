#!/bin/bash

# Setup script for NFT hosting on the VPS
# This script sets up the directories and permissions for hosting NFT metadata and images

set -e

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${G<PERSON>EN}Setting up NFT hosting directories...${NC}"

# Base directory for the application
NFT_BASE_DIR="/opt/spectershift"
UPLOADS_DIR="$NFT_BASE_DIR/uploads"
NFT_DIR="$UPLOADS_DIR/nft"
METADATA_DIR="$NFT_DIR/metadata"
IMAGES_DIR="$NFT_DIR/images"
BACKUP_DIR="$NFT_BASE_DIR/backups/nft"

# Create directories with proper permissions
echo "Creating directories..."
mkdir -p $METADATA_DIR
mkdir -p $IMAGES_DIR
mkdir -p $BACKUP_DIR

# Set permissions (assuming the application runs as the same user)
echo "Setting permissions..."
chmod -R 755 $UPLOADS_DIR
chown -R root:root $UPLOADS_DIR

# Create a backup script for NFT data
BACKUP_SCRIPT="$NFT_BASE_DIR/backup_nft.sh"
echo "Creating backup script at $BACKUP_SCRIPT"

cat > $BACKUP_SCRIPT << 'EOL'
#!/bin/bash

# Backup script for NFT metadata and images
# This script should be run periodically to backup NFT data

set -e

# Define directories
NFT_BASE_DIR="/opt/spectershift"
UPLOADS_DIR="$NFT_BASE_DIR/uploads"
NFT_DIR="$UPLOADS_DIR/nft"
BACKUP_DIR="$NFT_BASE_DIR/backups/nft"

# Create timestamp for backup
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/nft_backup_$TIMESTAMP.tar.gz"

# Create backup
echo "Creating backup of NFT data..."
tar -czf $BACKUP_FILE -C $UPLOADS_DIR nft

# Keep only the 10 most recent backups
echo "Cleaning up old backups..."
ls -t $BACKUP_DIR/nft_backup_*.tar.gz | tail -n +11 | xargs -r rm

echo "Backup completed: $BACKUP_FILE"
EOL

# Make the backup script executable
chmod +x $BACKUP_SCRIPT

# Add backup script to crontab if it's not already there
if ! crontab -l | grep -q "backup_nft.sh"; then
  echo "Adding backup script to crontab (daily at 3:30 AM)..."
  (crontab -l 2>/dev/null; echo "30 3 * * * $BACKUP_SCRIPT >> $NFT_BASE_DIR/logs/nft_backup.log 2>&1") | crontab -
fi

# Create a test metadata file for verification
echo "Creating test metadata file..."
TEST_METADATA='{
  "name": "Test Pet Specter",
  "description": "This is a test pet specter for verifying the NFT hosting setup",
  "image": "https://spectershift.merchgenieai.com/nft/images/test.png",
  "external_url": "https://spectershift.merchgenieai.com/game",
  "attributes": [
    {
      "trait_type": "Type",
      "value": "TEST"
    },
    {
      "trait_type": "Tier",
      "value": "COMMON"
    }
  ]
}'

echo $TEST_METADATA > "$METADATA_DIR/test.json"

# Test image (base64 encoded transparent PNG)
echo "Creating test image file..."
TEST_IMAGE_B64="iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFrGlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDUgNzkuMTYzNDk5LCAyMDE4LzA4LzEzLTE2OjQwOjIyICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxOSAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjMtMTEtMDJUMTA6MzU6NDcrMDE6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDIzLTExLTAyVDEwOjM3OjE1KzAxOjAwIiB4bXA6TWV0YWRhdGFEYXRlPSIyMDIzLTExLTAyVDEwOjM3OjE1KzAxOjAwIiBkYzpmb3JtYXQ9ImltYWdlL3BuZyIgcGhvdG9zaG9wOkNvbG9yTW9kZT0iMyIgcGhvdG9zaG9wOklDQ1Byb2ZpbGU9InNSR0IgSUVDNjE5NjYtMi4xIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOmVmZTRhZjNlLWE0MjAtNDA5Yy1hNjMzLTRlNzAxZTJjYjI3ZCIgeG1wTU06RG9jdW1lbnRJRD0iYWRvYmU6ZG9jaWQ6cGhvdG9zaG9wOjcwYzA2MTUzLWUzY2YtMzk0NS1hYmE0LTUxYzA5OGNmMGYyNiIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjMyOTY1N2U1LTg5N2ItNGQ0OS05OGY2LWY4NTk4NmY0NmViOCI+IDx4bXBNTTpIaXN0b3J5PiA8cmRmOlNlcT4gPHJkZjpsaSBzdEV2dDphY3Rpb249ImNyZWF0ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6MzI5NjU3ZTUtODk3Yi00ZDQ5LTk4ZjYtZjg1OTg2ZjQ2ZWI4IiBzdEV2dDp3aGVuPSIyMDIzLTExLTAyVDEwOjM1OjQ3KzAxOjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxOSAoTWFjaW50b3NoKSIvPiA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0ic2F2ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6ZWZlNGFmM2UtYTQyMC00MDljLWE2MzMtNGU3MDFlMmNiMjdkIiBzdEV2dDp3aGVuPSIyMDIzLTExLTAyVDEwOjM3OjE1KzAxOjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxOSAoTWFjaW50b3NoKSIgc3RFdnQ6Y2hhbmdlZD0iLyIvPiA8L3JkZjpTZXE+IDwveG1wTU06SGlzdG9yeT4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz40tGz7AAAPN0lEQVR42u3dX2xb913H8c/3nOPjOI7j1k7sLG3TpC7t1nVbx7auW7ehwQYPQ2xCaEjTEB1I2x6YNsQLaEiTBuIBgRA88AAaEhIPSDChCSQkQMBgY6w0dP3Tuuvapkm6LY2dxHb8L+fwgNM2Wy1hxc6xfXxeLynqVaP2l+Pzfr/z+1etQqEQBCCfTEYAgAAACACAAAAIAIAAAAgAgAgAgAAACACAAAAIAIAAAAgAgAgAgAAACACAAAAIAIAAAAgAgAgAgAAACACAAAAIAIAAAAgAgAAACACAAAAIAIAAAAgAgAAACACAAAAIAIAAAAgAgAAACACAAAAIAIAAAAgAQAAARABAAIAIAAhAHjm374MbNmywTZs22dKlS62jo8PKy8utVCrZ3Nyc/X979+61I0eO8NsFRIC3DcC+fftCe3u7M5nMv1v89ttPX0sms5pZICLEagT27t0bKioq3nCY5eXlbsCAgfh3AXv27Anbtm3zSulbeZ5fMVjGYuLfBezYscNrWc48oNAXGwRChBEIpTIP7G8aAaVsxo8bNx9wYybEFgGllJmZK+uRgdDGo0HM9wRCITQeGXYD5sS3CwibDw2HggYrQ0bElwR27twZJmb9hkPDJhKgFOYiQmxBIEwl/Ib9wyZKFwMDgXgDsHv37nDjYQPB5Zo5RCC+ALhcGKdM5YUJEYglAGY2ThnKmxIhniQQbnnQBu8KQQQQIoBbeZDvDCGCmHYCFy5cCOVlFVZTVWl11TVWVVnN1BFBPHYB165dC2Oj4zY+eUuTA7fszBcnGDoiiMcocP78+byDRwRxBsAYLSLICwCGiwjyAoABI4K8AGDIiCAvABg0IsgLAIaNCPICgIEjgrwAYOiIIC8AGDwiyAsAho8I8gKABSCCvABgEYggLwBYCCLICwAWgwjyAoAFIYK8AGBRiCAvAFgYIsgLABaHCPICgAUigrwAYJGIIC8AWCgiyAsAFosI8gKABSOCvABg0YggLwBYOCLICwAWjwjyAoAFJIK8AGARiSAvAFhIIsgLABaTCPICYH5+3t9t8YggLwAiDVtEEF8AKirK/Z69OxFBXAGorCz3u3btJAIhngD09vb6jz5xNEeEQtwACIXgP/rE0dw7IickRYfX19eP/999rC9oV2+UqfZbNZpinEnwXef7/P7rmnxfr7+63vcr9Njc8wTWFRsKjvDnz+/PkwODhob7319reBaGtrJQTC4rgj0D//Cx+OHHnCnzt3wcbGRm1yYtIKhYBpC/cngu/58PcGePKTjwettU3PnrJsdpapC/e1vP7DggCwevXqUFJSbHbHD3748NEcUxfuVwj6Yv4AuHP/AMCW/VKdNkxfuD999KAP2uZDbrb/9gBYs2ZNKCutMzv3D/+b5+Ks5T77KaYw3I/y1hf7o0G9Kwu595aHGwOADRs2hNqaOivJvGNT09PJJ3cf4UAgYshHxxuD8zOFZ556JrF+fVv41rfe1NzcPBMZFvUx4Zfee0/S5JOJpPnJrPl0ys4ePW5jI+M2Nzsb+VBOnToVdu7c6ZjWsFijIC+bGpuwkdFxG7w5YnNzOfu/LowtlgMgABi4OWJjY5NW3rzKWprbWBRbLWsbN26UJ11CXlnSa1uqvH399Gkzk6VS0iL7d4gAAJhMOtWlSmVp16S2dFrNazaqrr7eGYDKtFnHSq9PrfSFbIqJDYtWd3e3u3Z9QKNfedFTb35D3iXyDU1aUVMt16TW0NSgXbt3sQvwwVYrfKRIUy/dtvhnP7ZY7wMgAJhXU6PyVWn5iQlL3L7PVq1eTQAW+0HAQUJJ511Z/LPPEvS7AAABAR0A5IJ3FwKyQgKVqbRFcZ8Yj9mCxwMAQiAAABEAAQCIAAgAQARAAgAQAZAAgAiABABEACQAIAIgAQARAKM/AXjSV6i1PFWrm+UbLjKxgTsC5a+3yrWXaKBoqb5Z2u5fPXuOmQ2LFoDqYu+3riiYf7VTqtpuxeWl+pHPPMHkhi6OTlhyV4WmfuFxzWwq0vxAj+p//EmFQmB+w6IEwBe3Vm+QrqyQ3P2WnntSyeSXNNPaYE2NjQQgtg8Fcda+spxVHnLmlq3R5MvPySefkE8f1diN6ywOvA2HDDPBJmemrTC4QSrd5S5dvGDJZWnVtazQPB9H5R2A2ZlZm8ikFV641XRzVpptU+rwCXv17BkmdsyLu+JL+uydAXWs7Qo2+5Jbvv5TcnMJm7lynt0A5OWoEN9XHnbbW6Tu/Q9bc0udXn/jDaY1EBAEQABABEAAAAQARAA3HsKJRw7X2cDT0bY2rWBmPkgFzZY4+WJn3nlH/uKpnK1cVbKoo8JhwEUuGaRkslgpL5tVIp92vWUFztyxWd15/5pyZb1jzx/bRwCKVNpLufmUCt6rEPJKmrMQclJDzpKf/KA9dfKzuvzBJbtx9VrmfjkTUChE+tX0XFw3LPTOZf2G9Xlr2LqCiR1TAJZ2dvjM1k3Wvf2Auje12tzrL2r6nw6qp+ea+VffG8XXJkYYAKe+1Q26/cjDWtf9Qdt15CGbuP6IXjt4UHMzcywCAtDc3OxWr11lnXcXadmKRj36xAFbsnm/rrzToyvnrpuZiwgCAvAeqfT6lZmcjY9PaPjmlNYfWmV37uzWwC/v0j+9cFY3h8aj+nck36JXWuNlrdWmuZLdmn7toN18Yb/9zKdOIIHbdJZ4ambaVi5vcE3NJUrM3NTSdEdkX6PKlcfa3JV9QW+d1GvnX9bZ8y+aH7sZ5vrvKrgiI4AIFYLkExZCwtrWdVj9wYfs0V/9xaiPBjT/xl21Hl6usRNnrfvT35D/8uUwf+OaeWIcWwBGu29oWctZpVJJNdQXaWpqxtJe2V4X5ddq8k6+Kav2Zc1qWVqvrQe26D8O/YpNT0wy/dgCYEE+41Re6q24ok6HTvyKKmrKNTYyo+bmBvsf91Vo/tqwjS9dr6MPH1JX1ybVN67Qa9/8hk3cuMrij3kXUFNbGebmV9rE+JzyzqthSUJDt4qj/bZaxZvWrlylo+t2W+3S5Tp79pheu/iaeeIb7y5gzZo1oaxsieZnqzU1kla2kNN0ZsKcj/rWmJMvnw8Tk1nx0n+8D2sIQG40J1dcFGy8JGwovWK+/57F8D0BVV5ebamd5aysqlZT5iu0edcx2/mxZ1hkAjD/v142t2GlPbD5c9a07qPW98Fnra3jCA8BZfEHYOTmDRvqS+v4U8+rfsMB69p1TD8++Vm1tz/FO/3yvy4RduaMO7bfSst+UcpaLp3QyOgVJpblPwDdKx5Qe0k2TCZ7nBqO2o3JN7Rj9Z9paOgFNv/xVl9fH65fv25bt261np4eW9Owxm5cucBY8n0g0AftVMOxMOZ7lJJLKuOmQpky+mXTczwCzO+L5wstBcv5cZvzu5n8DIItP99ZKF7Iy+d8zoZt1P5h+icFKq0KW+dHLS0GwfcNAHyRAwEAAQAQARAAABFABAAgAog3AMlkknECEdwi/G5+6MSJ2zxWGBb1vQGj/L2KwUdylnrw5pD19fXZyMiIffjhh5lfEGkEFnUXEIVCoWAnT560gwcP8i9A5GcBBIAAcBeAeANQUlLiZmdn+dchYgjGxsZidndgAmDQly+/rSXVM8wtiPzo7sKFCy5uAFRVVYX+/n6rq6u3oeEp5hZEnoGxsbFYAbB58+awdu1a9z9vFnLLRv5jmVsQ+Q6gv78/NgCUlJQEyfn/99jsVSvxo0wtiHwXsHPnztgA0NLSEvL5vBOcjV//Jjs8s4sQEkwtiHxnGCcAmpubg3POgvnp6aGe9OvDNtw3wMSCWO0C4gJAS0tLWLJkifnCnF37+j/akTfOamR4lGkFEcvn8+7cuXOxAKCxsTH09vbayI0r+vyf/J7NTMzYgz/2mN1/333c/w8UJwDa29vD6Oio9fZ+YK/80TH71h+/oJnstFa2LdPO3bucvDNnTswC7k4dHR0hn89bf3+/ffO3Pq9XD75qLWsbbXQ6a339/XZ7V3Fqampqymdnw+Z77w2JRIJpI7LfVD9u3Jw5c8YdP348DAzc1Pnz/bb7Jw5pe98nFJyzkdFR6+/rt6eePqnf+Z3f+pfFvgsI169fN9HxMeBvhvNyXvDhvffes1OnTtlPvf7Ldvbos25sbNxCCLZ169bw3HPPBbODt98X4MKFC06+yJxzzh9++GGF4Dgci/ve1S4UPnodx/l8Pt4vAtwb67cJf+XKFYvlNBAA7sUuIO8A8IYQkNeGdPbsWW4KAsQCgCVLlvj29nYCAACAAABANADoP/7TGx//idZQmJ+TuFwQXA3/6vbA3m+8EDzfHJyZVEyf+TmmlRePXrkxnbh2pS+MjY7b1OSUhRBYpDjuAmqXt4SW5laVbX/ASvce1+TJ18399Rfk5nZ85HX1lNrcqrI1r9Zo7rG0lZc0hk3/9Vl9Y+9vIoF4wnDl8oBmtXLxXeQxQkJBIbgg7xV8EB1AxFFgDeDoYjExJAAgAiABABEACQCIAEgAQASgKAEoKS1mSghxBKC0rNiXl6WY2FGeBeSDTVT5JZ9ea8c33q9fWtuY5KqfsdjZ5AeAOQ0M35x3B1smmNCwaHeHK5euuKllTTY5MaHxSwnz3geLEgGRYP93AQqheulyzTx0RH7i79VXdFnVhQ0qXt5gUW2T+S3A/zLkDtY2G/tIrlcl1b95Ql+bWKF5h8t27VhOBMD9B8D0TFZzM9Mcow1IAEAAQARAAABEAAQAIAIgAQARAAkAiABIAEAEQAIAIgASABABxACA4IeHbvdl//zLJ3OcEQYEACQAIAIgAQARALkjEEAEQAIAIgCyAwCIAEgAQARAAkAEQHYAIAIgAQARADkMCBABkACAiQAkACACiJ1SCDYXQgglzClqAELP1WDW79RfXqXl67qsqLJCrrmJwXOGLyw+AK5e7Q+pwwesvH23lqzpVrquVKnaCvlSSVc6mGCwfEEzKnbFGv/CkXD4J35dN3suW274E9Zxy9tv+CX23/5ZJX76ceX8pJJTmw4c/7D9bMdDmr0wqeGxC8p+2GO5GzM2M3NLhRDsTl+XmdldAWDmglNbfW3wVZubDmEu75V3zvI5M3P2kYv97UXJuyA5r2BmLuhOnM+BL3K5C87fudEPm5+7E+fv3OiHm/r8nRDdmdGPf77Pz2nt+p3as3+/LljQlYEBDQ5e1MjIiHJz82Zmt+dm4u5W+R+LDzCkJSRtKAAAAABJRU5ErkJggg=="
echo $TEST_IMAGE_B64 | base64 -d > "$IMAGES_DIR/test.png"

# Setup complete
echo -e "${GREEN}NFT hosting setup completed successfully!${NC}"
echo ""
echo -e "${YELLOW}Test your setup by accessing:${NC}"
echo "https://spectershift.merchgenieai.com/nft/metadata/test.json"
echo "https://spectershift.merchgenieai.com/nft/images/test.png"
echo ""
echo -e "${YELLOW}Backup script installed at:${NC} $BACKUP_SCRIPT"
echo "Daily backups will be stored in $BACKUP_DIR" 