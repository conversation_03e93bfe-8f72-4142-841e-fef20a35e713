# SpecterShift Codebase Map

This document provides a high-level overview of the SpecterShift codebase structure, with a focus on **PVP (Player vs Player)** and **<PERSON>pecter** functionalities.

## Core Directories

*   **`client/`**: Contains all frontend code, built with React and Three.js.
    *   `client/src/game/`: Houses the core game logic.
        *   `client/src/game/entities/`: Defines game entities like `Player.ts`, `PetSpecter.ts`, `DungeonEnemy.ts`, etc.
        *   `client/src/game/pvp/`: Contains PVP-specific client logic (`TournamentBattleClient.ts`, `PVPArenaManager.ts`).
        *   `client/src/game/engine/`: Core game engine components (`GameEngine.ts`, `RenderEngine.ts`, etc. - *Assumption based on naming*).
        *   `client/src/game/ui/`: UI components related to the game interface (e.g., HUD, menus).
        *   `client/src/game/world/`: Dungeon generation and world management.
        *   `client/src/game/inventory/`: Likely handles player inventory, potentially including specters.
    *   `client/src/hooks/`: Custom React hooks, potentially for state management or fetching data.
    *   `client/src/contexts/` / `client/src/providers/`: React context API for global state management.
    *   `client/src/services/`: Client-side services, possibly for API interaction.
    *   `client/public/assets/`: Static assets like sprites, textures, audio.
*   **`server/`**: Contains all backend code (Node.js/Express likely).
    *   Key files like `index.ts` (main server entry), `routes.ts` (API endpoint definitions), `db.ts` (database connection), `unifiedWebSocket.ts` (WebSocket server), `petApi.ts` (pet-related logic), `nftApi.ts` (NFT interaction), `aiImageService.ts` (AI image generation), and `storage.ts` (data persistence) are located directly in this directory.
    *   `server/src/`: Contains subdirectories for more specific modules.
        *   `server/src/routes/`: May contain additional or more granular API endpoint definitions.
        *   `server/src/services/`: Houses backend services (e.g., `TournamentBattleService.ts`).
        *   `server/src/websocket/`: May contain components or utilities for the WebSocket server.
        *   `server/src/types/`: Server-specific TypeScript types.
*   **`shared/`**: Code shared between the client and server.
    *   `shared/petSchema.ts`: Likely contains the database schema or shared types/validation for Pet Specters.
    *   `shared/schema.ts`: Potentially other shared schemas (users, etc.).
*   **`contracts/`**: Smart contract code (Solidity likely) for NFTs.
*   **`migrations/`**: Database migration files (using Drizzle ORM based on `drizzle.config.ts`).

## Pet Specter Handling

*   **Definition & Core Logic**: `client/src/game/entities/PetSpecter.ts` seems to be the central file defining the Pet Specter entity on the client-side, including its behavior, rendering, attacks, leveling, and potentially NFT integration aspects.
*   **Persistence & Data**:
    *   Pet data is likely stored in the database according to the schema defined in `shared/petSchema.ts`.
    *   Loading pet data likely involves API calls from the client (potentially using services in `client/src/services/` or hooks in `client/src/hooks/`) to fetch data from server endpoints defined in `server/src/routes/`. These server endpoints would query the database.
    *   The requirement is to move away from `localStorage` towards database persistence, ensuring pets are loaded based on the logged-in user (OrangeID or MetaMask wallet address) by fetching from the backend upon login/game start.
*   **NFT Integration**:
    *   Smart contracts in `contracts/` define the NFT.
    *   Minting logic likely involves interactions with the backend (server API) which then interacts with the blockchain via the contracts.
    *   Checking ownership (`NFT browser`) would involve client-side wallet interaction (e.g., using ethers.js or viem) or backend checks.
    *   The `PetSpecter.ts` class might hold NFT-specific data (token ID, stats derived from NFT).
*   **AI Generation**:
    *   Managed by the backend, likely involving a dedicated service (not explicitly listed, might be within API routes or a separate file in `server/src/services/`).
    *   Uses FAL AI (`FAL_KEY` environment variable) and `rembg` for image processing. Requires `rembg` to be installed and used as a Python library on the server.
*   **Spawning & Summoning**:
    *   Currently potentially tied to client-side state or `localStorage`, leading to the persistence issue.
    *   Needs modification to load active pets from the database upon game initialization for the specific user.
    *   The Pet Menu UI component (likely in `client/src/game/ui/` or `client/src/components/`) needs logic to enforce the two-pet maximum summoning limit, possibly by checking the number of currently active `PetSpecter` instances associated with the player or querying the player's active pets from the database/state.

## PVP Handling

*   **Server-Side Battles**: `server/src/services/TournamentBattleService.ts` appears to handle the core logic for running PVP battles independently on the server. This likely includes managing battle state, calculating outcomes, and handling intervals.
*   **Client-Side Representation**: `client/src/game/pvp/TournamentBattleClient.ts` likely connects to the server (via WebSockets, managed in `server/src/websocket/`) to receive battle updates and visualize the ongoing battles.
*   **Arena**: `client/src/game/pvp/PVPArenaManager.ts` probably manages the visual representation of the coliseum-style arena and spectator movement within it.
*   **Tournament Structure**: Logic for creating tournaments, handling entry fees (POL), managing entrants, and distributing prize pools might reside in backend services (`server/src/services/`) and API routes (`server/src/routes/`).
*   **Pet Selection & Power-ups**: UI components (likely in `client/src/game/ui/` or `client/src/components/`) would handle player selection of pets and power-ups before a tournament/battle, sending this information to the backend.

## Key Files Summary

*   `client/src/game/entities/PetSpecter.ts`: Core client-side pet logic.
*   `shared/petSchema.ts`: Shared pet data structure/schema.
*   `server/index.ts`: Main server entry point.
*   `server/routes.ts`: Main API endpoint definitions.
*   `server/unifiedWebSocket.ts`: Core WebSocket server implementation.
*   `server/petApi.ts`: Handles backend logic for Pet Specters (data, AI generation).
*   `server/storage.ts` / `server/postgresStorage.ts` / `server/memPetStorage.ts`: Database interaction and storage logic.
*   `server/src/services/TournamentBattleService.ts`: Core server-side PVP battle logic.
*   `client/src/game/pvp/TournamentBattleClient.ts`: Client-side PVP connection and visualization.
*   `client/src/game/pvp/PVPArenaManager.ts`: Client-side PVP arena management.
*   Backend API routes (primarily in `server/routes.ts`, potentially supplemented by `server/src/routes/`) and services (`server/src/services/` and files like `server/petApi.ts`, `server/aiImageService.ts`): Handling pet data fetching/saving, AI generation requests, tournament management.
*   Frontend UI components (`client/src/game/ui/`, `client/src/components/`): Pet menu, tournament interface, NFT browser.

## PVP Production Flow

The PVP system in SpecterShift operates with a client-server architecture, where the server authoritatively manages battle logic and the client visualizes it.

1.  **Tournament Creation & Joining (HTTP API & Client UI):**
    *   **Creation:** A user or an automated system initiates tournament creation via a client UI. The client sends a `POST` request to the `/api/tournament-battles/tournaments` endpoint on the server. This request typically includes tournament parameters (name, max participants, entry fee POL). The `TournamentBattleService` on the server handles this, creates a new tournament instance, and often auto-registers the creator's chosen pet.
    *   **Discovery:** Users can view a list of active/open tournaments. The client UI fetches this list by making a `GET` request to `/api/tournament-battles/tournaments`.
    *   **Joining:** A player joins an existing tournament through the client UI. The client sends a `POST` request to `/api/tournament-battles/tournaments/:id/join` with their `userId`, selected `petId`, and chosen `powerups`. The `TournamentBattleService` validates the request and adds the participant to the tournament roster.

2.  **Battle Initialization (Server-Side - `TournamentBattleService`):**
    *   Once a tournament has enough participants or predefined conditions are met (or if a test battle is created via `POST /api/tournament-battles/test`), the `TournamentBattleService` initiates battles.
    *   It groups participants into specific battle instances, each with a unique `battleId`.
    *   The service prepares the initial state for each battle, including participant details (pets, stats, starting positions).

3.  **Client Connection for Spectating (WebSocket & Client UI):**
    *   When a user wants to spectate an ongoing battle (e.g., by selecting it from a list in the UI), the client initiates a WebSocket connection.
    *   The client connects to the WebSocket server at the `/tournament` path, usually providing the `battleId` as a query parameter (e.g., `ws://server_address/tournament?battleId=some_battle_id`).
    *   The `server/unifiedWebSocket.ts` handles this connection. If the path is `/tournament`, it routes the connection and subsequent messages to the `TournamentBattleService`.
    *   The `TournamentBattleService` then adds the user to the list of spectators for that battle using `addSpectatorToBattle(battleId, userId)`.

4.  **Arena Setup & Spectator Mode (Client-Side - `PVPArenaManager`, `TournamentBattleClient`, `GameEngine`):**
    *   The `PVPArenaManager` on the client is responsible for setting up the visual environment for spectating.
    *   It calls `setupArena(battleId, true)`, which in turn:
        *   Signals the `GameEngine` to enter spectator mode (`gameEngine.setSpectatorMode(true)`), disabling player controls and potentially adjusting the camera.
        *   Triggers the loading of the PVP arena map/environment (e.g., `ColiseumArena` or similar, managed by `GameEngine`).
    *   The `TournamentBattleClient` (or `PVPArenaManager` via `TournamentBattleClient`) formally messages the server (e.g., `MessageType.TournamentBattleJoin`) confirming the intent to spectate the specific `battleId`.

5.  **Battle Simulation & State Synchronization (Server-Side via WebSocket):**
    *   The `TournamentBattleService` contains the core battle simulation logic, running in an update loop (`updateBattles`, `simulateBattleTurn`).
    *   During each simulation step, the service:
        *   Calculates pet actions (movement, attacks, use of power-ups) based on pet stats, AI logic, and game rules.
        *   Determines damage, updates pet health, and checks for KO conditions.
        *   Generates a series of `RenderInstruction` objects (e.g., `RenderInstructionType.SpawnEntity`, `MoveEntity`, `Attack`, `DamageEntity`, `Victory`) that describe the visual changes that occurred during that tick.
    *   The server sends these updates to all connected clients (spectators) for that specific battle:
        *   `MessageType.TournamentBattleUpdate`: Contains the overall current state of the battle (participant health, positions, status).
        *   `MessageType.TournamentBattleRenderInstructions`: Contains an array of specific `RenderInstruction` objects for the client to execute.
    *   These messages are broadcast using functions like `sendBattleMessage(battleId, ...)` or `broadcastBattleUpdate(battleId)` from `server/unifiedWebSocket.ts`.

6.  **Battle Visualization (Client-Side - `TournamentBattleClient`, `GameEngine`):**
    *   The `client/src/game/pvp/TournamentBattleClient.ts` receives the WebSocket messages from the server.
    *   Upon receiving `MessageType.TournamentBattleUpdate`, it updates its internal model of the battle state (e.g., participant health in a UI, positions).
    *   Upon receiving `MessageType.TournamentBattleRenderInstructions`, it iterates through the `instructions` array:
        *   `SpawnEntity`: Instructs the `GameEngine` to create or display 3D models for the Pet Specters at specified positions/rotations within the Three.js scene.
        *   `MoveEntity`: Updates the `position` and `rotation` of existing Pet Specter models in the scene.
        *   `Attack`, `UsePowerup`, `DamageEntity`: Triggers the `GameEngine` to play corresponding visual effects (e.g., projectile animations, particle effects for impacts or power-ups, damage numbers).
        *   `Victory`: Instructs the `GameEngine` to display victory animations or messages for the winning pet/player.
    *   The `GameEngine` continuously renders the Three.js scene, providing a real-time visual representation of the battle as dictated by server instructions.

7.  **Battle Conclusion & Tournament Progression (Server-Side - `TournamentBattleService`):**
    *   When a battle concludes (e.g., one pet defeats all others), the `TournamentBattleService.endBattle()` method is called.
    *   The service records the winner and updates the overall tournament state.
    *   If part of an ongoing tournament, it may set up the next round of battles, advancing the winner.
    *   A final battle result message is sent to clients.
    *   For the tournament's final battle, the service handles the determination of the ultimate winner and may trigger logic for prize distribution (potentially interacting with smart contracts if entry fees/prizes are on-chain POL tokens).

8.  **Client Cleanup (Client-Side - `PVPArenaManager`, `TournamentBattleClient`):**
    *   When a user decides to stop spectating, or if the battle/tournament they are watching ends:
        *   The `PVPArenaManager.dispose()` method is called. This cleans up the arena view, signals the `GameEngine` to exit spectator mode (`gameEngine.setSpectatorMode(false)`), and may send a formal "leave battle" message to the server via WebSocket.
        *   The `TournamentBattleClient.leaveBattle()` method is called to clear any client-side data specific to that battle (cached participant info, effects, etc.) and remove associated WebSocket message handlers.

This flow centralizes authoritative battle logic on the server (`TournamentBattleService`) and uses WebSockets (`unifiedWebSocket.ts`) for real-time communication of state and render instructions to clients (`TournamentBattleClient`, `PVPArenaManager`), which are responsible for the visual representation using the `GameEngine`.

