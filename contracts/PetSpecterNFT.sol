// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Strings.sol";

contract PetSpecterNFT is ERC721Enumerable, Ownable {
    using Strings for uint256;
    
    // Base URI for metadata
    string private _baseTokenURI;
    
    // Mapping from token ID to price paid
    mapping(uint256 => uint256) private _tokenPrices;

    // Tournament structure
    struct Tournament {
        uint256 id;
        uint256 entryFee;
        uint256 prizePot;
        uint256 entrantCount;
        uint256 maxEntrants;
        bool active;
        address creator;
        uint256 createdAt;
    }

    // Tournament storage
    mapping(uint256 => Tournament) public tournaments;
    uint256 public tournamentCount;
    
    // Track tournament participants
    mapping(uint256 => mapping(uint256 => bool)) public tokenInTournament; // tournamentId => tokenId => isParticipating
    mapping(uint256 => uint256[]) private tournamentParticipants; // tournamentId => tokenIds
    
    // Platform fee percentages
    uint256 public mintFeeBps = 300; // 3% (in basis points - 100 = 1%)
    uint256 public tournamentFeeBps = 3000; // 30% (in basis points - 100 = 1%)

    // Test mode parameters
    bool public testMode = true; // Start in test mode
    uint256 public constant TEST_MINT_PRICE = 0.01 ether; // Very low for testnet (approx 0.01 POL)
    uint256 public constant TEST_TOURNAMENT_FEE = 0.02 ether; // 0.02 POL for tournaments in test mode
    uint256 public constant PROD_MIN_MINT_PRICE = 0.1 ether; // Min price in production
    uint256 public constant PROD_TOURNAMENT_FEE = 2 ether; // 2 POL for tournaments in production
    
    // Events
    event PetSpecterMinted(address indexed owner, uint256 indexed tokenId, uint256 price);
    event BaseURIUpdated(string newBaseURI);
    event TournamentCreated(uint256 indexed tournamentId, address indexed creator, uint256 entryFee, uint256 maxEntrants);
    event PetEnteredTournament(uint256 indexed tournamentId, uint256 indexed tokenId, address owner);
    event TournamentModeChanged(bool testMode);
    event FeesUpdated(uint256 mintFeeBps, uint256 tournamentFeeBps);
    
    constructor(string memory initialBaseURI) ERC721("SpecterShift Pets", "SSP") Ownable(msg.sender) {
        _baseTokenURI = initialBaseURI;
    }
    
    /**
     * @dev Returns the base URI for token metadata
     */
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }
    
    /**
     * @dev Update the base URI for token metadata
     * @param newBaseURI New base URI
     */
    function setBaseURI(string memory newBaseURI) external onlyOwner {
        _baseTokenURI = newBaseURI;
        emit BaseURIUpdated(newBaseURI);
    }
    
    /**
     * @dev Toggle test mode (affects pricing)
     * @param _testMode Whether to enable test mode
     */
    function setTestMode(bool _testMode) external onlyOwner {
        testMode = _testMode;
        emit TournamentModeChanged(_testMode);
    }
    
    /**
     * @dev Update fee percentages (in basis points, 100 = 1%)
     * @param _mintFeeBps Mint fee in basis points
     * @param _tournamentFeeBps Tournament fee in basis points
     */
    function updateFees(uint256 _mintFeeBps, uint256 _tournamentFeeBps) external onlyOwner {
        require(_mintFeeBps <= 1000, "Mint fee too high"); // Max 10%
        require(_tournamentFeeBps <= 5000, "Tournament fee too high"); // Max 50%
        
        mintFeeBps = _mintFeeBps;
        tournamentFeeBps = _tournamentFeeBps;
        
        emit FeesUpdated(_mintFeeBps, _tournamentFeeBps);
    }
    
    /**
     * @dev Get the price paid for a specific token
     * @param tokenId Token ID
     * @return Price paid in wei
     */
    function getTokenPrice(uint256 tokenId) external view returns (uint256) {
        require(_exists(tokenId), "PetSpecterNFT: Price query for nonexistent token");
        return _tokenPrices[tokenId];
    }
    
    /**
     * @dev Get current mint price based on mode
     */
    function getMintPrice() public view returns (uint256) {
        return testMode ? TEST_MINT_PRICE : PROD_MIN_MINT_PRICE;
    }
    
    /**
     * @dev Get current tournament entry fee based on mode
     */
    function getTournamentFee() public view returns (uint256) {
        return testMode ? TEST_TOURNAMENT_FEE : PROD_TOURNAMENT_FEE;
    }
    
    /**
     * @dev Mint a new pet specter NFT
     * @return tokenId The minted token ID
     */
    function mintPetSpecter() external payable returns (uint256) {
        uint256 mintPrice = getMintPrice();
        require(msg.value >= mintPrice, "PetSpecterNFT: Insufficient payment");
        
        // Calculate platform fee
        uint256 platformFee = (mintPrice * mintFeeBps) / 10000;
        
        // Mint token
        uint256 tokenId = totalSupply() + 1;
        _safeMint(msg.sender, tokenId);
        
        // Record price paid
        _tokenPrices[tokenId] = mintPrice;
        
        // Transfer platform fee to owner
        (bool success, ) = owner().call{value: platformFee}("");
        require(success, "PetSpecterNFT: Fee transfer failed");
        
        // Refund excess payment if any
        uint256 refundAmount = msg.value - mintPrice;
        if (refundAmount > 0) {
            (bool refundSuccess, ) = msg.sender.call{value: refundAmount}("");
            require(refundSuccess, "PetSpecterNFT: Refund failed");
        }
        
        emit PetSpecterMinted(msg.sender, tokenId, mintPrice);
        
        return tokenId;
    }
    
    /**
     * @dev Create a new tournament
     * @param maxEntrants Maximum number of entrants (4-20)
     * @return tournamentId The created tournament ID
     */
    function createTournament(uint256 maxEntrants) external payable returns (uint256) {
        require(maxEntrants >= 4 && maxEntrants <= 20, "Invalid number of entrants");
        uint256 tournamentFee = getTournamentFee();
        require(msg.value >= tournamentFee, "Insufficient tournament fee");
        
        // Create new tournament
        tournamentCount++;
        uint256 tournamentId = tournamentCount;
        
        tournaments[tournamentId] = Tournament({
            id: tournamentId,
            entryFee: tournamentFee,
            prizePot: tournamentFee, // Initial prize pot is the entry fee
            entrantCount: 0,
            maxEntrants: maxEntrants,
            active: true,
            creator: msg.sender,
            createdAt: block.timestamp
        });
        
        // Refund excess payment if any
        if (msg.value > tournamentFee) {
            (bool refundSuccess, ) = msg.sender.call{value: msg.value - tournamentFee}("");
            require(refundSuccess, "Tournament refund failed");
        }
        
        emit TournamentCreated(tournamentId, msg.sender, tournamentFee, maxEntrants);
        return tournamentId;
    }
    
    /**
     * @dev Enter a pet specter into a tournament
     * @param tournamentId The tournament ID
     * @param tokenId The pet specter token ID to enter
     */
    function enterTournament(uint256 tournamentId, uint256 tokenId) external payable {
        Tournament storage tournament = tournaments[tournamentId];
        require(tournament.active, "Tournament not active");
        require(tournament.entrantCount < tournament.maxEntrants, "Tournament full");
        require(ownerOf(tokenId) == msg.sender, "Not token owner");
        require(!tokenInTournament[tournamentId][tokenId], "Already in tournament");
        
        uint256 entryFee = tournament.entryFee;
        require(msg.value >= entryFee, "Insufficient entry fee");
        
        // Add pet to tournament
        tokenInTournament[tournamentId][tokenId] = true;
        tournamentParticipants[tournamentId].push(tokenId);
        tournament.entrantCount++;
        
        // Add entry fee to prize pot
        tournament.prizePot += entryFee;
        
        // Refund excess payment if any
        if (msg.value > entryFee) {
            (bool refundSuccess, ) = msg.sender.call{value: msg.value - entryFee}("");
            require(refundSuccess, "Entry fee refund failed");
        }
        
        emit PetEnteredTournament(tournamentId, tokenId, msg.sender);
    }
    
    /**
     * @dev Get all participants in a tournament
     * @param tournamentId The tournament ID
     * @return An array of token IDs participating in the tournament
     */
    function getTournamentParticipants(uint256 tournamentId) external view returns (uint256[] memory) {
        return tournamentParticipants[tournamentId];
    }
    
    /**
     * @dev End a tournament and distribute prizes (must be called by owner)
     * @param tournamentId The tournament ID
     * @param winners Array of winner token IDs in order (first place first)
     * @param percentages Array of prize percentages for each winner (must sum to 70%)
     */
    function endTournament(uint256 tournamentId, uint256[] calldata winners, uint256[] calldata percentages) external onlyOwner {
        Tournament storage tournament = tournaments[tournamentId];
        require(tournament.active, "Tournament not active");
        require(winners.length > 0 && winners.length <= tournament.entrantCount, "Invalid winners count");
        require(winners.length == percentages.length, "Winners and percentages must match");
        
        uint256 totalPercentage = 0;
        for (uint256 i = 0; i < percentages.length; i++) {
            totalPercentage += percentages[i];
        }
        require(totalPercentage == 70, "Percentages must sum to 70%");
        
        // Validate all winners are participants
        for (uint256 i = 0; i < winners.length; i++) {
            require(tokenInTournament[tournamentId][winners[i]], "Invalid winner");
        }
        
        // Calculate platform fee (30% of prize pot)
        uint256 platformFee = (tournament.prizePot * tournamentFeeBps) / 10000;
        uint256 totalPrize = tournament.prizePot - platformFee;
        
        // Distribute prizes to winners
        for (uint256 i = 0; i < winners.length; i++) {
            uint256 prizeAmount = (totalPrize * percentages[i]) / 100;
            address winnerAddress = ownerOf(winners[i]);
            (bool success, ) = winnerAddress.call{value: prizeAmount}("");
            require(success, "Prize distribution failed");
        }
        
        // Send platform fee to owner
        (bool feeSuccess, ) = owner().call{value: platformFee}("");
        require(feeSuccess, "Platform fee transfer failed");
        
        // Mark tournament as inactive
        tournament.active = false;
    }
    
    /**
     * @dev Override tokenURI to ensure it ends with .json
     */
    function tokenURI(uint256 tokenId) public view override returns (string memory) {
        require(_exists(tokenId), "PetSpecterNFT: URI query for nonexistent token");
        
        string memory baseURI = _baseURI();
        return bytes(baseURI).length > 0 ? string(abi.encodePacked(baseURI, tokenId.toString(), ".json")) : "";
    }
    
    /**
     * @dev Withdraw contract balance (minus any pending refunds)
     */
    function withdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "PetSpecterNFT: No balance to withdraw");
        
        (bool success, ) = owner().call{value: balance}("");
        require(success, "PetSpecterNFT: Withdrawal failed");
    }
    
    /**
     * @dev Check if token exists
     */
    function _exists(uint256 tokenId) internal view returns (bool) {
        return _ownerOf(tokenId) != address(0);
    }
} 