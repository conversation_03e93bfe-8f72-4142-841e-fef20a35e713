# ShatterShift Deployment Instructions

Follow these steps to deploy ShatterShift to the VPS at shattershift.merchgenieai.com.

## 1. Connect to the VPS

```
ssh root@**************
```

## 2. Create Directory Structure

```
mkdir -p /opt/shattershift
cd /opt/shattershift
```

## 3. Transfer the Game Files

**Option 1:** Transfer with SCP
From your local machine:
```
cd /path/to/local/SpecterShift
scp -r ./* root@**************:/opt/shattershift/
```

**Option 2:** Transfer with rsync
```
rsync -avz --exclude 'node_modules' --exclude '.git' /path/to/local/SpecterShift/ root@**************:/opt/shattershift/
```

## 4. Setup Node.js (on the VPS)

```
# Install Node.js LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Verify installation
node -v
npm -v
```

## 5. Install Dependencies and Build (on the VPS)

```
cd /opt/shattershift
npm ci
npm run build
```

## 6. Fix Server Host Configuration

The server is configured to listen on localhost, but we need it to listen on all interfaces:

```
# Update the host in the built file
sed -i 's/host: "localhost"/host: "0.0.0.0"/g' dist/index.js
```

## 7. Set Up PM2 for Process Management

```
# Install PM2 globally
npm install -g pm2

# Create ecosystem.config.js
cat > ecosystem.config.js << 'EOL'
module.exports = {
  apps: [{
    name: "shattershift",
    script: "dist/index.js",
    env: {
      NODE_ENV: "production",
      PORT: 5001
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: "1G"
  }]
};
EOL

# Start the application
pm2 start ecosystem.config.js

# Setup PM2 to start on boot
pm2 save
pm2 startup
```

## 8. Configure Nginx for the Subdomain

```
# Create Nginx configuration
cat > /etc/nginx/sites-available/shattershift.merchgenieai.com << 'EOL'
server {
    listen 80;
    server_name shattershift.merchgenieai.com;

    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /ws {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
EOL

# Create symlink to enable the site
ln -sf /etc/nginx/sites-available/shattershift.merchgenieai.com /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Reload Nginx configuration
systemctl reload nginx
```

## 9. Set Up SSL with Let's Encrypt

```
# Install Certbot if not already installed
apt-get update
apt-get install -y certbot python3-certbot-nginx

# Obtain SSL certificate
certbot --nginx -d shattershift.merchgenieai.com --non-interactive --agree-tos --email <EMAIL>
```

## 10. Setup DNS Record

Ensure you add an A record for `shattershift.merchgenieai.com` pointing to the VPS IP address in your DNS provider.

## 11. Monitoring and Troubleshooting

```
# Check application logs
pm2 logs shattershift

# Check Nginx access and error logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Restart the application if needed
pm2 restart shattershift

# Reload Nginx if needed
systemctl reload nginx
```

## 12. Test the Deployment

Visit https://shattershift.merchgenieai.com in your browser to verify the game is working.

## Note About Multiplayer Issue

The multiplayer player positions issue (players frozen at 0,0) will still exist in this deployment as it's an unresolved bug in the codebase. Despite this limitation, the game should be accessible for the game jam submission. 

# Deployment Instructions for PetSpecterNFT Contract

This guide provides instructions for deploying the PetSpecterNFT contract to multiple test networks, including Polygon Amoy Testnet, Orange Testnet L1, and AVAX C-Chain.

## Prerequisites

- NodeJS (v14+) and npm/yarn installed
- Hardhat or Truffle development environment set up
- MetaMask wallet with test tokens for the target network
- Contract code in your project under `contracts/PetSpecterNFT.sol`

## Installation

1. Install required dependencies:

```bash
npm install --save-dev hardhat @nomiclabs/hardhat-ethers ethers @nomiclabs/hardhat-waffle @openzeppelin/contracts dotenv
```

2. Create a `.env` file in your project root (do not commit this to version control):

```
PRIVATE_KEY=your_wallet_private_key_here
POLYGONSCAN_API_KEY=your_polygonscan_api_key_if_you_want_to_verify
AMOY_RPC_URL=https://rpc-amoy.polygon.technology
ORANGE_TESTNET_RPC_URL=https://subnets.avax.network/orangetest/testnet/rpc
AVAX_TESTNET_RPC_URL=https://api.avax-test.network/ext/bc/C/rpc
```

3. Configure your hardhat.config.js:

```javascript
require("@nomiclabs/hardhat-waffle");
require("@nomiclabs/hardhat-etherscan");
require('dotenv').config();

module.exports = {
  solidity: "0.8.20",
  networks: {
    // Polygon Amoy Testnet
    amoy: {
      url: process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 80002
    },
    // Orange Testnet L1
    orangeTestnet: {
      url: process.env.ORANGE_TESTNET_RPC_URL || "https://subnets.avax.network/orangetest/testnet/rpc",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 987
    },
    // AVAX C-Chain Testnet (Fuji)
    avaxTestnet: {
      url: process.env.AVAX_TESTNET_RPC_URL || "https://api.avax-test.network/ext/bc/C/rpc",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 43113
    }
  },
  etherscan: {
    apiKey: process.env.POLYGONSCAN_API_KEY
  }
};
```

## Create Deployment Script

Create a new file `scripts/deploy.js`:

```javascript
const hre = require("hardhat");

async function main() {
  // Get the network name
  const network = hre.network.name;
  console.log(`Deploying to ${network}...`);

  // Get the contract factory
  const PetSpecterNFT = await hre.ethers.getContractFactory("PetSpecterNFT");
  
  // Define the base URI based on environment
  // Replace these with your actual API endpoints
  let baseURI;
  if (network === "amoy") {
    baseURI = "https://your-polygon-testnet-api.com/nft/metadata/";
  } else if (network === "orangeTestnet") {
    baseURI = "https://your-orange-testnet-api.com/nft/metadata/";
  } else if (network === "avaxTestnet") {
    baseURI = "https://your-avax-testnet-api.com/nft/metadata/";
  } else {
    baseURI = "https://your-api.com/nft/metadata/";
  }
  
  // Deploy the contract with the base URI
  const nftContract = await PetSpecterNFT.deploy(baseURI);
  await nftContract.deployed();
  
  console.log("PetSpecterNFT deployed to:", nftContract.address);
  console.log("Base URI set to:", baseURI);
  
  // Verify contract on block explorer if not on localhost
  if (network !== "localhost" && network !== "hardhat") {
    console.log("Waiting for block confirmations...");
    await nftContract.deployTransaction.wait(5); // Wait for 5 confirmations
    
    // Verify the contract on the block explorer
    console.log("Verifying contract on block explorer...");
    try {
      await hre.run("verify:verify", {
        address: nftContract.address,
        constructorArguments: [baseURI],
      });
      console.log("Contract verified successfully");
    } catch (error) {
      console.error("Error verifying contract:", error);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
```

## Deployment Process

### 1. Deploy to Polygon Amoy Testnet (Primary Option)

1. Ensure you have test POL in your wallet. You can get it from the Polygon Amoy faucet.

2. Deploy the contract:

```bash
npx hardhat run scripts/deploy.js --network amoy
```

3. After deployment, note the contract address output in the console.

4. The contract is deployed in test mode by default, which means:
   - Mint price: 0.01 POL (instead of 0.1 POL)
   - Tournament fee: 0.02 POL (instead of 2 POL)

### 2. Deploy to Orange Testnet L1 (Alternative)

1. Ensure you have Orange Testnet tokens in your wallet.

2. Deploy the contract:

```bash
npx hardhat run scripts/deploy.js --network orangeTestnet
```

3. After deployment, note the contract address output in the console.

4. Orange Testnet L1 Details:
   - Chain ID: 987
   - RPC URL: https://subnets.avax.network/orangetest/testnet/rpc
   - Currency Symbol: Usually displayed as AVAX

### 3. Deploy to AVAX C-Chain Testnet (Alternative)

1. Get AVAX testnet tokens from the AVAX faucet.

2. Deploy the contract:

```bash
npx hardhat run scripts/deploy.js --network avaxTestnet
```

3. After deployment, note the contract address output in the console.

## Post-Deployment Configuration

After deploying the contract, you'll need to:

1. **Update Base URI if needed**: If you need to change the base URI after deployment:

```javascript
// Using ethers.js
const contractAddress = "YOUR_DEPLOYED_CONTRACT_ADDRESS";
const PetSpecterNFT = await ethers.getContractFactory("PetSpecterNFT");
const contract = PetSpecterNFT.attach(contractAddress);

// Set new base URI
await contract.setBaseURI("https://your-new-api.com/nft/metadata/");
```

2. **Toggle Test Mode**: When ready to move to production mode with full prices:

```javascript
// Using ethers.js with the contract instance from above
await contract.setTestMode(false);
```

## Integration with Front-End

To interact with the contract from your front-end:

1. Configure web3 or ethers.js to connect to the correct network
2. Use the contract ABI and deployed address
3. Create functions for minting, creating tournaments, entering tournaments

Here's a sample code for creating a connection:

```javascript
import { ethers } from "ethers";
import PetSpecterNFT from "../artifacts/contracts/PetSpecterNFT.sol/PetSpecterNFT.json";

// Get network and contract info
const getContractInfo = (networkId) => {
  const networks = {
    80002: { // Polygon Amoy
      contractAddress: "YOUR_AMOY_CONTRACT_ADDRESS",
      name: "Polygon Amoy Testnet"
    },
    987: { // Orange Testnet
      contractAddress: "YOUR_ORANGE_TESTNET_CONTRACT_ADDRESS", 
      name: "Orange Testnet L1"
    },
    43113: { // AVAX C-Chain Testnet
      contractAddress: "YOUR_AVAX_TESTNET_CONTRACT_ADDRESS",
      name: "AVAX C-Chain Testnet"
    }
  };
  
  return networks[networkId] || null;
};

// Connect to the contract
const connectToContract = async () => {
  // Check if MetaMask is installed
  if (!window.ethereum) {
    throw new Error("MetaMask is not installed");
  }
  
  // Request account access
  const accounts = await window.ethereum.request({ method: "eth_requestAccounts" });
  
  // Create provider and signer
  const provider = new ethers.providers.Web3Provider(window.ethereum);
  const signer = provider.getSigner();
  
  // Get network
  const network = await provider.getNetwork();
  const contractInfo = getContractInfo(network.chainId);
  
  if (!contractInfo) {
    throw new Error("Unsupported network");
  }
  
  // Create contract instance
  const contract = new ethers.Contract(
    contractInfo.contractAddress,
    PetSpecterNFT.abi,
    signer
  );
  
  return { contract, account: accounts[0], network: contractInfo.name };
};
```

## Contract Verification

After deployment, it's important to verify your contract on the network's block explorer:

- For Polygon Amoy: Use Polygonscan Amoy
- For Orange Testnet: If they have a block explorer
- For AVAX C-Chain: Use Snowtrace (Fuji)

## Backend Integration

Ensure your backend API (nftApi.ts) is properly configured to handle metadata and image requests from all networks where you've deployed.

1. Make your backend API accessible via HTTPS from the internet
2. Configure CORS to allow requests from your frontend domain
3. Ensure your image fetching is working properly for all networks
4. Set the BASE_URL environment variable to your deployed backend URL

## Switching Between Networks Later

If you need to add or switch to a new network:

1. Deploy the contract on the new network
2. Update your frontend configuration to include the new contract address
3. Ensure your backend can handle requests from the new contract
4. Set the baseURI on the new contract to point to your backend API

## Troubleshooting

- **Low Gas Error**: Increase the gas limit in your deploy script
- **Contract Creation Failed**: Check that you have enough native tokens for gas
- **Contract Verification Failed**: Make sure the contract is fully deployed and has enough confirmations
- **Transaction Underpriced**: Increase the gas price or maxFeePerGas in your hardhat.config.js
- **Slow Transactions**: Some testnet networks can be slower than others, be patient

## Security Considerations

- The contract owner has significant control, such as ending tournaments and setting fees
- Keep your private keys secure and never expose them in public repositories
- Test thoroughly before allowing real users to interact with your contract
- Remember to withdraw accumulated funds regularly from the contract to a secure wallet 