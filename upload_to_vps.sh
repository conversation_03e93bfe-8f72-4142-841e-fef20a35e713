#!/bin/bash
set -e

# <PERSON><PERSON>t to upload ShatterShift game files to the VPS
echo "Uploading ShatterShift to VPS..."

# Variables
VPS_IP="**************"
VPS_USER="root"
REMOTE_DIR="/opt/spectershift"
CURRENT_DIR=$(pwd)

# First, run the backup script
if [ -f "$CURRENT_DIR/backup_vps.sh" ]; then
  echo "Running backup script first..."
  chmod +x "$CURRENT_DIR/backup_vps.sh"
  "$CURRENT_DIR/backup_vps.sh"
fi

# Create temporary directory for build files
echo "Creating temporary directory for build..."
TMP_DIR=$(mktemp -d)
mkdir -p "$TMP_DIR"

# Copy all files except node_modules and .git
echo "Copying files to temporary directory..."
rsync -a --exclude 'node_modules' --exclude '.git' --exclude '.cursor' "$CURRENT_DIR"/ "$TMP_DIR"/

echo "Creating remote directory structure..."
ssh $VPS_USER@$VPS_IP "mkdir -p $REMOTE_DIR"

# Preserve important configuration files before upload
echo "Preserving important configuration files..."
ssh $VPS_USER@$VPS_IP "mkdir -p /tmp/spectershift_config_backup"
ssh $VPS_USER@$VPS_IP "cp $REMOTE_DIR/.env /tmp/spectershift_config_backup/ 2>/dev/null || true"
ssh $VPS_USER@$VPS_IP "cp $REMOTE_DIR/ecosystem.config.js /tmp/spectershift_config_backup/ 2>/dev/null || true"

# Upload files to the VPS
echo "Uploading files to VPS..."
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude '.cursor' --exclude '.env' "$CURRENT_DIR"/ $VPS_USER@$VPS_IP:$REMOTE_DIR/

# Restore important configuration files
echo "Restoring important configuration files..."
ssh $VPS_USER@$VPS_IP "cp /tmp/spectershift_config_backup/.env $REMOTE_DIR/ 2>/dev/null || true"
ssh $VPS_USER@$VPS_IP "cp /tmp/spectershift_config_backup/ecosystem.config.js $REMOTE_DIR/ 2>/dev/null || true"

# Upload deployment script
echo "Uploading deployment script..."
scp "$CURRENT_DIR/deploy_shattershift.sh" $VPS_USER@$VPS_IP:$REMOTE_DIR/

# Set execute permissions on the deployment script
echo "Setting execute permissions..."
ssh $VPS_USER@$VPS_IP "chmod +x $REMOTE_DIR/deploy_shattershift.sh"

echo "Upload complete. Connect to the VPS and run the deployment script:"
echo "ssh $VPS_USER@$VPS_IP"
echo "cd $REMOTE_DIR"
echo "./deploy_shattershift.sh"

# Clean up temporary directory
echo "Cleaning up..."
rm -rf "$TMP_DIR"

echo "Done!"