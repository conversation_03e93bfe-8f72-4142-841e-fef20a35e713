#!/bin/bash
set -e

# Script to backup the current VPS deployment before updating
echo "Creating backup of current SpecterShift deployment on VPS..."

# Variables
VPS_IP="**************"
VPS_USER="root"
REMOTE_DIR="/opt/spectershift"
BACKUP_DIR="/opt/spectershift_backup_$(date +%Y%m%d_%H%M%S)"

# Create backup directory on VPS
echo "Creating backup directory on VPS..."
ssh $VPS_USER@$VPS_IP "mkdir -p $BACKUP_DIR"

# Copy current deployment to backup directory
echo "Backing up current deployment..."
ssh $VPS_USER@$VPS_IP "cp -r $REMOTE_DIR/* $BACKUP_DIR/ 2>/dev/null || true"

# Backup nginx configuration
echo "Backing up nginx configuration..."
ssh $VPS_USER@$VPS_IP "cp /etc/nginx/sites-available/spectershift.merchgenieai.com $BACKUP_DIR/nginx_config.backup 2>/dev/null || true"

# Backup .env file if it exists
echo "Backing up environment variables..."
ssh $VPS_USER@$VPS_IP "cp $REMOTE_DIR/.env $BACKUP_DIR/.env 2>/dev/null || true"

# Backup PM2 configuration
echo "Backing up PM2 configuration..."
ssh $VPS_USER@$VPS_IP "cp $REMOTE_DIR/ecosystem.config.js $BACKUP_DIR/ecosystem.config.js 2>/dev/null || true"

echo "Backup complete at $BACKUP_DIR on the VPS"
echo "To restore: ssh $VPS_USER@$VPS_IP \"cp -r $BACKUP_DIR/* $REMOTE_DIR/\""
