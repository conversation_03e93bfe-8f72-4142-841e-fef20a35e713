// Import jest-dom for DOM element assertions
require('@testing-library/jest-dom');

// Mock canvas for Three.js
require('jest-canvas-mock');

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock for WebGL context
HTMLCanvasElement.prototype.getContext = jest.fn().mockImplementation((contextType) => {
  if (contextType === 'webgl' || contextType === 'webgl2') {
    return {
      createShader: jest.fn(),
      createProgram: jest.fn(),
      createBuffer: jest.fn(),
      bindBuffer: jest.fn(),
      bufferData: jest.fn(),
      getAttribLocation: jest.fn(),
      enableVertexAttribArray: jest.fn(),
      vertexAttribPointer: jest.fn(),
      useProgram: jest.fn(),
      drawArrays: jest.fn(),
      viewport: jest.fn(),
      clearColor: jest.fn(),
      clear: jest.fn(),
      getExtension: jest.fn(),
      getShaderPrecisionFormat: jest.fn().mockReturnValue({
        precision: 23,
        rangeMin: 127,
        rangeMax: 127
      }),
      getParameter: jest.fn(),
      enable: jest.fn(),
      disable: jest.fn(),
      blendFunc: jest.fn(),
      drawElements: jest.fn(),
      createTexture: jest.fn(),
      bindTexture: jest.fn(),
      texImage2D: jest.fn(),
      texParameteri: jest.fn(),
      generateMipmap: jest.fn(),
      deleteTexture: jest.fn(),
      deleteBuffer: jest.fn(),
      deleteProgram: jest.fn(),
      deleteShader: jest.fn(),
    };
  }
  return null;
});

// Mock for Three.js
jest.mock('three', () => {
  const actualThree = jest.requireActual('three');
  return {
    ...actualThree,
    WebGLRenderer: jest.fn().mockImplementation(() => ({
      setSize: jest.fn(),
      setPixelRatio: jest.fn(),
      render: jest.fn(),
      shadowMap: {
        enabled: false,
        type: 1
      },
      domElement: document.createElement('canvas'),
      dispose: jest.fn(),
      setClearColor: jest.fn(),
      clear: jest.fn(),
      clearDepth: jest.fn(),
    })),
    AudioListener: jest.fn().mockImplementation(() => ({
      context: {
        state: 'running'
      },
      setMasterVolume: jest.fn(),
    })),
    Audio: jest.fn().mockImplementation(() => ({
      setBuffer: jest.fn(),
      setVolume: jest.fn(),
      play: jest.fn(),
      stop: jest.fn(),
      isPlaying: false,
    })),
    AudioLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn((url, onLoad) => {
        onLoad({});
      }),
    })),
  };
});

// Mock for cannon-es
jest.mock('cannon-es', () => {
  return {
    World: jest.fn().mockImplementation(() => ({
      gravity: { set: jest.fn() },
      addBody: jest.fn(),
      removeBody: jest.fn(),
      step: jest.fn(),
    })),
    Body: jest.fn().mockImplementation(() => ({
      position: { set: jest.fn(), copy: jest.fn() },
      quaternion: { set: jest.fn(), copy: jest.fn() },
      velocity: { set: jest.fn() },
      angularVelocity: { set: jest.fn() },
      applyForce: jest.fn(),
      applyImpulse: jest.fn(),
    })),
    Vec3: jest.fn().mockImplementation((x, y, z) => ({ x, y, z, set: jest.fn() })),
    Quaternion: jest.fn().mockImplementation(() => ({
      setFromAxisAngle: jest.fn(),
      set: jest.fn(),
    })),
    Box: jest.fn(),
    Sphere: jest.fn(),
    Plane: jest.fn(),
    Material: jest.fn(),
    ContactMaterial: jest.fn(),
  };
});

// Mock for howler
jest.mock('howler', () => {
  return {
    Howl: jest.fn().mockImplementation(() => ({
      play: jest.fn().mockReturnValue(1),
      stop: jest.fn(),
      volume: jest.fn(),
      rate: jest.fn(),
      seek: jest.fn(),
      loop: jest.fn(),
      state: jest.fn().mockReturnValue('loaded'),
      playing: jest.fn().mockReturnValue(false),
    })),
  };
});

// Mock for nipplejs
jest.mock('nipplejs', () => {
  return {
    create: jest.fn().mockReturnValue({
      on: jest.fn(),
      destroy: jest.fn(),
    }),
  };
});

// Mock for PointerLockControls
jest.mock('three/examples/jsm/controls/PointerLockControls', () => {
  return {
    PointerLockControls: jest.fn().mockImplementation(() => ({
      connect: jest.fn(),
      disconnect: jest.fn(),
      lock: jest.fn(),
      unlock: jest.fn(),
      isLocked: false,
      getObject: jest.fn().mockReturnValue({
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
      }),
    })),
  };
});

// Global mocks
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock for WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1,
}));

// Console mocks to reduce noise during tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
console.error = jest.fn((...args) => {
  if (args[0] && args[0].includes && args[0].includes('Warning:')) {
    return;
  }
  originalConsoleError(...args);
});
console.warn = jest.fn((...args) => {
  if (args[0] && args[0].includes && args[0].includes('Warning:')) {
    return;
  }
  originalConsoleWarn(...args);
});
