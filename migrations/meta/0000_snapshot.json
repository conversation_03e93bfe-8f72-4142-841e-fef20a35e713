{"id": "953a1156-6d12-4ad3-ab13-3a6ad7ff3723", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.game_arenas": {"name": "game_arenas", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "mode": {"name": "mode", "type": "text", "primaryKey": false, "notNull": true}, "max_players": {"name": "max_players", "type": "integer", "primaryKey": false, "notNull": true, "default": 4}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "current_level": {"name": "current_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "enemies_total": {"name": "enemies_total", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "enemies_defeated": {"name": "enemies_defeated", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'waiting'"}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.game_players": {"name": "game_players", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "arena_id": {"name": "arena_id", "type": "integer", "primaryKey": false, "notNull": true}, "player_id": {"name": "player_id", "type": "text", "primaryKey": false, "notNull": true}, "player_name": {"name": "player_name", "type": "text", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": false}, "is_host": {"name": "is_host", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "specters_captured": {"name": "specters_captured", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "position_x": {"name": "position_x", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'0'::jsonb"}, "position_y": {"name": "position_y", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'5'::jsonb"}, "position_z": {"name": "position_z", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'0'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.game_teams": {"name": "game_teams", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "arena_id": {"name": "arena_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "specters_captured": {"name": "specters_captured", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.high_scores": {"name": "high_scores", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "player_name": {"name": "player_name", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "game_mode": {"name": "game_mode", "type": "text", "primaryKey": false, "notNull": true, "default": "'single'"}, "team_name": {"name": "team_name", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.specter_types": {"name": "specter_types", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "base_points": {"name": "base_points", "type": "integer", "primaryKey": false, "notNull": true}, "teleports": {"name": "teleports", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "splits": {"name": "splits", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "camouflages": {"name": "camouflages", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.nft_transactions": {"name": "nft_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tx_hash": {"name": "tx_hash", "type": "text", "primaryKey": false, "notNull": true}, "token_id": {"name": "token_id", "type": "text", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "pet_specter_id": {"name": "pet_specter_id", "type": "integer", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "platform_fee": {"name": "platform_fee", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'completed'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"nft_transactions_pet_specter_id_pet_specters_id_fk": {"name": "nft_transactions_pet_specter_id_pet_specters_id_fk", "tableFrom": "nft_transactions", "tableTo": "pet_specters", "columnsFrom": ["pet_specter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"nft_transactions_tx_hash_unique": {"name": "nft_transactions_tx_hash_unique", "nullsNotDistinct": false, "columns": ["tx_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pet_specters": {"name": "pet_specters", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "game_id": {"name": "game_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "integer", "primaryKey": false, "notNull": false}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": false}, "token_id": {"name": "token_id", "type": "text", "primaryKey": false, "notNull": false}, "specter_type": {"name": "specter_type", "type": "text", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "xp": {"name": "xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "traits": {"name": "traits", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "equipment": {"name": "equipment", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "stats": {"name": "stats", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"pet_specters_owner_id_users_id_fk": {"name": "pet_specters_owner_id_users_id_fk", "tableFrom": "pet_specters", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pvp_bets": {"name": "pvp_bets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tx_hash": {"name": "tx_hash", "type": "text", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "match_id": {"name": "match_id", "type": "text", "primaryKey": false, "notNull": true}, "pet_specter_id": {"name": "pet_specter_id", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "odds": {"name": "odds", "type": "text", "primaryKey": false, "notNull": true}, "platform_fee": {"name": "platform_fee", "type": "text", "primaryKey": false, "notNull": true}, "potential_winnings": {"name": "potential_winnings", "type": "text", "primaryKey": false, "notNull": true}, "outcome": {"name": "outcome", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "paid_out": {"name": "paid_out", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"pvp_bets_pet_specter_id_pet_specters_id_fk": {"name": "pvp_bets_pet_specter_id_pet_specters_id_fk", "tableFrom": "pvp_bets", "tableTo": "pet_specters", "columnsFrom": ["pet_specter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pvp_bets_tx_hash_unique": {"name": "pvp_bets_tx_hash_unique", "nullsNotDistinct": false, "columns": ["tx_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pvp_matches": {"name": "pvp_matches", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "match_id": {"name": "match_id", "type": "text", "primaryKey": false, "notNull": true}, "player1_wallet_address": {"name": "player1_wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "player2_wallet_address": {"name": "player2_wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "player1_pet_id": {"name": "player1_pet_id", "type": "integer", "primaryKey": false, "notNull": false}, "player2_pet_id": {"name": "player2_pet_id", "type": "integer", "primaryKey": false, "notNull": false}, "winner_id": {"name": "winner_id", "type": "integer", "primaryKey": false, "notNull": false}, "total_bet_amount": {"name": "total_bet_amount", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "total_platform_fee": {"name": "total_platform_fee", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "prize_fee_contribution": {"name": "prize_fee_contribution", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"pvp_matches_player1_pet_id_pet_specters_id_fk": {"name": "pvp_matches_player1_pet_id_pet_specters_id_fk", "tableFrom": "pvp_matches", "tableTo": "pet_specters", "columnsFrom": ["player1_pet_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pvp_matches_player2_pet_id_pet_specters_id_fk": {"name": "pvp_matches_player2_pet_id_pet_specters_id_fk", "tableFrom": "pvp_matches", "tableTo": "pet_specters", "columnsFrom": ["player2_pet_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pvp_matches_winner_id_pet_specters_id_fk": {"name": "pvp_matches_winner_id_pet_specters_id_fk", "tableFrom": "pvp_matches", "tableTo": "pet_specters", "columnsFrom": ["winner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pvp_matches_match_id_unique": {"name": "pvp_matches_match_id_unique", "nullsNotDistinct": false, "columns": ["match_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tournament_participants": {"name": "tournament_participants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tournament_id": {"name": "tournament_id", "type": "integer", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "pet_specter_id": {"name": "pet_specter_id", "type": "integer", "primaryKey": false, "notNull": true}, "entry_tx_hash": {"name": "entry_tx_hash", "type": "text", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": false}, "prize": {"name": "prize", "type": "text", "primaryKey": false, "notNull": false}, "prize_tx_hash": {"name": "prize_tx_hash", "type": "text", "primaryKey": false, "notNull": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"tournament_participants_tournament_id_tournaments_id_fk": {"name": "tournament_participants_tournament_id_tournaments_id_fk", "tableFrom": "tournament_participants", "tableTo": "tournaments", "columnsFrom": ["tournament_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tournament_participants_pet_specter_id_pet_specters_id_fk": {"name": "tournament_participants_pet_specter_id_pet_specters_id_fk", "tableFrom": "tournament_participants", "tableTo": "pet_specters", "columnsFrom": ["pet_specter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tournaments": {"name": "tournaments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "entry_fee": {"name": "entry_fee", "type": "text", "primaryKey": false, "notNull": true}, "prize_pool": {"name": "prize_pool", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": true, "default": 16}, "current_participants": {"name": "current_participants", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'registration'"}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.training_transactions": {"name": "training_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tx_hash": {"name": "tx_hash", "type": "text", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "pet_specter_id": {"name": "pet_specter_id", "type": "integer", "primaryKey": false, "notNull": false}, "trait_type": {"name": "trait_type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "platform_fee": {"name": "platform_fee", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'completed'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {"training_transactions_pet_specter_id_pet_specters_id_fk": {"name": "training_transactions_pet_specter_id_pet_specters_id_fk", "tableFrom": "training_transactions", "tableTo": "pet_specters", "columnsFrom": ["pet_specter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"training_transactions_tx_hash_unique": {"name": "training_transactions_tx_hash_unique", "nullsNotDistinct": false, "columns": ["tx_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallet_balances": {"name": "wallet_balances", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "matic_balance": {"name": "matic_balance", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "ekto_balance": {"name": "ekto_balance", "type": "text", "primaryKey": false, "notNull": true, "default": "'0'"}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wallet_balances_user_id_users_id_fk": {"name": "wallet_balances_user_id_users_id_fk", "tableFrom": "wallet_balances", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"wallet_balances_wallet_address_unique": {"name": "wallet_balances_wallet_address_unique", "nullsNotDistinct": false, "columns": ["wallet_address"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}