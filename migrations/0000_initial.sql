CREATE TABLE IF NOT EXISTS pet_specters (
  id SERIAL PRIMARY KEY,
  game_id TEXT NOT NULL,
  name TEXT NOT NULL,
  specter_type TEXT NOT NULL,
  wallet_address TEXT,
  token_id TEXT,
  owner_id INTEGER,
  level INTEGER NOT NULL DEFAULT 1,
  xp INTEGER NOT NULL DEFAULT 0,
  stats JSONB NOT NULL DEFAULT '{"health": 100, "maxHealth": 100, "attackPower": 10, "defenseValue": 5, "speed": 5}'::jsonb,
  traits JSONB NOT NULL DEFAULT '[]'::jsonb,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  last_active TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS nft_transactions (
  id SERIAL PRIMARY KEY,
  tx_hash TEXT NOT NULL UNIQUE,
  token_id TEXT NOT NULL,
  wallet_address TEXT NOT NULL,
  pet_id INTEGER REFERENCES pet_specters(id),
  price NUMERIC(20, 8) NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL,
  metadata JSONB,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS wallet_balances (
  id SERIAL PRIMARY KEY,
  wallet_address TEXT NOT NULL UNIQUE,
  points INTEGER NOT NULL DEFAULT 0,
  tokens NUMERIC(20, 8) NOT NULL DEFAULT 0,
  metadata JSONB,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
