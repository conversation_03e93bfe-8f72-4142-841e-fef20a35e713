<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="techGrid" width="64" height="64" patternUnits="userSpaceOnUse">
      <rect width="64" height="64" fill="#141414"/>
      
      <!-- Grid lines -->
      <line x1="0" y1="16" x2="64" y2="16" stroke="#1f1f1f" stroke-width="1"/>
      <line x1="0" y1="32" x2="64" y2="32" stroke="#1f1f1f" stroke-width="1"/>
      <line x1="0" y1="48" x2="64" y2="48" stroke="#1f1f1f" stroke-width="1"/>
      <line x1="16" y1="0" x2="16" y2="64" stroke="#1f1f1f" stroke-width="1"/>
      <line x1="32" y1="0" x2="32" y2="64" stroke="#1f1f1f" stroke-width="1"/>
      <line x1="48" y1="0" x2="48" y2="64" stroke="#1f1f1f" stroke-width="1"/>
      
      <!-- Large center feature -->
      <rect x="28" y="28" width="8" height="8" fill="#252525"/>
      
      <!-- Small dots at intersections -->
      <circle cx="16" cy="16" r="1" fill="#252525"/>
      <circle cx="32" cy="16" r="1" fill="#252525"/>
      <circle cx="48" cy="16" r="1" fill="#252525"/>
      <circle cx="16" cy="32" r="1" fill="#252525"/>
      <circle cx="48" cy="32" r="1" fill="#252525"/>
      <circle cx="16" cy="48" r="1" fill="#252525"/>
      <circle cx="32" cy="48" r="1" fill="#252525"/>
      <circle cx="48" cy="48" r="1" fill="#252525"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#techGrid)"/>
</svg>