<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="hexFloor" width="100" height="86.6" patternUnits="userSpaceOnUse">
      <!-- Background -->
      <rect width="100" height="86.6" fill="#1a1d21"/>
      
      <!-- First row of hexagons -->
      <polygon points="0,21.65 25,0 75,0 100,21.65 75,43.3 25,43.3" fill="#252930" stroke="#1a1d21" stroke-width="1"/>
      
      <!-- Second row of hexagons (offset) -->
      <polygon points="50,21.65 75,43.3 75,86.6 50,86.6 25,65.15 25,43.3" fill="#252930" stroke="#1a1d21" stroke-width="1"/>
      <polygon points="50,86.6 75,86.6 100,65.15 100,43.3 75,43.3 50,64.95" fill="#252930" stroke="#1a1d21" stroke-width="1"/>
      
      <!-- Small center hexagons for detail -->
      <polygon points="40,21.65 50,14.43 60,21.65 60,36.08 50,43.3 40,36.08" fill="#1a1d21" stroke="#313740" stroke-width="1"/>
      <polygon points="15,54.13 25,46.91 35,54.13 35,68.56 25,75.78 15,68.56" fill="#1a1d21" stroke="#313740" stroke-width="1"/>
      <polygon points="65,54.13 75,46.91 85,54.13 85,68.56 75,75.78 65,68.56" fill="#1a1d21" stroke="#313740" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#hexFloor)"/>
</svg>