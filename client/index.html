<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SpecterShift Hunters</title>
  <script>
    // Temporary console log filtering for debugging
    (function() {
      const originalConsoleLog = console.log;
      const originalConsoleWarn = console.warn;
      const originalConsoleError = console.error;

      const messagesToFilter = [
        "WalletConnect Core is already initialized",
        "MaxListenersExceededWarning: Possible EventEmitter memory leak detected"
        // Add other specific repetitive messages if needed
      ];

      console.log = function(message, ...args) {
        if (typeof message === 'string' && messagesToFilter.some(filterMsg => message.includes(filterMsg))) {
          return; // Suppress
        }
        originalConsoleLog.apply(console, [message, ...args]);
      };

      console.warn = function(message, ...args) {
        if (typeof message === 'string' && messagesToFilter.some(filterMsg => message.includes(filterMsg))) {
          return; // Suppress
        }
        originalConsoleWarn.apply(console, [message, ...args]);
      };

      // You might want to be careful about filtering errors, but if Bedrock/WalletConnect also spams errors:
      // console.error = function(message, ...args) {
      //   if (typeof message === 'string' && messagesToFilter.some(filterMsg => message.includes(filterMsg))) {
      //     return; // Suppress
      //   }
      //   originalConsoleError.apply(console, [message, ...args]);
      // };

      originalConsoleLog('CONSOLE FILTERING ENABLED: Repetitive Bedrock/WalletConnect logs will be suppressed.');
    })();
  </script>
  <!-- Disable Vite HMR to prevent WebSocket conflicts -->
  <script>
    // Store HMR preference in localStorage to persist across page reloads
    localStorage.setItem('vite-hmr-disabled', 'true');

    // Disable HMR by intercepting WebSocket connections
    if (window.WebSocket) {
      // Store the original WebSocket constructor in a global variable
      // This ensures it's accessible to all scripts that need the native WebSocket
      window.__nativeWebSocket = window.WebSocket;
      const originalWebSocket = window.WebSocket;

      window.WebSocket = function(url, protocols) {
        // Only block Vite HMR connections
        if (url.includes('__vite_hmr')) {
          console.log('Blocked Vite HMR WebSocket connection:', url);
          return {
            url: url,
            readyState: 3, // CLOSED
            send: function() {},
            close: function() {},
            onopen: null,
            onclose: null,
            onmessage: null,
            onerror: null
          };
        }

        // CRITICAL FIX: Special handling for tournament WebSocket connections
        // Always allow tournament WebSocket connections with the native WebSocket
        if (url.includes('/tournament')) {
          console.log('Tournament WebSocket connection detected in index.html script, using native WebSocket:', url);
          // Use the original WebSocket constructor directly without any proxying
          return new window.__nativeWebSocket(url, protocols);
        }

        // For all other connections, use the original WebSocket
        return new originalWebSocket(url, protocols);
      };
      window.WebSocket.CONNECTING = originalWebSocket.CONNECTING;
      window.WebSocket.OPEN = originalWebSocket.OPEN;
      window.WebSocket.CLOSING = originalWebSocket.CLOSING;
      window.WebSocket.CLOSED = originalWebSocket.CLOSED;
      console.log('Vite HMR WebSocket connections disabled, but tournament connections allowed');
    }
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <!-- Preload critical third-party scripts -->
  <link rel="preconnect" href="https://public-cdn-files.pages.dev">
  <link rel="preload" href="https://public-cdn-files.pages.dev/bedrock-passport.umd.js" as="script">
  <!-- Optional: Include Tailwind CSS for styling the widget if not already globally available -->
  <!-- <script src="https://cdn.tailwindcss.com"></script> -->
  <style>
    body, html {
      margin: 0;
      padding: 0;
      overflow: hidden; /* This might be overridden by React components for scrollable pages like LoginPage */
      font-family: 'Roboto', sans-serif;
      /* Updated background to match LoginPage gradient */
      background: linear-gradient(to bottom, #4f0d73, #302b63, #000000);
      color: white; /* Default text color for initial state */
    }

    #game-container { /* This ID might not be used if #root is the primary for game */
      width: 100vw;
      height: 100vh;
      position: relative;
    }

    canvas {
      width: 100%;
      height: 100%;
      display: block;
    }

    .crosshair {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      transform: translate(-50%, -50%);
      pointer-events: none;
    }

    .title-font {
      font-family: 'Orbitron', sans-serif;
    }
    /* Basic styling for bedrock-login-widget container before React loads if needed */
    #bedrock-login-widget {
      /* Styles applied by React component are preferred, but this can be a fallback */
      /* background-color: #1a1a2e; */
      /* padding: 20px; */
      /* border-radius: 8px; */
    }
  </style>
</head>
<body>
  <div id="root">
    <!-- React app will mount here -->
  </div>

  <script type="module" src="/src/main.tsx"></script>
  <a target="_blank" href="https://jam.pieter.com" style="font-family: 'system-ui', sans-serif; position: fixed; bottom: -1px; right: -1px; padding: 7px; font-size: 14px; font-weight: bold; background: #fff; color: #000; text-decoration: none; z-index: 10; border-top-left-radius: 12px; z-index: 10000; border: 1px solid #fff;">🕹️ Vibe Jam 2025</a>
</body>
</html>
