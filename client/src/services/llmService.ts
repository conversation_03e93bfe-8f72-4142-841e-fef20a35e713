// Base API URL
const API_BASE_URL = '/api';

// Maximum number of polling attempts
const MAX_POLLING_ATTEMPTS = 30;
// Polling interval in milliseconds
const POLLING_INTERVAL_MS = 2000;

/**
 * Service for interacting with LLM APIs for prompt enhancement
 */
export class LLMService {
  /**
   * Enhance a user's pet description with LLM to create an optimal prompt for image generation
   */
  static async enhancePromptForPetGeneration(
    preferences: {
      specterType: string;
      description: string;
      color?: string;
      style?: string;
    }
  ): Promise<string> {
    // No try/catch - let errors propagate up
    const response = await fetch(`${API_BASE_URL}/ai/enhance-prompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(preferences),
    });

    // If the request is accepted but queued (status 202)
    if (response.status === 202) {
      const queueData = await response.json();
      console.log('Request queued:', queueData);

      // Poll for completion
      return this.pollForCompletion(queueData.requestId, 'prompt');
    }

    // Handle other non-OK responses
    if (!response.ok) {
      throw new Error(`Failed to enhance prompt: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.enhancedPrompt) {
      throw new Error('LLM service returned empty prompt');
    }

    console.log('LLM enhanced prompt:', result.enhancedPrompt);
    return result.enhancedPrompt;
  }



  /**
   * Analyze an NFT image to extract features for pet generation
   */
  static async analyzeNFTImage(
    nftImageUrl: string
  ): Promise<{
    recommendedType: string;
    colorPalette: string[];
    description: string;
    style: string;
  }> {
    // No try/catch - let errors propagate up
    const response = await fetch(`${API_BASE_URL}/ai/analyze-nft-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl: nftImageUrl }),
    });

    // If the request is accepted but queued (status 202)
    if (response.status === 202) {
      const queueData = await response.json();
      console.log('NFT analysis request queued:', queueData);

      // Poll for completion
      return this.pollForCompletion(queueData.requestId, 'analysis');
    }

    // Handle other non-OK responses
    if (!response.ok) {
      throw new Error(`Failed to analyze NFT image: ${response.statusText}`);
    }

    const result = await response.json();

    // Validate the result has all required fields
    if (!result.recommendedType || !result.colorPalette || !result.description || !result.style) {
      throw new Error('NFT analysis missing required fields');
    }

    console.log('NFT analysis result:', result);
    return result;
  }

  /**
   * Poll for request completion
   * @param requestId The request ID to poll for
   * @param type The type of request ('prompt' or 'analysis')
   * @returns The completed request result
   */
  private static async pollForCompletion(requestId: string, type: 'prompt' | 'analysis'): Promise<any> {
    let attempts = 0;

    while (attempts < MAX_POLLING_ATTEMPTS) {
      attempts++;

      try {
        // Get request status
        const statusResponse = await fetch(`${API_BASE_URL}/ai/request-status/${requestId}`);

        if (!statusResponse.ok) {
          console.error(`Failed to get request status: ${statusResponse.statusText}`);
          // Wait before trying again
          await new Promise(resolve => setTimeout(resolve, POLLING_INTERVAL_MS));
          continue;
        }

        const statusData = await statusResponse.json();
        console.log(`Request ${requestId} status:`, statusData);

        // If request is completed, get the result
        if (statusData.status === 'completed') {
          // For prompt enhancement
          if (type === 'prompt') {
            const resultResponse = await fetch(`${API_BASE_URL}/ai/enhance-prompt`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                specterType: 'WISP', // Dummy value, will use cached result
                description: 'Cached result', // Dummy value, will use cached result
                requestId // Include request ID to get cached result
              }),
            });

            if (!resultResponse.ok) {
              throw new Error(`Failed to get prompt result: ${resultResponse.statusText}`);
            }

            const data = await resultResponse.json();
            return data.enhancedPrompt;
          }

          // For NFT analysis
          if (type === 'analysis') {
            const resultResponse = await fetch(`${API_BASE_URL}/ai/analyze-nft-image`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                imageUrl: 'https://example.com/dummy.png', // Dummy value, will use cached result
                requestId // Include request ID to get cached result
              }),
            });

            if (!resultResponse.ok) {
              throw new Error(`Failed to get analysis result: ${resultResponse.statusText}`);
            }

            return await resultResponse.json();
          }
        }

        // If request failed
        if (statusData.status === 'failed') {
          throw new Error(`Request ${requestId} failed`);
        }

        // If still in queue or processing, wait and try again
        console.log(`Request ${requestId} is ${statusData.status}. Queue position: ${statusData.queuePosition}. Waiting...`);

        // Notify user of queue position
        if (statusData.queuePosition > 0) {
          console.log(`Your request is in position ${statusData.queuePosition} in the queue.`);
        }
      } catch (error) {
        console.error('Error polling for request completion:', error);
      }

      // Wait before trying again
      await new Promise(resolve => setTimeout(resolve, POLLING_INTERVAL_MS));
    }

    // If we've reached the maximum number of attempts, throw an error
    throw new Error(`Request ${requestId} timed out after ${MAX_POLLING_ATTEMPTS} polling attempts`);
  }
}