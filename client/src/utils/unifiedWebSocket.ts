/**
 * Unified WebSocket utility for all WebSocket connections in the application
 * This handles both game WebSockets and tournament battle WebSockets
 */

// Capture the native WebSocket constructor immediately when the module loads
// This is crucial to avoid capturing a proxied version if other scripts modify window.WebSocket
const NativeWebSocket = window.WebSocket;

// Connection tracking
let activeConnections: Map<string, WebSocket> = new Map();
let messageHandlers: Map<string, Set<(message: any) => void>> = new Map();
let connectionAttempts: Map<string, number> = new Map();
let heartbeatIntervals: Map<string, number> = new Map();
let connectionMetadata: Map<WebSocket, { url: string; connectionId: string; createdAt: number }> = new Map();

// Constants
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 2000; // 2 seconds
const HEARTBEAT_INTERVAL = 15000; // 15 seconds for ping-pong heartbeat
const HEARTBEAT_TIMEOUT = 10000; // 10 seconds timeout for pong response

/**
 * Create a WebSocket connection
 * @param url The WebSocket URL to connect to
 * @param connectionId A unique identifier for this connection
 * @param protocols Optional WebSocket protocols
 * @returns A WebSocket connection
 */
export function createWebSocket(
  url: string, 
  connectionId: string,
  protocols?: string | string[]
): WebSocket {
  console.log(`Creating WebSocket connection to ${url} with ID ${connectionId}`);
  
  // Check if a connection with this ID already exists
  if (activeConnections.has(connectionId)) {
    console.log(`WebSocket connection with ID ${connectionId} already exists, returning existing connection`);
    return activeConnections.get(connectionId)!;
  }
  
  // Create a new WebSocket connection using the native WebSocket constructor
  let ws: WebSocket;
  if (protocols !== undefined) {
    ws = new NativeWebSocket(url, protocols);
  } else {
    ws = new NativeWebSocket(url);
  }
  
  // Add custom properties to help with tracking
  (ws as any).connectionId = connectionId;
  (ws as any).lastHeartbeatTime = Date.now();
  (ws as any).lastPongTime = Date.now();
  (ws as any).heartbeatTimeout = null;
  (ws as any).createdAt = Date.now(); // Store creation time
  
  console.log(`WebSocket Client: Storing connection ${connectionId} with URL ${url} and creation time ${(ws as any).createdAt}`);
  // Store the connection
  activeConnections.set(connectionId, ws);
  
  // Initialize message handlers set for this connection
  if (!messageHandlers.has(connectionId)) {
    messageHandlers.set(connectionId, new Set());
  }

  // Store URL in metadata
  const metadata = { url, connectionId, createdAt: (ws as any).createdAt };
  connectionMetadata.set(ws, metadata);
  
  // Set up event handlers
  setupWebSocketEventHandlers(ws, url, connectionId, protocols);
  
  return ws;
}

/**
 * Set up event handlers for a WebSocket connection
 * @param ws The WebSocket connection
 * @param url The WebSocket URL
 * @param connectionId The connection ID
 * @param protocols Optional WebSocket protocols
 */
function setupWebSocketEventHandlers(ws: WebSocket, url: string, connectionId: string, protocols?: string | string[]): void {
  // Handle connection open
  ws.addEventListener('open', () => {
    console.log(`WebSocket Client ${connectionId}: Connection opened to ${url}`);
    
    // Reset connection attempts on successful connection
    connectionAttempts.set(connectionId, 0);
    
    // Initialize WebSocket state
    (ws as any).isAlive = true;
    (ws as any).lastPongTime = Date.now();
    
    // Send an immediate ping to verify the connection is working
    sendPing(ws);
    
    // Set up aggressive heartbeat to prevent abnormal closures
    const heartbeatInterval = window.setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        // Check if we've received a pong response since our last ping
        const timeSinceLastPong = Date.now() - (ws as any).lastPongTime;
        
        if (timeSinceLastPong > HEARTBEAT_TIMEOUT) {
          console.warn(`WebSocket Client ${connectionId}: Heartbeat timeout (${timeSinceLastPong}ms since last pong)`);
          
          // If using tournament battleId connection, add the battleId as a query param to help track issues
          if (url.includes('/tournament') && url.includes('battleId=')) {
            const battleId = new URL(url).searchParams.get('battleId');
            console.warn(`Tournament battle connection timeout for battleId: ${battleId}`);
          }
          
          // Don't close the connection; keep trying to send pings
          // The server might eventually respond
          console.warn(`WebSocket Client ${connectionId}: Continuing ping attempts despite timeout.`);
        }
        
        // Send a ping regardless
        (ws as any).lastHeartbeatTime = Date.now();
        sendPing(ws);
      } else if (ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING) {
        // Clear the heartbeat interval if the connection is closing or closed
        clearInterval(heartbeatInterval);
        heartbeatIntervals.delete(connectionId);
      }
    }, HEARTBEAT_INTERVAL);
    
    // Store the interval ID for cleanup
    heartbeatIntervals.set(connectionId, heartbeatInterval as unknown as number);
  });
  
  // Handle messages
  ws.addEventListener('message', (event) => {
    try {
      // Update last activity time on any message
      (ws as any).lastActivityTime = Date.now();
      
      // Parse the message
      const message = JSON.parse(event.data);
      
      // Check for pong responses
      if (message.type === 'pong') {
        (ws as any).isAlive = true;
        (ws as any).lastPongTime = Date.now();
        console.log(`WebSocket Client ${connectionId}: Received pong response, connection is alive`);
        // Don't proceed further for pong messages
        return;
      }
      
      // Log the message (except pings/pongs to reduce noise)
      if (message.type !== 'ping') {
        console.log(`WebSocket Client ${connectionId}: Received message:`, message);
      }
      
      // Call all registered message handlers for this connection
      const handlers = messageHandlers.get(connectionId);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message);
          } catch (error) {
            console.error(`Error in WebSocket message handler for connection ${connectionId}:`, error);
          }
        });
      }
    } catch (error) {
      console.error(`Error parsing WebSocket message for connection ${connectionId}:`, error);
    }
  });
  
  // Handle connection close
  ws.addEventListener('close', (event) => {
    console.log(`WebSocket Client ${connectionId}: Closed with code ${event.code}, reason: ${event.reason || 'No reason provided'}`);
    
    // Clean up resources
    const heartbeatInterval = heartbeatIntervals.get(connectionId);
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatIntervals.delete(connectionId);
    }
    
    // Remove from active connections
    activeConnections.delete(connectionId);
    
    // For Tournament connections, report additional diagnostics for abnormal closures
    const metadata = connectionMetadata.get(ws);
    const connectionUrl = metadata?.url || (ws as any).url || 'unknown'; // Get URL from metadata or fallback
    if (connectionUrl.includes('/tournament') && event.code === 1006) {
      console.error(`Tournament WebSocket abnormal closure (1006) details:
        - URL: ${connectionUrl}
        - Connection ID: ${connectionId}
        - Time since creation: ${Date.now() - ((ws as any).createdAt || Date.now())}ms
        - Last ping sent: ${Date.now() - ((ws as any).lastHeartbeatTime || Date.now())}ms ago
        - Last pong received: ${Date.now() - ((ws as any).lastPongTime || Date.now())}ms ago
      `);
    }
  });
  
  // Handle errors
  ws.addEventListener('error', (error) => {
    console.error(`WebSocket Client ${connectionId}: Error occurred -`, error);
    console.error(`WebSocket Client ${connectionId}: Current readyState: ${ws.readyState}`);

    // For Tournament connections, report additional diagnostics
    const metadata = connectionMetadata.get(ws);
    const connectionUrl = metadata?.url || (ws as any).url || 'unknown'; // Get URL from metadata or fallback
    if (connectionUrl.includes('/tournament')) {
      console.error(`Tournament WebSocket error details:
        - URL: ${connectionUrl}
        - Connection ID: ${connectionId}
        - ReadyState: ${ws.readyState}
        - Time since creation: ${Date.now() - ((ws as any).createdAt || Date.now())}ms
      `);
    }
  });
}

/**
 * Send a ping message to keep the connection alive
 * @param ws The WebSocket connection
 */
function sendPing(ws: WebSocket): void {
  if (ws.readyState === WebSocket.OPEN) {
    const pingMessage = {
      type: 'ping',
      data: {
        clientTime: Date.now()
      },
      timestamp: Date.now(),
      sender: 'client'
    };
    
    try {
      ws.send(JSON.stringify(pingMessage));
    } catch (error) {
      console.error(`Error sending ping:`, error);
    }
  }
}

/**
 * Register a message handler for a WebSocket connection
 * @param connectionId The connection ID
 * @param handler The message handler function
 */
export function addMessageHandler(connectionId: string, handler: (message: any) => void): void {
  // Get or create the handlers set for this connection
  if (!messageHandlers.has(connectionId)) {
    messageHandlers.set(connectionId, new Set());
  }
  
  // Add the handler
  messageHandlers.get(connectionId)!.add(handler);
}

/**
 * Remove a message handler for a WebSocket connection
 * @param connectionId The connection ID
 * @param handler The message handler function to remove
 */
export function removeMessageHandler(connectionId: string, handler: (message: any) => void): void {
  // Get the handlers set for this connection
  const handlers = messageHandlers.get(connectionId);
  
  // Remove the handler if it exists
  if (handlers) {
    handlers.delete(handler);
  }
}

/**
 * Send a message through a WebSocket connection
 * @param connectionId The connection ID
 * @param type The message type
 * @param data The message data
 * @returns True if the message was sent, false otherwise
 */
export function sendMessage(connectionId: string, type: string, data: any): boolean {
  // Get the WebSocket connection
  const ws = activeConnections.get(connectionId);
  
  if (ws && ws.readyState === WebSocket.OPEN) {
    const message = {
      type,
      data,
      timestamp: Date.now(),
      sender: 'client'
    };
    
    console.log(`Sending WebSocket message on connection ${connectionId}:`, message);
    try {
      ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error(`Error sending WebSocket message:`, error);
      return false;
    }
  } else {
    console.error(`Cannot send WebSocket message, connection ${connectionId} not open (readyState: ${ws?.readyState})`);
    return false;
  }
}

/**
 * Close a WebSocket connection
 * @param connectionId The connection ID
 * @param code The close code
 * @param reason The close reason
 */
export function closeConnection(connectionId: string, code: number = 1000, reason: string = 'Normal closure'): void {
  // Get the WebSocket connection
  const ws = activeConnections.get(connectionId);
  
  if (ws) {
    console.log(`Closing WebSocket connection ${connectionId} with code ${code}, reason: ${reason}`);
    
    // Clean up heartbeat interval
    const heartbeatInterval = heartbeatIntervals.get(connectionId);
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatIntervals.delete(connectionId);
    }
    
    // Remove from active connections
    activeConnections.delete(connectionId);
    
    // Remove message handlers
    messageHandlers.delete(connectionId);
    
    // Close the connection
    try {
      ws.close(code, reason);
    } catch (error) {
      console.error(`Error closing WebSocket connection:`, error);
    }
  }
}

/**
 * Create a tournament battle WebSocket connection
 * @param battleId The battle ID
 * @param token The user token
 * @returns The connection ID
 */
export function createTournamentBattleConnection(battleId: string, token: string): string {
  // Generate a connection ID
  const connectionId = `tournament-${battleId}-${token}`;

  // Create the WebSocket URL relative to the current origin
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  // Use window.location.host which includes hostname and port (if non-default)
  const host = window.location.host; 
  const url = `${protocol}//${host}/tournament?token=${token}&battleId=${battleId}`;

  console.log(`Creating WebSocket connection with URL: ${url}`); // Log the generated URL

  // Create the WebSocket connection
  createWebSocket(url, connectionId);

  return connectionId;
}

/**
 * Join a tournament battle
 * @param connectionId The connection ID
 * @param battleId The battle ID
 * @param userId The user ID
 */
export function joinTournamentBattle(connectionId: string, battleId: string, userId: string): void {
  sendMessage(connectionId, 'tournament_battle_join', {
    battleId,
    userId,
    isSpectator: true,
    clientTimestamp: Date.now()
  });
}

/**
 * Leave a tournament battle
 * @param connectionId The connection ID
 * @param battleId The battle ID
 * @param userId The user ID
 */
export function leaveTournamentBattle(connectionId: string, battleId: string, userId: string): void {
  sendMessage(connectionId, 'tournament_battle_leave', {
    battleId,
    userId
  });
  
  // Close the connection
  closeConnection(connectionId);
}

/**
 * Update spectator position in a tournament battle
 * @param connectionId The connection ID
 * @param battleId The battle ID
 * @param position The spectator position
 */
export function updateSpectatorPosition(connectionId: string, battleId: string, position: any): void {
  sendMessage(connectionId, 'spectator_position_update', {
    battleId,
    position
  });
}

/**
 * Get all active connection IDs
 * @returns Array of connection IDs
 */
export function getActiveConnectionIds(): string[] {
  return Array.from(activeConnections.keys());
}

/**
 * Check if a connection is active
 * @param connectionId The connection ID
 * @returns True if the connection is active, false otherwise
 */
export function isConnectionActive(connectionId: string): boolean {
  const ws = activeConnections.get(connectionId);
  return ws !== undefined && ws.readyState === WebSocket.OPEN;
}

// Export the native WebSocket constructor for use in other modules
export { NativeWebSocket };
