/**
 * Unified WebSocket utility for all types of connections
 * This utility provides direct WebSocket connections that bypass all proxying in the application
 */

import { createWebSocket, sendMessage } from './unifiedWebSocket';

// Use the native WebSocket constructor stored in index.html
// This ensures we always have access to the original WebSocket constructor
// even if the WebSocket has been proxied or modified
const NativeWebSocket = (window as any).__nativeWebSocket || window.WebSocket;

/**
 * Connection types for different WebSocket connections
 */
export enum WebSocketConnectionType {
  GAME = 'game',
  TOURNAMENT = 'tournament',
  DIRECT = 'direct'
}

/**
 * Create a WebSocket connection
 * @param url The WebSocket URL to connect to
 * @param type The type of connection (game, tournament, or direct)
 * @param protocols Optional WebSocket protocols
 * @returns A WebSocket connection
 */
export function createWebSocketConnection(
  url: string,
  type: WebSocketConnectionType = WebSocketConnectionType.DIRECT,
  protocols?: string | string[]
): WebSocket {
  // Log connection attempt with type
  console.log(`Creating ${type} WebSocket connection to ${url}`);

  // For tournament connections, ensure the URL includes the tournament path
  if (type === WebSocketConnectionType.TOURNAMENT && !url.includes('/tournament')) {
    console.warn('Tournament WebSocket URL does not include /tournament path');
  }

  try {
    // Generate a connection ID based on the type and URL
    const connectionId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Use the unified WebSocket implementation
    const socket = createWebSocket(url, connectionId, protocols);
    
    // Store the connection ID on the socket for easier access
    (socket as any).connectionId = connectionId;
    
    return socket;
  } catch (error) {
    console.error(`Error creating WebSocket connection to ${url}:`, error);
    
    // We no longer create a dummy socket - instead throw the error to be handled by the caller
    // This ensures we don't mask connection issues with fake sockets
    throw new Error(`Failed to create WebSocket connection to ${url}: ${error}`);
  }
}

/**
 * Send a message through a WebSocket connection
 * @param ws The WebSocket connection
 * @param type The message type
 * @param data The message data
 * @param sender The message sender
 * @returns True if the message was sent, false otherwise
 */
export function sendWebSocketMessage(
  ws: WebSocket,
  type: string,
  data: any,
  sender: string
): boolean {
  // Extract the connectionId from the WebSocket object if available
  const connectionId = (ws as any).connectionId;

  if (connectionId) {
    // Use the unified WebSocket implementation if connectionId is available
    return sendMessage(connectionId, type, data);
  } else if (ws.readyState === WebSocket.OPEN) {
    // Fall back to direct WebSocket usage if no connectionId is available
    const message = {
      type,
      data,
      timestamp: Date.now(),
      sender
    };

    console.log(`Sending WebSocket message of type ${type}`);
    ws.send(JSON.stringify(message));
    return true;
  } else {
    console.error(`Cannot send WebSocket message, connection not open (readyState: ${ws.readyState})`);
    return false;
  }
}

/**
 * Get the WebSocket URL for a specific connection type
 * @param type The type of connection
 * @param battleId Optional battle ID for tournament connections
 * @returns The WebSocket URL
 */
export function getWebSocketUrl(type: WebSocketConnectionType, battleId?: string): string {
  const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';

  // Get the hostname from the current URL
  // In production, use the hostname as is; in development, use localhost/127.0.0.1
  let host = window.location.hostname;
  
  // Only override with 127.0.0.1 in local development
  if (host === 'localhost') {
    host = '127.0.0.1';
  }

  // Use port 5001 for all WebSocket connections
  const port = '5001';
  const userId = localStorage.getItem('walletAddress') || 'anonymous';

  // Log the host and port being used
  console.log(`Using WebSocket host: ${host} and port: ${port}`);

  let url = '';
  switch (type) {
    case WebSocketConnectionType.GAME:
      url = `${protocol}://${host}:${port}/game-ws?token=${encodeURIComponent(userId)}`;
      break;

    case WebSocketConnectionType.TOURNAMENT:
      url = battleId
        ? `${protocol}://${host}:${port}/tournament?token=${encodeURIComponent(userId)}&battleId=${battleId}`
        : `${protocol}://${host}:${port}/tournament?token=${encodeURIComponent(userId)}`;
      break;

    case WebSocketConnectionType.DIRECT:
    default:
      url = `${protocol}://${host}:${port}?token=${encodeURIComponent(userId)}`;
      break;
  }

  console.log(`Generated WebSocket URL: ${url}`);
  return url;
}

// Export the native WebSocket constructor for use in other modules
export { NativeWebSocket };
