/**
 * This utility disables Vite's HMR WebSocket connections to prevent conflicts with our game WebSockets
 * It also ensures that tournament WebSocket connections are always allowed
 */

import { NativeWebSocket } from './unifiedWebSocket';

export function disableHmrWebSocket() {
  // Check if we're in development mode
  if (import.meta.env.DEV) {
    console.log('Development mode detected, disabling Vite HMR WebSocket connections');

    // Store the original WebSocket constructor
    const originalWebSocket = window.WebSocket;

    // Create a proxy WebSocket constructor that intercepts HMR connections
    const WebSocketProxy = function(this: any, url: string, protocols?: string | string[]) {
      // Check if this is a Vite HMR WebSocket connection
      if (url.includes('/__vite_hmr')) {
        console.log('Intercepted Vite HMR WebSocket connection attempt:', url);

        // Create a mock WebSocket that doesn't actually connect
        this.url = url;
        this.readyState = 3; // CLOSED
        this.protocol = '';
        this.extensions = '';

        // Mock methods
        this.close = () => {};
        this.send = () => {};

        // Set up event handlers
        this.onopen = null;
        this.onclose = null;
        this.onmessage = null;
        this.onerror = null;

        // Simulate a connection failure after a short delay
        setTimeout(() => {
          if (this.onerror) {
            this.onerror(new Event('error'));
          }
          if (this.onclose) {
            this.onclose(new CloseEvent('close'));
          }
        }, 100);

        return this;
      }

      // CRITICAL FIX: Always allow tournament WebSocket connections
      // This ensures that tournament WebSocket connections are never blocked or proxied
      if (url.includes('/tournament')) {
        console.log('Tournament WebSocket connection detected in disableHmr.ts, using direct WebSocket:', url);
        // Use the native WebSocket constructor directly without any proxying
        // This ensures we're using the most direct and reliable WebSocket implementation
        return new NativeWebSocket(url, protocols);
      }

      // For all other non-HMR connections, use the original WebSocket
      return new originalWebSocket(url, protocols);
    };

    // Copy static properties from the original WebSocket
    WebSocketProxy.CONNECTING = originalWebSocket.CONNECTING;
    WebSocketProxy.OPEN = originalWebSocket.OPEN;
    WebSocketProxy.CLOSING = originalWebSocket.CLOSING;
    WebSocketProxy.CLOSED = originalWebSocket.CLOSED;

    // Override the WebSocket constructor
    // @ts-ignore - We need to override the constructor
    window.WebSocket = WebSocketProxy as any;

    console.log('Vite HMR WebSocket connections disabled, tournament connections allowed');
  }
}
