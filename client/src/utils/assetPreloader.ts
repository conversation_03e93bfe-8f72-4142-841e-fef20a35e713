/**
 * Utility for preloading critical game assets
 */

import * as THREE from 'three';

// Singleton texture loader
const textureLoader = new THREE.TextureLoader();

// List of critical textures to preload
const criticalTextures = [
  // Level textures (using SVG files that actually exist)
  '/assets/textures/level/floor1.svg',
  '/assets/textures/level/wall1.svg',
  '/assets/textures/level/ceiling1.svg',
  // Weapon effect textures
  '/assets/textures/gravity_effect.png',
  '/assets/textures/phase_effect.png',
  '/assets/textures/time_effect.png',
  // Enemy textures
  '/assets/textures/specter_sprite.png',
  '/assets/textures/orange.svg', // Orange enemy texture
];

/**
 * Preloads critical game assets in the background
 * This helps reduce visible loading when transitioning to the game
 */
export const preloadCriticalAssets = (): Promise<void> => {
  return new Promise((resolve) => {
    let loadedCount = 0;
    const totalAssets = criticalTextures.length;

    // Function to check if all assets are loaded
    const checkAllLoaded = () => {
      loadedCount++;
      if (loadedCount >= totalAssets) {
        console.log('All critical assets preloaded');
        resolve();
      }
    };

    // Preload all textures
    criticalTextures.forEach(texturePath => {
      textureLoader.load(
        texturePath,
        () => {
          console.log(`Preloaded texture: ${texturePath}`);
          checkAllLoaded();
        },
        undefined, // onProgress is not supported by TextureLoader
        (error) => {
          console.warn(`Failed to preload texture: ${texturePath}`, error);
          checkAllLoaded(); // Still count as "loaded" to avoid blocking
        }
      );
    });

    // If no assets to load, resolve immediately
    if (totalAssets === 0) {
      resolve();
    }
  });
};

// Export the loader for direct access if needed
export { textureLoader };
