import React, { useEffect, useState, Suspense, lazy } from "react";
import { Switch, Route, useLocation, Redirect } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import { Web3Provider } from "@/contexts/Web3Context";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { PvpAccessProvider } from "@/contexts/PvpAccessContext";
import { NativeWebSocket } from "@/utils/webSocketUtils";
import MinimalLoadingIndicator from "@/components/MinimalLoadingIndicator";

// Pre-load the Login component to avoid delay
import Login from "@/pages/Login";

// Lazy load other pages to reduce initial bundle size
const Home = lazy(() => import("@/pages/Home"));
const Game = lazy(() => import("@/pages/Game"));
const NotFound = lazy(() => import("@/pages/not-found"));
const Sponsors = lazy(() => import("@/pages/Sponsors"));
const PVP = lazy(() => import("@/pages/pvp"));
const AuthCallback = lazy(() => import("@/pages/AuthCallback"));

// Routing component that handles auth redirection
const AuthAwareRoute = () => {
  const { isLoggedIn, isAuthLoading } = useAuth();
  const [, setLocation] = useLocation();
  const [checkedLocalStorage, setCheckedLocalStorage] = useState(false);

  useEffect(() => {
    // Helper function to check if session is expired
    const isSessionExpired = (): boolean => {
      const lastLoginTimestamp = localStorage.getItem('lastLoginTimestamp');
      if (!lastLoginTimestamp) return false;

      const loginTime = parseInt(lastLoginTimestamp, 10);
      const currentTime = Date.now();
      const SESSION_TIMEOUT_MS = 12 * 60 * 60 * 1000; // 12 hours

      return (currentTime - loginTime) > SESSION_TIMEOUT_MS;
    };

    // Helper function to validate stored Orange ID user
    const isValidOrangeIDUser = (userString: string): boolean => {
      try {
        const parsedUser = JSON.parse(userString);
        return parsedUser && parsedUser.id && (parsedUser.email || parsedUser.displayName);
      } catch {
        return false;
      }
    };

    // Check localStorage directly to see if we have valid stored auth
    const storedUser = localStorage.getItem('orangeIDUser');
    const walletAddress = localStorage.getItem('walletAddress');

    // Check if session is expired first
    if (isSessionExpired()) {
      console.log('AuthAwareRoute: Session expired, clearing stored auth');
      // Clear expired auth data
      localStorage.removeItem('orangeIDUser');
      localStorage.removeItem('orangeIDToken');
      localStorage.removeItem('orangeIDRefreshToken');
      localStorage.removeItem('lastLoginTimestamp');
      localStorage.removeItem('walletAddress');
      localStorage.removeItem('walletUser');
      setCheckedLocalStorage(true);
      return;
    }

    // Check for valid stored auth
    const hasValidOrangeID = storedUser &&
                            storedUser !== 'undefined' &&
                            storedUser !== 'null' &&
                            isValidOrangeIDUser(storedUser);
    const hasValidWallet = walletAddress && walletAddress !== 'undefined' && walletAddress !== 'null';

    if (hasValidOrangeID || hasValidWallet) {
      console.log('AuthAwareRoute: Found valid user in localStorage, redirecting to /home');
      setLocation('/home');
      setCheckedLocalStorage(true);
      return;
    }

    setCheckedLocalStorage(true);

    // As a fallback, also check the auth context state
    if (!isAuthLoading && isLoggedIn) {
      console.log('AuthAwareRoute: User is logged in via auth context, redirecting to /home');
      setLocation('/home');
    }
  }, [isLoggedIn, isAuthLoading, setLocation]);

  // Show loading indicator while checking auth
  if (isAuthLoading && !checkedLocalStorage) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900">
        <MinimalLoadingIndicator size="large" fixed={false} />
        <p className="text-white mt-4">Checking authentication...</p>
      </div>
    );
  }

  // If not logged in, show the login page
  return <Login />;
};

function App() {
  // Initialize with false for no loading delay
  const [isLoading, setIsLoading] = useState(false);
  const [loadingFailed, setLoadingFailed] = useState(false);

  // Check for authentication when app loads
  useEffect(() => {
    const checkStoredAuth = () => {
      // Helper function to check if session is expired
      const isSessionExpired = (): boolean => {
        const lastLoginTimestamp = localStorage.getItem('lastLoginTimestamp');
        if (!lastLoginTimestamp) return false;

        const loginTime = parseInt(lastLoginTimestamp, 10);
        const currentTime = Date.now();
        const SESSION_TIMEOUT_MS = 12 * 60 * 60 * 1000; // 12 hours

        return (currentTime - loginTime) > SESSION_TIMEOUT_MS;
      };

      // Helper function to validate stored Orange ID user
      const isValidOrangeIDUser = (userString: string): boolean => {
        try {
          const parsedUser = JSON.parse(userString);
          return parsedUser && parsedUser.id && (parsedUser.email || parsedUser.displayName);
        } catch {
          return false;
        }
      };

      // Check if session is expired first
      if (isSessionExpired()) {
        console.log('App: Session expired, clearing stored auth');
        localStorage.removeItem('orangeIDUser');
        localStorage.removeItem('orangeIDToken');
        localStorage.removeItem('orangeIDRefreshToken');
        localStorage.removeItem('lastLoginTimestamp');
        localStorage.removeItem('walletAddress');
        localStorage.removeItem('walletUser');
        return;
      }

      // Check both auth methods
      const storedUser = localStorage.getItem('orangeIDUser');
      const walletAddress = localStorage.getItem('walletAddress');

      // Only consider valid user data
      const hasValidOrangeUser = storedUser &&
                                storedUser !== 'undefined' &&
                                storedUser !== 'null' &&
                                isValidOrangeIDUser(storedUser);
      const hasValidWallet = walletAddress && walletAddress !== 'undefined' && walletAddress !== 'null';

      if (hasValidOrangeUser || hasValidWallet) {
        // If we have a stored user, manually trigger an auth event
        console.log('App: Found valid stored auth, triggering auth event');
        try {
          // Dispatch the event to notify AuthContext
          window.dispatchEvent(new Event('auth-login-success'));

          // If we're on the login page, redirect to home
          if (window.location.pathname === '/' || window.location.pathname === '') {
            console.log('App: User has stored auth and is on login page, redirecting to /home');
            setTimeout(() => {
              window.location.href = '/home';
            }, 100);
          }
        } catch (e) {
          console.error('App: Failed to dispatch auth event or redirect', e);
        }
      }
    };

    // Short delay to ensure other components are ready
    setTimeout(checkStoredAuth, 300);
  }, []);

  // Removed the artificial delay - no need to wait before routing

  // Catch global errors during initialization
  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error("Global error caught:", error);
      // Don't let errors prevent the app from loading
      if (isLoading) {
        setIsLoading(false);
        setLoadingFailed(true);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [isLoading]);

  // Prevent WebSocket connections on the home page, but allow them on other pages
  useEffect(() => {
    // Function to check if we're on the home page
    const checkIfHomePage = () => {
      // Check if we're on the PVP page - always allow WebSocket connections on PVP page
      if (window.location.pathname.includes('/pvp')) {
        console.log('PVP page detected - allowing all WebSocket connections');
        return false;
      }

      // Otherwise, check if we're on the home page
      return window.location.pathname === '/' || window.location.pathname === '';
    };

    // Initial check
    let isHomePage = checkIfHomePage();
    let originalWebSocket: typeof WebSocket | null = null;

    // Function to apply or remove WebSocket prevention
    const updateWebSocketPrevention = () => {
      // Check current page
      const currentlyOnHomePage = checkIfHomePage();

      // If we're on the home page and prevention isn't active, apply it
      if (currentlyOnHomePage && !originalWebSocket) {
        console.log('Home page detected - preventing WebSocket connections');

        // Store the original WebSocket constructor
        originalWebSocket = window.WebSocket;

        // Create a mock WebSocket that doesn't actually connect
        const MockWebSocket = function(this: any, url: string) {
          // CRITICAL FIX: Special handling for tournament WebSocket connections - ALWAYS allow these
          if (url.includes('/tournament')) {
            console.log('Tournament WebSocket connection detected in App.tsx, using native WebSocket:', url);
            // Use the native WebSocket directly without any proxying
            // This ensures we're using the most direct and reliable WebSocket implementation
            return new NativeWebSocket(url);
          }

          // console.warn(`WebSocket connection prevented on home page: ${url}`);

          // Set up a mock WebSocket object
          this.url = url;
          this.readyState = 3; // CLOSED
          this.protocol = '';

          // Mock methods
          this.send = () => {};
          this.close = () => {};

          // Set up event handlers
          this.onopen = null;
          this.onclose = null;
          this.onmessage = null;
          this.onerror = null;

          // Simulate a connection failure after a short delay
          setTimeout(() => {
            if (this.onerror) {
              this.onerror(new Event('error'));
            }
            if (this.onclose) {
              this.onclose(new CloseEvent('close', { code: 1006, reason: 'Connection prevented on home page', wasClean: false }));
            }
          }, 100);

          // Event listener methods
          this.addEventListener = (type: string, listener: EventListener) => {
            if (type === 'error') this.onerror = listener;
            if (type === 'close') this.onclose = listener;
            if (type === 'open') this.onopen = listener;
            if (type === 'message') this.onmessage = listener;
          };

          this.removeEventListener = () => {};
        };

        // Copy static properties from the original WebSocket
        MockWebSocket.CONNECTING = originalWebSocket.CONNECTING;
        MockWebSocket.OPEN = originalWebSocket.OPEN;
        MockWebSocket.CLOSING = originalWebSocket.CLOSING;
        MockWebSocket.CLOSED = originalWebSocket.CLOSED;

        // Override the WebSocket constructor
        // @ts-ignore - We need to override the constructor
        window.WebSocket = MockWebSocket as any;
      }
      // If we're not on the home page and prevention is active, remove it
      else if (!currentlyOnHomePage && originalWebSocket) {
        console.log('Not on home page - restoring WebSocket connections');
        // Restore the original WebSocket
        window.WebSocket = originalWebSocket;
        originalWebSocket = null;
      }

      // Update the state
      isHomePage = currentlyOnHomePage;
    };

    // Apply initial prevention if needed
    updateWebSocketPrevention();

    // Listen for navigation events
    const handleNavigation = () => {
      updateWebSocketPrevention();
    };

    // Add event listeners for navigation
    window.addEventListener('popstate', handleNavigation);

    // Check for navigation periodically (for client-side routing)
    const intervalId = setInterval(handleNavigation, 500);

    return () => {
      // Clean up
      window.removeEventListener('popstate', handleNavigation);
      clearInterval(intervalId);

      // Restore the original WebSocket when the component unmounts
      if (originalWebSocket) {
        window.WebSocket = originalWebSocket;
      }
    };
  }, []);

  // If loading failed but we're continuing anyway, log it but still render the app
  if (loadingFailed) {
    console.warn("Application loaded with errors, some features may not work correctly");
  }

  // If app is still in initial loading state, show a loading indicator
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900">
        <div className="bg-black/50 p-8 rounded-lg text-center">
          <MinimalLoadingIndicator size="large" fixed={false} />
          <p className="text-white mt-4">Loading SpecterShift...</p>
        </div>
      </div>
    );
  }

  return (
    <Web3Provider>
      <AuthProvider>
        <PvpAccessProvider>
          <Suspense fallback={<MinimalLoadingIndicator />}>
            <Switch>
              {/* Public routes with auth-aware redirection */}
              <Route path="/" component={AuthAwareRoute} />
              <Route path="/auth/callback" component={AuthCallback} />

              {/* Game routes */}
              <Route path="/home" component={Home} />
              <Route path="/game" component={Game} />
              <Route path="/pvp" component={PVP} />
              <Route path="/sponsors" component={Sponsors} />
              <Route component={NotFound} />
            </Switch>
          </Suspense>
          <Toaster />
        </PvpAccessProvider>
      </AuthProvider>
    </Web3Provider>
  );
}

export default App;