import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import MobileControls from '../../../game/controls/MobileControls';
import * as THREE from 'three';
import nipplejs from 'nipplejs';

// Mock nipplejs
jest.mock('nipplejs', () => ({
  create: jest.fn().mockReturnValue({
    on: jest.fn(),
    destroy: jest.fn(),
  }),
}));

describe('MobileControls', () => {
  // Mock callback functions
  const mockOnMove = jest.fn();
  const mockOnLook = jest.fn();
  const mockOnJump = jest.fn();
  const mockOnShoot = jest.fn();
  const mockOnSwitchWeapon = jest.fn();
  const mockOnGrapple = jest.fn();
  const mockOnJetpack = jest.fn();
  const mockOnInteract = jest.fn();
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });
  
  test('should render correctly', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Check that joystick containers are rendered
    expect(document.querySelector('.left-joystick')).toBeInTheDocument();
    expect(document.querySelector('.right-joystick')).toBeInTheDocument();
    
    // Check that action buttons are rendered
    expect(screen.getByText('JUMP')).toBeInTheDocument();
    expect(screen.getByText('SHOOT')).toBeInTheDocument();
    expect(screen.getByText('GRAPPLE')).toBeInTheDocument();
  });
  
  test('should initialize joysticks', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Should create two joysticks
    expect(nipplejs.create).toHaveBeenCalledTimes(2);
    
    // Check left joystick configuration
    expect(nipplejs.create).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: 'static',
        position: expect.objectContaining({ left: '100px', bottom: '100px' }),
      })
    );
    
    // Check right joystick configuration
    expect(nipplejs.create).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: 'static',
        position: expect.objectContaining({ right: '100px', bottom: '100px' }),
      })
    );
  });
  
  test('should call onJump when jump button is clicked', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Click jump button
    fireEvent.click(screen.getByText('JUMP'));
    
    // Should call onJump
    expect(mockOnJump).toHaveBeenCalledTimes(1);
  });
  
  test('should call onShoot when shoot button is touched', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Touch shoot button
    fireEvent.touchStart(screen.getByText('SHOOT'));
    
    // Should call onShoot
    expect(mockOnShoot).toHaveBeenCalledTimes(1);
  });
  
  test('should call onGrapple when grapple button is clicked', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Click grapple button
    fireEvent.click(screen.getByText('GRAPPLE'));
    
    // Should call onGrapple
    expect(mockOnGrapple).toHaveBeenCalledTimes(1);
  });
  
  test('should toggle jetpack state when jetpack button is clicked', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Find jetpack button
    const jetpackButton = screen.getByText('JETPACK');
    
    // Click jetpack button to activate
    fireEvent.click(jetpackButton);
    
    // Should call onJetpack with true
    expect(mockOnJetpack).toHaveBeenCalledWith(true);
    
    // Click jetpack button again to deactivate
    fireEvent.click(jetpackButton);
    
    // Should call onJetpack with false
    expect(mockOnJetpack).toHaveBeenCalledWith(false);
  });
  
  test('should call onSwitchWeapon when weapon buttons are clicked', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Find weapon buttons
    const gravityButton = screen.getByText('1');
    const timeButton = screen.getByText('2');
    const phaseButton = screen.getByText('3');
    
    // Click gravity button
    fireEvent.click(gravityButton);
    
    // Should call onSwitchWeapon with 0
    expect(mockOnSwitchWeapon).toHaveBeenCalledWith(0);
    
    // Click time button
    fireEvent.click(timeButton);
    
    // Should call onSwitchWeapon with 1
    expect(mockOnSwitchWeapon).toHaveBeenCalledWith(1);
    
    // Click phase button
    fireEvent.click(phaseButton);
    
    // Should call onSwitchWeapon with 2
    expect(mockOnSwitchWeapon).toHaveBeenCalledWith(2);
  });
  
  test('should handle joystick movement correctly', () => {
    render(
      <MobileControls
        onMove={mockOnMove}
        onLook={mockOnLook}
        onJump={mockOnJump}
        onShoot={mockOnShoot}
        onSwitchWeapon={mockOnSwitchWeapon}
        onGrapple={mockOnGrapple}
        onJetpack={mockOnJetpack}
        onInteract={mockOnInteract}
      />
    );
    
    // Get the on method from the created joystick
    const onMethod = (nipplejs.create() as any).on;
    
    // Simulate joystick move event
    const moveCallback = onMethod.mock.calls.find(call => call[0] === 'move')[1];
    
    // Call the move callback with mock data
    moveCallback(null, {
      angle: { radian: Math.PI / 4 }, // 45 degrees
      force: 50, // Full force
      vector: { x: 0.7071, y: 0.7071 } // Normalized vector for 45 degrees
    });
    
    // Should call onMove with correct direction vector
    expect(mockOnMove).toHaveBeenCalledWith(
      expect.objectContaining({
        x: expect.any(Number),
        y: 0,
        z: expect.any(Number)
      })
    );
    
    // The x and z values should be approximately equal for 45 degrees
    const moveArg = mockOnMove.mock.calls[0][0];
    expect(Math.abs(moveArg.x)).toBeCloseTo(Math.abs(moveArg.z));
  });
});
