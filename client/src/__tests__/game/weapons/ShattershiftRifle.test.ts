import * as THREE from 'three';
import ShattershiftRifle from '../../../game/weapons/ShattershiftRifle';
import { AmmoType } from '../../../game/types';

// Create directory if it doesn't exist
jest.mock('../../../game/audio/AudioManager', () => ({
  playSound: jest.fn(),
  playSoundEffect: jest.fn(),
}));

describe('ShattershiftRifle', () => {
  let scene: THREE.Scene;
  let camera: THREE.PerspectiveCamera;
  let rifle: ShattershiftRifle;
  
  beforeEach(() => {
    // Setup
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera();
    rifle = new ShattershiftRifle(scene, camera);
    
    // Mock document.dispatchEvent
    document.dispatchEvent = jest.fn();
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  test('should initialize with correct default values', () => {
    expect(rifle.getCurrentAmmoType()).toBe(AmmoType.GravityWell);
    expect(rifle.getAmmo()).toEqual([5, 5, 5]); // Default ammo counts
    expect(rifle.isHomingEnabled()).toBe(false);
  });
  
  test('should switch ammo types correctly', () => {
    expect(rifle.getCurrentAmmoType()).toBe(AmmoType.GravityWell);
    
    rifle.setAmmoType(AmmoType.TimeBubble);
    expect(rifle.getCurrentAmmoType()).toBe(AmmoType.TimeBubble);
    
    rifle.setAmmoType(AmmoType.PhaseNet);
    expect(rifle.getCurrentAmmoType()).toBe(AmmoType.PhaseNet);
  });
  
  test('should reduce ammo when firing', () => {
    const initialAmmo = rifle.getAmmo();
    
    // Fire gravity well ammo
    rifle.fireAmmo();
    
    // Ammo should be reduced
    const newAmmo = rifle.getAmmo();
    expect(newAmmo[0]).toBe(initialAmmo[0] - 1);
    expect(newAmmo[1]).toBe(initialAmmo[1]); // Other ammo types unchanged
    expect(newAmmo[2]).toBe(initialAmmo[2]); // Other ammo types unchanged
  });
  
  test('should not reduce ammo for Phase Net (infinite ammo)', () => {
    // Switch to Phase Net
    rifle.setAmmoType(AmmoType.PhaseNet);
    const initialAmmo = rifle.getAmmo();
    
    // Fire Phase Net
    rifle.fireAmmo();
    
    // Ammo should not be reduced for Phase Net
    const newAmmo = rifle.getAmmo();
    expect(newAmmo[2]).toBe(initialAmmo[2]); // Phase Net ammo unchanged
  });
  
  test('should enable homing correctly', () => {
    // Initially homing should be disabled
    expect(rifle.isHomingEnabled()).toBe(false);
    
    // Enable homing
    rifle.enableHoming(30);
    
    // Homing should be enabled
    expect(rifle.isHomingEnabled()).toBe(true);
    
    // Should dispatch custom event
    expect(document.dispatchEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'homingStatusChanged',
        detail: expect.objectContaining({
          active: true,
          duration: 30
        })
      })
    );
  });
  
  test('should disable homing after duration expires', () => {
    // Enable homing
    rifle.enableHoming(30);
    expect(rifle.isHomingEnabled()).toBe(true);
    
    // Fast-forward time by updating with large delta
    rifle.updateHoming(31); // More than the duration
    
    // Homing should be disabled
    expect(rifle.isHomingEnabled()).toBe(false);
    
    // Should dispatch custom event for deactivation
    expect(document.dispatchEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'homingStatusChanged',
        detail: expect.objectContaining({
          active: false,
          duration: 0
        })
      })
    );
  });
  
  test('should update homing timer correctly', () => {
    // Enable homing
    rifle.enableHoming(30);
    
    // Update with small delta
    rifle.updateHoming(5);
    
    // Homing should still be enabled
    expect(rifle.isHomingEnabled()).toBe(true);
    
    // Update with remaining time
    rifle.updateHoming(25);
    
    // Homing should be disabled
    expect(rifle.isHomingEnabled()).toBe(false);
  });
});
