import * as THREE from 'three';
import GameEngine from '../../../game/engine/GameEngine';
import { SpecterType, AmmoType } from '../../../game/types';

// Mock dependencies
jest.mock('../../../game/engine/InputHandler');
jest.mock('../../../game/world/LevelGenerator');
jest.mock('../../../game/entities/Player');
jest.mock('../../../game/entities/Specter');
jest.mock('../../../game/entities/PetSpecter');
jest.mock('../../../game/weapons/ShattershiftRifle');
jest.mock('../../../game/audio/AudioManager');

describe('GameEngine', () => {
  let container: HTMLDivElement;
  let gameEngine: GameEngine;
  
  beforeEach(() => {
    // Setup
    container = document.createElement('div');
    document.body.appendChild(container);
    
    // Create game engine
    gameEngine = new GameEngine(container, false);
  });
  
  afterEach(() => {
    // Cleanup
    document.body.removeChild(container);
    jest.clearAllMocks();
  });
  
  test('should initialize correctly', () => {
    expect(gameEngine).toBeDefined();
    expect(gameEngine.isRunning()).toBe(true);
  });
  
  test('should pause and resume correctly', () => {
    // Initially running
    expect(gameEngine.isRunning()).toBe(true);
    
    // Pause
    gameEngine.pause();
    expect(gameEngine.isRunning()).toBe(false);
    
    // Resume
    gameEngine.resume();
    expect(gameEngine.isRunning()).toBe(true);
  });
  
  test('should set mobile mode correctly', () => {
    // Mock input handler
    const mockSetMobileMode = jest.fn();
    (gameEngine as any).input = { setMobileMode: mockSetMobileMode };
    
    // Set mobile mode
    gameEngine.setMobileMode(true);
    
    // Should set internal flag
    expect((gameEngine as any).isMobile).toBe(true);
    
    // Should call input handler
    expect(mockSetMobileMode).toHaveBeenCalledWith(true);
  });
  
  test('should set network mode correctly', () => {
    // Set network mode
    gameEngine.setNetworkMode(true);
    
    // Should set internal flag
    expect((gameEngine as any).isNetworkMode).toBe(true);
  });
  
  test('should handle mobile input correctly', () => {
    // Mock input handler
    const mockSetMobileDirection = jest.fn();
    (gameEngine as any).input = { setMobileDirection: mockSetMobileDirection };
    
    // Create direction vector
    const direction = new THREE.Vector3(1, 0, 0);
    
    // Handle mobile input
    gameEngine.handleMobileInput(direction);
    
    // Should call input handler
    expect(mockSetMobileDirection).toHaveBeenCalledWith(direction);
  });
  
  test('should handle mobile look correctly', () => {
    // Create camera
    (gameEngine as any).camera = {
      rotation: { x: 0, y: 0 }
    };
    
    // Handle mobile look
    gameEngine.handleMobileLook(0.1, 0.2);
    
    // Should update camera rotation
    expect((gameEngine as any).camera.rotation.y).toBe(-0.1);
    expect((gameEngine as any).camera.rotation.x).toBe(0.2);
  });
  
  test('should handle mobile jump correctly', () => {
    // Mock input handler
    const mockSetMobileJump = jest.fn();
    (gameEngine as any).input = { setMobileJump: mockSetMobileJump };
    
    // Handle mobile jump
    gameEngine.handleMobileJump();
    
    // Should call input handler
    expect(mockSetMobileJump).toHaveBeenCalled();
  });
  
  test('should handle mobile shoot correctly', () => {
    // Mock input handler
    const mockSetMobileShoot = jest.fn();
    (gameEngine as any).input = { setMobileShoot: mockSetMobileShoot };
    
    // Handle mobile shoot
    gameEngine.handleMobileShoot();
    
    // Should call input handler
    expect(mockSetMobileShoot).toHaveBeenCalled();
  });
  
  test('should handle mobile weapon switch correctly', () => {
    // Mock input handler
    const mockSetMobileAmmoSwitch = jest.fn();
    (gameEngine as any).input = { setMobileAmmoSwitch: mockSetMobileAmmoSwitch };
    
    // Handle mobile weapon switch
    gameEngine.handleMobileWeaponSwitch(1);
    
    // Should call input handler
    expect(mockSetMobileAmmoSwitch).toHaveBeenCalledWith(1);
  });
  
  test('should handle mobile jetpack correctly', () => {
    // Mock input handler
    const mockSetMobileJetpack = jest.fn();
    (gameEngine as any).input = { setMobileJetpack: mockSetMobileJetpack };
    
    // Handle mobile jetpack
    gameEngine.handleMobileJetpack(true);
    
    // Should call input handler
    expect(mockSetMobileJetpack).toHaveBeenCalledWith(true);
  });
  
  test('should update homing target at fixed intervals', () => {
    // Mock rifle and specters
    (gameEngine as any).rifle = {
      isHomingEnabled: jest.fn().mockReturnValue(true)
    };
    (gameEngine as any).specters = [
      { getPosition: jest.fn().mockReturnValue(new THREE.Vector3(10, 0, 0)) },
      { getPosition: jest.fn().mockReturnValue(new THREE.Vector3(5, 0, 0)) }
    ];
    (gameEngine as any).camera = {
      position: new THREE.Vector3(0, 0, 0)
    };
    
    // Mock updateHomingTarget method
    const updateHomingTargetSpy = jest.spyOn(gameEngine as any, 'updateHomingTarget');
    
    // Set elapsed time to trigger update
    (gameEngine as any).elapsedTime = 1;
    (gameEngine as any).lastHomingUpdateTime = 0;
    (gameEngine as any).homingUpdateInterval = 0.5;
    
    // Call update method
    (gameEngine as any).update(0.1);
    
    // Should call updateHomingTarget
    expect(updateHomingTargetSpy).toHaveBeenCalled();
    
    // Should update lastHomingUpdateTime
    expect((gameEngine as any).lastHomingUpdateTime).toBe(1);
  });
  
  test('should find nearest specter for homing targeting', () => {
    // Create camera
    (gameEngine as any).camera = {
      position: new THREE.Vector3(0, 0, 0)
    };
    
    // Create specters at different distances
    (gameEngine as any).specters = [
      { getPosition: jest.fn().mockReturnValue(new THREE.Vector3(10, 0, 0)) },
      { getPosition: jest.fn().mockReturnValue(new THREE.Vector3(5, 0, 0)) },
      { getPosition: jest.fn().mockReturnValue(new THREE.Vector3(20, 0, 0)) }
    ];
    
    // Call updateHomingTarget
    (gameEngine as any).updateHomingTarget();
    
    // Should set nearest specter to the closest one (at distance 5)
    expect((gameEngine as any).nearestSpecter).toBe((gameEngine as any).specters[1]);
  });
});
