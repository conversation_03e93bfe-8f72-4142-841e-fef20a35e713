import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { ColiseumArena } from '../../../game/world/ColiseumArena';

// Mock SVGLoader
jest.mock('three/examples/jsm/loaders/SVGLoader', () => ({
  SVGLoader: class {
    load() {}
  }
}));

describe('ColiseumArena', () => {
  let scene: THREE.Scene;
  let world: CANNON.World;
  let arena: ColiseumArena;

  beforeEach(() => {
    // Set up scene and world
    scene = new THREE.Scene();
    world = new CANNON.World();
    world.gravity.set(0, -9.82, 0);

    // Mock texture loader
    const mockTexture = {
      wrapS: 0,
      wrapT: 0,
      repeat: { set: jest.fn() }
    };
    THREE.TextureLoader.prototype.load = jest.fn().mockReturnValue(mockTexture);

    // Create arena
    arena = new ColiseumArena(scene, world);
  });

  afterEach(() => {
    // Clean up
    arena.dispose();
    
    // Reset mocks
    jest.clearAllMocks();
  });

  test('should create arena with all components', () => {
    arena.create();
    
    // Check that objects were added to the scene
    expect(scene.children.length).toBeGreaterThan(0);
    
    // Check that physics bodies were added to the world
    expect(world.bodies.length).toBeGreaterThan(0);
  });

  test('should return arena center', () => {
    const center = arena.getArenaCenter();
    
    expect(center).toBeInstanceOf(THREE.Vector3);
    expect(center.x).toBe(0);
    expect(center.y).toBe(0);
    expect(center.z).toBe(0);
  });

  test('should return arena radius', () => {
    const radius = arena.getArenaRadius();
    
    expect(typeof radius).toBe('number');
    expect(radius).toBeGreaterThan(0);
  });

  test('should toggle spectator mode', () => {
    // Default should be false
    expect(arena.getIsSpectatorMode()).toBe(false);
    
    // Enable spectator mode
    arena.setSpectatorMode(true);
    expect(arena.getIsSpectatorMode()).toBe(true);
    
    // Disable spectator mode
    arena.setSpectatorMode(false);
    expect(arena.getIsSpectatorMode()).toBe(false);
  });

  test('should provide spectator positions', () => {
    arena.create();
    
    const position = arena.getRandomSpectatorPosition();
    
    expect(position).toBeInstanceOf(THREE.Vector3);
  });

  test('should dispose of all resources', () => {
    arena.create();
    
    // Count children before disposal
    const childrenBefore = scene.children.length;
    const bodiesBefore = world.bodies.length;
    
    arena.dispose();
    
    // Count children after disposal
    const childrenAfter = scene.children.length;
    
    // Scene should have fewer children after disposal
    expect(childrenAfter).toBeLessThan(childrenBefore);
  });
});
