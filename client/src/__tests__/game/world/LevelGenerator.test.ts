import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';
import LevelGenerator from '../../../game/world/LevelGenerator';

// Mock TextureLoader
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    TextureLoader: jest.fn().mockImplementation(() => ({
      load: jest.fn().mockImplementation((url, onLoad) => {
        const texture = {
          wrapS: 0,
          wrapT: 0,
          repeat: { set: jest.fn() },
        };
        if (onLoad) onLoad(texture);
        return texture;
      }),
    })),
  };
});

describe('LevelGenerator', () => {
  let scene: THREE.Scene;
  let world: CANNON.World;
  let levelGenerator: LevelGenerator;
  
  beforeEach(() => {
    // Setup
    scene = new THREE.Scene();
    world = new CANNON.World();
    levelGenerator = new LevelGenerator(scene, world);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  test('should initialize correctly', () => {
    expect(levelGenerator).toBeDefined();
  });
  
  test('should generate initial level', () => {
    // Spy on internal methods
    const createMainGroundSpy = jest.spyOn(levelGenerator as any, 'createMainGround');
    const generateChunkSpy = jest.spyOn(levelGenerator as any, 'generateChunk');
    
    levelGenerator.generateInitialLevel();
    
    // Should create main ground
    expect(createMainGroundSpy).toHaveBeenCalled();
    
    // Should generate chunks (center + surrounding)
    expect(generateChunkSpy).toHaveBeenCalledWith(0, 0); // Center chunk
    expect(generateChunkSpy).toHaveBeenCalledTimes(9); // 3x3 grid of chunks
  });
  
  test('should update ground position to follow player', () => {
    // Create a spy for the mainGroundMesh position update
    const mockMainGroundMesh = { position: { x: 0, z: 0 } };
    Object.defineProperty(levelGenerator as any, 'mainGroundMesh', {
      get: jest.fn(() => mockMainGroundMesh),
      set: jest.fn(),
    });
    
    // Call update with player position
    const playerPosition = new THREE.Vector3(10, 5, 20);
    levelGenerator.update(playerPosition);
    
    // Ground should follow player (x and z coordinates)
    expect(mockMainGroundMesh.position.x).toBe(playerPosition.x);
    expect(mockMainGroundMesh.position.z).toBe(playerPosition.z);
  });
  
  test('should generate new chunks when player moves to a new chunk', () => {
    // Mock the generateChunk method
    const generateChunkSpy = jest.spyOn(levelGenerator as any, 'generateChunk').mockImplementation(() => {});
    
    // Set initial player chunk
    (levelGenerator as any).lastPlayerChunk = { x: 0, z: 0 };
    
    // Move player to a new chunk
    const playerPosition = new THREE.Vector3(100, 5, 100); // Assuming chunkSize is less than 100
    levelGenerator.update(playerPosition);
    
    // Should generate new chunks
    expect(generateChunkSpy).toHaveBeenCalled();
    
    // Player chunk should be updated
    expect((levelGenerator as any).lastPlayerChunk.x).not.toBe(0);
    expect((levelGenerator as any).lastPlayerChunk.z).not.toBe(0);
  });
  
  test('should regenerate level correctly', () => {
    // Spy on internal methods
    const removeExistingChunksSpy = jest.spyOn(levelGenerator as any, 'removeExistingChunks').mockImplementation(() => {});
    const generateChunkSpy = jest.spyOn(levelGenerator as any, 'generateChunk').mockImplementation(() => {});
    
    // Add some generated chunks to the map
    (levelGenerator as any).generatedChunks.set('0,0', true);
    (levelGenerator as any).generatedChunks.set('1,0', true);
    
    levelGenerator.regenerateLevel();
    
    // Should remove existing chunks
    expect(removeExistingChunksSpy).toHaveBeenCalled();
    
    // Should clear generated chunks map
    expect((levelGenerator as any).generatedChunks.size).toBe(0);
    
    // Should reset player chunk
    expect((levelGenerator as any).lastPlayerChunk).toEqual({ x: 0, z: 0 });
    
    // Should generate new chunks
    expect(generateChunkSpy).toHaveBeenCalledWith(0, 0); // Center chunk
    expect(generateChunkSpy).toHaveBeenCalledTimes(9); // 3x3 grid of chunks
  });
});
