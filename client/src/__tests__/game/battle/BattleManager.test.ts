import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { BattleManager, BattleState } from '../../../game/battle/BattleManager';
import { PetSpecter } from '../../../game/entities/PetSpecter';

// Mock AudioManager
jest.mock('../../../game/audio/AudioManager', () => ({
  playSoundEffect: jest.fn(),
  playSound: jest.fn(),
  stopSound: jest.fn(),
}));

describe('BattleManager', () => {
  let scene: THREE.Scene;
  let world: CANNON.World;
  let battleManager: BattleManager;
  let pet1: PetSpecter;
  let pet2: PetSpecter;

  beforeEach(() => {
    // Set up scene and world
    scene = new THREE.Scene();
    world = new CANNON.World();
    world.gravity.set(0, -9.82, 0);

    // Create battle manager
    battleManager = new BattleManager(scene, world);

    // Create mock pet specters
    // Note: We're creating partial mocks since we don't need full functionality
    pet1 = {
      id: 1,
      name: 'Test Pet 1',
      health: 100,
      maxHealth: 100,
      attackPower: 10,
      defenseValue: 5,
      behaviorState: 'follow',
      mesh: new THREE.Mesh(new THREE.SphereGeometry(1), new THREE.MeshBasicMaterial()),
      getPosition: jest.fn().mockReturnValue(new THREE.Vector3(-10, 5, 0)),
      setPosition: jest.fn(),
      takeDamage: jest.fn().mockImplementation(function(this: any, amount: number) {
        this.health -= amount;
      }),
    } as unknown as PetSpecter;

    pet2 = {
      id: 2,
      name: 'Test Pet 2',
      health: 100,
      maxHealth: 100,
      attackPower: 10,
      defenseValue: 5,
      behaviorState: 'follow',
      mesh: new THREE.Mesh(new THREE.SphereGeometry(1), new THREE.MeshBasicMaterial()),
      getPosition: jest.fn().mockReturnValue(new THREE.Vector3(10, 5, 0)),
      setPosition: jest.fn(),
      takeDamage: jest.fn().mockImplementation(function(this: any, amount: number) {
        this.health -= amount;
      }),
    } as unknown as PetSpecter;

    // Add meshes to scene
    scene.add(pet1.mesh);
    scene.add(pet2.mesh);

    // Add user data to meshes
    pet1.mesh.userData = { specterReference: pet1 };
    pet2.mesh.userData = { specterReference: pet2 };
  });

  afterEach(() => {
    // Clean up
    scene.remove(pet1.mesh);
    scene.remove(pet2.mesh);
    pet1.mesh.geometry.dispose();
    pet2.mesh.geometry.dispose();
    (pet1.mesh.material as THREE.Material).dispose();
    (pet2.mesh.material as THREE.Material).dispose();
    
    // Reset mocks
    jest.clearAllMocks();
  });

  test('should initialize with waiting state', () => {
    expect(battleManager.getBattleState()).toBe(BattleState.WAITING);
  });

  test('should add pets to the battle', () => {
    battleManager.addPet(pet1);
    battleManager.addPet(pet2);
    
    // Check that pets were positioned
    expect(pet1.setPosition).toHaveBeenCalled();
    expect(pet2.setPosition).toHaveBeenCalled();
    
    // Check that pets were set to attack mode
    expect(pet1.behaviorState).toBe('attack');
    expect(pet2.behaviorState).toBe('attack');
  });

  test('should not start battle with fewer than 2 pets', () => {
    const onBattleEnd = jest.fn();
    
    battleManager.addPet(pet1);
    battleManager.startBattle(onBattleEnd);
    
    // Battle should not start
    expect(battleManager.getBattleState()).toBe(BattleState.WAITING);
    expect(onBattleEnd).not.toHaveBeenCalled();
  });

  test('should start battle with 2 or more pets', () => {
    const onBattleEnd = jest.fn();
    
    battleManager.addPet(pet1);
    battleManager.addPet(pet2);
    battleManager.startBattle(onBattleEnd);
    
    // Battle should be in intro state
    expect(battleManager.getBattleState()).toBe(BattleState.INTRO);
    
    // After intro period, battle should be in fighting state
    jest.advanceTimersByTime(3000);
    expect(battleManager.getBattleState()).toBe(BattleState.FIGHTING);
  });

  test('should log battle events', () => {
    battleManager.addPet(pet1);
    battleManager.addPet(pet2);
    battleManager.startBattle();
    
    // Should have at least one event (battle start)
    expect(battleManager.getBattleEvents().length).toBeGreaterThan(0);
  });

  test('should reset battle state', () => {
    battleManager.addPet(pet1);
    battleManager.addPet(pet2);
    battleManager.startBattle();
    
    // Reset battle
    battleManager.reset();
    
    // Should be back to waiting state
    expect(battleManager.getBattleState()).toBe(BattleState.WAITING);
    expect(battleManager.getBattleEvents().length).toBe(0);
  });

  test('should toggle spectator mode', () => {
    // Default should be true (spectator mode enabled)
    expect(battleManager.isSpectatorMode()).toBe(true);
    
    // Disable spectator mode
    battleManager.setSpectatorMode(false);
    expect(battleManager.isSpectatorMode()).toBe(false);
    
    // Enable spectator mode
    battleManager.setSpectatorMode(true);
    expect(battleManager.isSpectatorMode()).toBe(true);
  });
});
