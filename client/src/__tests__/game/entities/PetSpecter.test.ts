import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { PetSpecter } from '../../../game/entities/PetSpecter';
import { SpecterTraitType, SpecterType } from '../../../game/types';

// Mock AudioManager
jest.mock('../../../game/audio/AudioManager', () => ({
  playSoundEffect: jest.fn(),
  playSound: jest.fn(),
}));

describe('PetSpecter', () => {
  let scene: THREE.Scene;
  let world: CANNON.World;
  let petSpecter: PetSpecter;
  let mockSpecterType: SpecterType;
  
  beforeEach(() => {
    // Setup
    scene = new THREE.Scene();
    world = new CANNON.World();
    
    mockSpecterType = {
      id: 1,
      name: 'Wisp',
      color: '#3399ff',
      points: 50,
      texture: 'assets/textures/wisp.png'
    };
    
    petSpecter = new PetSpecter(
      scene,
      world,
      new THREE.Vector3(0, 0, 0),
      'player-123',
      mockSpecterType,
      'Test Pet'
    );
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  test('should initialize with correct default values', () => {
    expect(petSpecter.name).toBe('Test Pet');
    expect(petSpecter.ownerID).toBe('player-123');
    expect(petSpecter.specterType).toBe(mockSpecterType);
    expect(petSpecter.level).toBe(1);
    expect(petSpecter.health).toBe(100);
    expect(petSpecter.maxHealth).toBe(100);
    expect(petSpecter.behaviorState).toBe('follow');
    expect(petSpecter.traits.length).toBe(5); // Should have 5 traits
  });
  
  test('should take damage correctly', () => {
    const initialHealth = petSpecter.health;
    petSpecter.takeDamage(20);
    
    // Health should be reduced, but not by the full amount due to defense
    expect(petSpecter.health).toBeLessThan(initialHealth);
    expect(petSpecter.health).toBeGreaterThan(initialHealth - 20); // Defense reduces damage
  });
  
  test('should die when health reaches zero', () => {
    // Mock the die method
    const dieSpy = jest.spyOn(petSpecter, 'die').mockImplementation(() => {});
    
    // Deal massive damage to ensure death
    petSpecter.takeDamage(1000);
    
    expect(petSpecter.health).toBeLessThanOrEqual(0);
    expect(dieSpy).toHaveBeenCalled();
  });
  
  test('should gain trait XP correctly', () => {
    const initialXP = petSpecter.getTraitXP(SpecterTraitType.ATTACK);
    const xpToAdd = 50;
    
    petSpecter.gainTraitXP(SpecterTraitType.ATTACK, xpToAdd);
    
    expect(petSpecter.getTraitXP(SpecterTraitType.ATTACK)).toBe(initialXP + xpToAdd);
  });
  
  test('should level up trait when XP reaches threshold', () => {
    const initialLevel = petSpecter.getTraitLevel(SpecterTraitType.ATTACK);
    const trait = petSpecter.traits.find(t => t.type === SpecterTraitType.ATTACK);
    
    if (trait) {
      // Set XP just below threshold
      trait.xp = trait.xpToNextLevel - 1;
      
      // Add enough XP to level up
      petSpecter.gainTraitXP(SpecterTraitType.ATTACK, 2);
      
      // Trait should level up
      expect(petSpecter.getTraitLevel(SpecterTraitType.ATTACK)).toBe(initialLevel + 1);
      // XP should reset
      expect(petSpecter.getTraitXP(SpecterTraitType.ATTACK)).toBe(1); // 2 - (xpToNextLevel - xp)
    } else {
      fail('Attack trait not found');
    }
  });
  
  test('should change behavior state correctly', () => {
    expect(petSpecter.behaviorState).toBe('follow');
    
    petSpecter.setBehaviorState('attack');
    expect(petSpecter.behaviorState).toBe('attack');
    
    petSpecter.setBehaviorState('idle');
    expect(petSpecter.behaviorState).toBe('idle');
  });
  
  test('should equip items correctly', () => {
    const weapon = {
      id: 'weapon1',
      name: 'Test Weapon',
      type: 'weapon',
      attackBonus: 10,
      defenseBonus: 0,
      speedBonus: 5,
      description: 'A test weapon'
    };
    
    petSpecter.equipItem(weapon);
    
    expect(petSpecter.equipment.weapon).toBe(weapon);
    // Check that stats are affected by equipment
    expect(petSpecter.getEquipmentAttackBonus()).toBe(10);
    expect(petSpecter.getEquipmentSpeedBonus()).toBe(5);
  });
});
