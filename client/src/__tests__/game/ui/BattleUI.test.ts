import { Battle<PERSON> } from '../../../game/ui/BattleUI';
import { BattleState } from '../../../game/battle/BattleManager';
import { PetSpecter } from '../../../game/entities/PetSpecter';

describe('BattleUI', () => {
  let battleUI: BattleUI;
  let mockPet1: Partial<PetSpecter>;
  let mockPet2: Partial<PetSpecter>;

  beforeEach(() => {
    // Create mock document elements
    document.body.innerHTML = '';
    
    // Create battle UI
    battleUI = new BattleUI();
    
    // Create mock pet specters
    mockPet1 = {
      name: 'Test Pet 1',
      health: 80,
      maxHealth: 100
    } as Partial<PetSpecter>;
    
    mockPet2 = {
      name: 'Test Pet 2',
      health: 60,
      maxHealth: 100
    } as Partial<PetSpecter>;
  });

  afterEach(() => {
    // Clean up
    battleUI.destroy();
    document.body.innerHTML = '';
  });

  test('should create UI elements', () => {
    // Check that elements were added to the document
    expect(document.body.children.length).toBeGreaterThan(0);
  });

  test('should show and hide UI', () => {
    // Initially hidden
    expect(battleUI.isShown()).toBe(false);
    
    // Show UI
    battleUI.show();
    expect(battleUI.isShown()).toBe(true);
    
    // Hide UI
    battleUI.hide();
    expect(battleUI.isShown()).toBe(false);
  });

  test('should update pet information', () => {
    battleUI.show();
    battleUI.updatePetInfo(mockPet1 as PetSpecter, mockPet2 as PetSpecter);
    
    // Check that pet names are displayed
    const container = document.body.firstChild as HTMLElement;
    expect(container.innerHTML).toContain('Test Pet 1');
    expect(container.innerHTML).toContain('Test Pet 2');
    
    // Check that health values are displayed
    expect(container.innerHTML).toContain('80/100');
    expect(container.innerHTML).toContain('60/100');
  });

  test('should update battle state', () => {
    battleUI.show();
    
    // Test different battle states
    battleUI.updateBattleState(BattleState.WAITING);
    expect(document.body.innerHTML).toContain('Waiting for battle');
    
    battleUI.updateBattleState(BattleState.INTRO);
    expect(document.body.innerHTML).toContain('Battle starting');
    
    battleUI.updateBattleState(BattleState.FIGHTING);
    expect(document.body.innerHTML).toContain('Battle in progress');
    
    battleUI.updateBattleState(BattleState.FINISHED);
    expect(document.body.innerHTML).toContain('Battle finished');
  });

  test('should add battle events to log', () => {
    battleUI.show();
    
    // Add a test event
    const testEvent = {
      timestamp: Date.now(),
      type: 'attack' as const,
      sourceId: 1,
      targetId: 2,
      message: 'Test Pet 1 attacks Test Pet 2!'
    };
    
    battleUI.addBattleEvent(testEvent);
    
    // Check that event message is displayed
    expect(document.body.innerHTML).toContain('Test Pet 1 attacks Test Pet 2!');
  });

  test('should clean up on destroy', () => {
    battleUI.show();
    
    // Count children before destruction
    const childrenBefore = document.body.children.length;
    
    battleUI.destroy();
    
    // Count children after destruction
    const childrenAfter = document.body.children.length;
    
    // Document should have fewer children after destruction
    expect(childrenAfter).toBeLessThan(childrenBefore);
  });
});
