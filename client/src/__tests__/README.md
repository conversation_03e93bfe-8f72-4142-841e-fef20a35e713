# SpecterShift Test Suite

This directory contains tests for the SpecterShift game. The tests are organized by component type and functionality.

## Test Structure

- `game/entities/`: Tests for game entities like PetSpecter
- `game/world/`: Tests for world generation and level management
- `game/controls/`: Tests for player controls, including mobile controls
- `game/engine/`: Tests for the game engine and core functionality
- `game/weapons/`: Tests for weapons and combat systems
- `hooks/`: Tests for React hooks used in the game

## Running Tests

To run all tests:

```bash
npm test
```

To run tests in watch mode (for development):

```bash
npm run test:watch
```

To run a specific test file:

```bash
npm test -- path/to/test.test.ts
```

## Test Coverage

The test suite is configured to generate coverage reports. After running tests, you can view the coverage report in the `coverage` directory.

## Writing New Tests

When writing new tests, follow these guidelines:

1. Place tests in the appropriate directory based on the component being tested
2. Name test files with the `.test.ts` or `.test.tsx` extension
3. Use descriptive test names that explain what is being tested
4. Mock external dependencies to isolate the component being tested
5. Test both success and failure cases
6. Test edge cases and boundary conditions

## Mocks

Common mocks are set up in the `jest.setup.js` file, including:

- Three.js components
- WebGL context
- Canvas
- Audio
- WebSockets
- Cannon.js physics

If you need additional mocks, add them to the appropriate test file or consider adding them to the setup file if they are used across multiple tests.

## Key Areas Tested

1. **PetSpecter**: Core functionality, stats, behaviors, combat
2. **Homing Powerup**: Performance optimization
3. **Infinite World Generation**: Ground plane following camera
4. **Mobile Controls**: Touch interface and joystick controls
5. **Game Engine**: Core functionality and mobile support
6. **Multiplayer**: WebSocket communication and game state management

## Future Test Improvements

- Add integration tests for complex interactions between components
- Add end-to-end tests for critical user flows
- Improve test coverage for UI components
- Add performance tests for critical game systems
