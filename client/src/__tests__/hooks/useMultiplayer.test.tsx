import { renderHook, act } from '@testing-library/react';
import { useMultiplayer } from '../../hooks/useMultiplayer';
import { GameMode, MessageType } from '@shared/schema';

// Mock WebSocket
const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
};

// Mock global WebSocket
global.WebSocket = jest.fn().mockImplementation(() => mockWebSocket);

describe('useMultiplayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should initialize with default values', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    expect(result.current.isConnected).toBe(false);
    expect(result.current.isHost).toBe(false);
    expect(result.current.gameId).toBe(null);
    expect(result.current.players).toEqual([]);
    expect(result.current.gameState).toEqual({
      id: 0,
      mode: GameMode.SINGLE,
      status: 'waiting',
      currentLevel: 1,
      enemiesTotal: 0,
      enemiesDefeated: 0,
      players: [],
    });
  });
  
  test('should connect to WebSocket server', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Should create WebSocket
    expect(WebSocket).toHaveBeenCalled();
    
    // Should add event listeners
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
  });
  
  test('should create a new game', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });
    
    // Create a new game
    act(() => {
      result.current.createGame(GameMode.TEAM_DEATHMATCH);
    });
    
    // Should send create game message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.CREATE_GAME)
    );
  });
  
  test('should join an existing game', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player2');
    });
    
    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });
    
    // Join an existing game
    act(() => {
      result.current.joinGame('game-123');
    });
    
    // Should send join game message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.JOIN_GAME)
    );
  });
  
  test('should handle player movement updates', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });
    
    // Update player position
    act(() => {
      result.current.updatePlayerPosition({ x: 10, y: 5, z: 20 });
    });
    
    // Should send player update message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.PLAYER_UPDATE)
    );
  });
  
  test('should handle weapon effect creation', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });
    
    // Create weapon effect
    const effectData = {
      id: 'effect-123',
      type: 'gravity',
      position: { x: 10, y: 5, z: 20 },
      radius: 5,
      duration: 3
    };
    
    act(() => {
      result.current.createWeaponEffect(effectData);
    });
    
    // Should send weapon effect message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.WEAPON_EFFECT)
    );
  });
  
  test('should handle incoming game state updates', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Get message handler
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'message')[1];
    
    // Simulate game state update message
    const gameStateUpdate = {
      type: MessageType.GAME_STATE,
      data: {
        id: 123,
        mode: GameMode.TEAM_DEATHMATCH,
        status: 'in_progress',
        currentLevel: 2,
        enemiesTotal: 10,
        enemiesDefeated: 5,
        players: [
          { id: 'player-1', name: 'Player1', position: { x: 0, y: 0, z: 0 } }
        ]
      }
    };
    
    act(() => {
      messageHandler({ data: JSON.stringify(gameStateUpdate) });
    });
    
    // Game state should be updated
    expect(result.current.gameState).toEqual(gameStateUpdate.data);
  });
  
  test('should disconnect from WebSocket server', () => {
    const { result } = renderHook(() => useMultiplayer());
    
    // Simulate connection
    act(() => {
      result.current.connect('Player1');
    });
    
    // Disconnect
    act(() => {
      result.current.disconnect();
    });
    
    // Should close WebSocket
    expect(mockWebSocket.close).toHaveBeenCalled();
  });
});
