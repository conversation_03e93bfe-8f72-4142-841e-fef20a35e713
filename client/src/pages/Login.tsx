import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

const Login: React.FC = () => {
  const { isAuthLoading } = useAuth();
  const [bedrockWidgetInitialized, setBedrockWidgetInitialized] = useState(false);

  // Define Orange ID tenant credentials
  const bedrockConfig = {
    baseUrl: 'https://api.bedrockpassport.com',
    authCallbackUrl: window.location.origin + '/auth/callback',
    tenantId: 'orange-yn4vaq0i6w',
    subscriptionKey: '1c533c7334df48b88f7d85c361c10015',
  };

  // Initialize Bedrock widget when component mounts
  useEffect(() => {
    if (isAuthLoading) {
      console.log('Login.tsx useEffect: Auth state is loading...');
      return; 
    }

    console.log('Login.tsx useEffect: Auth loaded. Initializing login UI.');
    
    if (!bedrockWidgetInitialized) {
      console.log('Login.tsx useEffect: Initializing Bedrock dependencies.');
      initializeBedrockDependencies();
    } else {
      console.log('Login.tsx useEffect: Bedrock widget already marked as initialized (or in process). Doing nothing further here.');
    }
  }, [isAuthLoading, bedrockWidgetInitialized]);

  const initializeBedrockDependencies = async () => {
    if (bedrockWidgetInitialized) return; // Prevent re-initialization

    console.log('Login: Starting Bedrock Passport initialization sequence.');
    try {
      if (!window.React || !window.ReactDOM) {
        console.error('Login: React or ReactDOM not found on window. Critical error.');
        throw new Error("React or ReactDOM not available globally.");
      }

      // Set a timeout to prevent hanging if script loading takes too long
      const scriptLoadPromise = new Promise(async (resolve, reject) => {
        try {
          if (!window.Bedrock) {
            console.log('Login: Loading Bedrock Passport script');
            await loadScript('https://public-cdn-files.pages.dev/bedrock-passport.umd.js', 'Bedrock');
          }
          resolve(true);
        } catch (error) {
          reject(error);
        }
      });
      
      // Add a timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Bedrock script loading timed out after 10 seconds")), 10000);
      });
      
      // Race the script loading against the timeout
      await Promise.race([scriptLoadPromise, timeoutPromise])
        .catch(error => {
          console.error('Login: Error or timeout loading Bedrock script:', error);
          throw error;
        });
      
      console.log('Login: Bedrock dependencies ready.');
      initializeBedrockWidget();
    } catch (error) {
      console.error('Login: Error loading Bedrock dependencies:', error);
      const widgetContainer = document.getElementById('bedrock-login-widget');
      if (widgetContainer) {
        widgetContainer.innerHTML = '<p style="color: red; text-align: center;">Error: Could not load authentication service. Please refresh the page or try again later.</p>';
      }
    }
  };

  // Helper function to load scripts
  const loadScript = (src: string, scriptName: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`script[src="${src}"]`)) {
        console.log(`Login: Script ${scriptName} (${src}) already loaded.`);
        resolve();
        return;
      }
      
      // First add a preload hint to tell the browser to start downloading the script
      const preloadLink = document.createElement('link');
      preloadLink.rel = 'preload';
      preloadLink.href = src;
      preloadLink.as = 'script';
      document.head.appendChild(preloadLink);
      
      // Then create and add the actual script
      const script = document.createElement('script');
      script.src = src;
      script.async = true; // Change to true for non-blocking loading
      script.onload = () => {
        console.log(`Login: Loaded script ${scriptName}: ${src}`);
        resolve();
      };
      script.onerror = (error) => {
        console.error(`Login: Error loading script ${scriptName} (${src}):`, error);
        reject(error);
      };
      document.body.appendChild(script);
    });
  };

  // Initialize the Bedrock widget
  const initializeBedrockWidget = () => {
    if (bedrockWidgetInitialized) return;
    console.log('Login: Initializing Bedrock widget actual render.');

    if (!window.React || !window.ReactDOM || !window.ReactDOM.createRoot) {
      console.error('Login: Required libraries (React, ReactDOM, createRoot) not available on window.');
      const widgetContainer = document.getElementById('bedrock-login-widget');
      if (widgetContainer) {
        widgetContainer.innerHTML = '<p style="color: red; text-align: center;">Error: Could not load authentication service. Please try again later.</p>';
      }
      return;
    }

    if (!window.Bedrock) {
      console.error('Login: Bedrock not available on window for widget rendering.');
      // This case should ideally be caught by initializeBedrockDependencies, but as a safeguard:
      const widgetContainer = document.getElementById('bedrock-login-widget');
      if (widgetContainer) {
        widgetContainer.innerHTML = '<p style="color: red; text-align: center;">Authentication service failed to load. Please refresh.</p>';
      }
      return;
    }

    const container = document.getElementById('bedrock-login-widget');
    if (!container) {
      console.error('Login: Login widget container (#bedrock-login-widget) not found in the DOM.');
      return;
    }
    
    // Mark as initialized before rendering to prevent React warnings about state updates during render
    setBedrockWidgetInitialized(true);
    
    // Clear container (React will handle this, but just to be safe)
    // Note: We're not using innerHTML = '' anymore to avoid flickering
    
    try {
      console.log('Login: Creating root and rendering widget with ReactDOM');
      // Use a setTimeout to allow the React state update to complete first
      setTimeout(() => {
        // Double-check that the container still exists
        if (!document.getElementById('bedrock-login-widget')) {
          console.error('Login: Widget container removed before rendering');
          return;
        }
        
        const root = window.ReactDOM.createRoot(container);
        root.render(
          window.React.createElement(
            window.Bedrock.BedrockPassportProvider,
            bedrockConfig,
            window.React.createElement(window.Bedrock.LoginPanel, {
              title: 'Sign in',
              logo: '/assets/textures/EyeOfHorus.png',
              logoAlt: 'SpecterShift Hunters',
              walletButtonText: 'Connect Wallet',
              showConnectWallet: true,
              separatorText: 'OR',
              features: {
                enableWalletConnect: true,
                enableAppleLogin: true,
                enableGoogleLogin: true,
                enableEmailLogin: true,
              },
              titleClass: 'text-xl font-bold',
              logoClass: 'ml-2 md:h-8 h-6',
              panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
              buttonClass: 'hover:border-violet-500',
              separatorTextClass: 'text-gray-400',
              separatorClass: 'bg-transparent',
              linkRowClass: 'justify-center',
              headerClass: 'justify-center',
            })
          )
        );
        console.log('Login: Bedrock widget initialized successfully');
      }, 0);
    } catch (error) {
      console.error('Login: Error initializing or rendering Bedrock Passport widget:', error);
      container.innerHTML = '<p style="color: red; text-align: center;">Error: Could not display authentication options. Please refresh the page.</p>';
      setBedrockWidgetInitialized(false); // Reset state on error
    }
  };

  // Conditional rendering based on auth loading state
  if (isAuthLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-purple-900 via-indigo-900 to-black flex justify-center items-center">
        <div className="text-white text-lg">Loading Authentication...</div>
      </div>
    );
  }

  // Show the login page content - redirection is now handled by AuthAwareRoute
  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-900 via-indigo-900 to-black flex flex-col items-center justify-center p-4">
      <div className="text-center mb-8">
        <h1 className="title-font text-4xl md:text-6xl text-white font-extrabold tracking-wider">
          SPECTERSHIFT<br />
          <span className="text-blue-400">HUNTERS</span>
        </h1>
        <p className="mt-4 text-gray-300">Sign in to start hunting</p>
      </div>
      
      <div id="bedrock-login-widget" className="w-full max-w-md bg-gray-800/50 p-6 rounded-lg shadow-xl">
        {/* Show loading indicator until widget is initialized */}
        {!bedrockWidgetInitialized && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-blue-300">Loading login options...</p>
          </div>
        )}
      </div>
      
      <div className="mt-8 text-center text-gray-400 text-sm">
        <p>By signing in, you agree to our Terms of Service and Privacy Policy</p>
      </div>
    </div>
  );
};

// Add TypeScript interfaces for window globals
declare global {
  interface Window {
    React: any;
    ReactDOM: any;
    Bedrock: any;
  }
}

export default Login; 