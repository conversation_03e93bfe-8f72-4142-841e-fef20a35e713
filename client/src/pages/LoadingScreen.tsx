import { Progress } from "@/components/ui/progress";
import { useEffect, useState } from "react";

const LoadingScreen = () => {
  const [progress, setProgress] = useState(0);
  const [forceComplete, setForceComplete] = useState(false);

  useEffect(() => {
    // Normal loading progression
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 5;
      });
    }, 150);

    // Failsafe - force complete after 5 seconds no matter what
    const failsafeTimer = setTimeout(() => {
      if (progress < 100) {
        console.warn("Loading timeout reached, forcing completion");
        setForceComplete(true);
        setProgress(100);
      }
    }, 5000);

    return () => {
      clearInterval(interval);
      clearTimeout(failsafeTimer);
    };
  }, [progress]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-900 via-indigo-900 to-black flex flex-col items-center justify-center">
      <h1 className="title-font text-4xl md:text-6xl mb-16 text-white text-center font-extrabold tracking-wider">
        SHATTERSHIFT<br />
        <span className="text-blue-400">HUNTERS</span>
      </h1>

      <div className="relative w-64 h-4 mb-4 mx-auto">
        <div className="absolute inset-0 border-2 border-blue-400 rounded-full overflow-hidden">
          <Progress value={progress} className="h-full bg-transparent" />
        </div>
      </div>

      <div className="text-blue-400 text-sm tracking-widest title-font">
        {progress < 100 && !forceComplete ? "LOADING SHATTERSCAPES..." : "READY"}
      </div>

      <div className="absolute bottom-10 w-full max-w-md px-8">
        <div className="text-gray-400 text-xs text-center italic">
          "The fractured multiverse awaits its hunters..."
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;