import { useEffect, useState } from 'react';
import { preloadCriticalAssets } from '@/utils/assetPreloader';
import { useLocation } from 'wouter';
import DualLoginButton from '@/components/DualLoginButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { GameButton } from '@/components/ui/GameButton';
import { IconButton } from '@/components/ui/IconButton';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PVPArenaDialog from '@/components/PVPArenaDialog';
import PVPRequirementDialog from '@/components/PVPRequirementDialog';
import StatusIssuesDialog from '@/components/StatusIssuesDialog';
import AuthProtection from '@/components/AuthProtection';
import {
  Info,
  PlayCircle,
  AlertTriangle,
  Users,
  Trophy,
  Swords,
  UserPlus,
  Search,
  Loader2,
  RefreshCw,
  Plus,
  UserCircle,
  Beaker
} from 'lucide-react';
import { GameMode, MessageType } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { usePvpAccess } from '@/contexts/PvpAccessContext';
import { useAuth } from '@/contexts/AuthContext';

const Home = () => {
  const [, setLocation] = useLocation();
  const [showInfo, setShowInfo] = useState(false);
  const [showStatusIssues, setShowStatusIssues] = useState(false);
  const [showMultiplayer, setShowMultiplayer] = useState(false);
  const [showPVPArena, setShowPVPArena] = useState(false);
  const [showPVPRequirement, setShowPVPRequirement] = useState(false);
  const [multiplayerTab, setMultiplayerTab] = useState("join");
  const [playerName, setPlayerName] = useState("");
  const [gameCode, setGameCode] = useState("");
  const [gameMode, setGameMode] = useState<GameMode>(GameMode.CoOp);
  const [isLoading, setIsLoading] = useState(false);
  const [assetsPreloaded, setAssetsPreloaded] = useState(false);
  const { toast } = useToast();
  const { isPvpEnabled } = usePvpAccess();
  const { isLoggedIn, currentUser } = useAuth();

  // Set player name from user profile if available
  useEffect(() => {
    // Check localStorage directly for authentication
    const checkAuth = () => {
      const storedUser = localStorage.getItem('orangeIDUser');
      const walletAddress = localStorage.getItem('walletAddress');
      
      // If we have stored credentials but auth context says we're not logged in,
      // there's an inconsistency that needs to be fixed
      if ((storedUser && storedUser !== 'undefined' && storedUser !== 'null') || walletAddress) {
        if (!isLoggedIn) {
          console.log('Home: Found user in localStorage but isLoggedIn is false. Triggering auth event.');
          // Try to dispatch an auth event to restore the session
          try {
            window.dispatchEvent(new Event('auth-login-success'));
            // Only reload if absolutely necessary
            setTimeout(() => {
              if (!isLoggedIn) {
                console.log('Home: Auth context still not updated. Reloading page to restore session.');
                window.location.reload();
              }
            }, 1000);
          } catch (e) {
            console.error('Home: Failed to dispatch auth event', e);
            // Fallback to reload
            window.location.reload();
          }
          return;
        }
      }
    };
    
    // Check auth status immediately
    checkAuth();
    
    if (currentUser) {
      // Use displayName, name, or email in that order of preference
      const name = currentUser.displayName || currentUser.name || (currentUser.email ? currentUser.email.split('@')[0] : '');
      if (name) {
        setPlayerName(name);
      }
    }
  }, [currentUser, isLoggedIn]);
  
  // Preload assets when component mounts
  useEffect(() => {
    // Start preloading assets in the background
    preloadCriticalAssets()
      .then(() => {
        console.log('Assets preloaded successfully');
        setAssetsPreloaded(true);
      })
      .catch(error => {
        console.warn('Asset preloading had some issues:', error);
        // Still mark as preloaded to not block the user
        setAssetsPreloaded(true);
      });
  }, []);

  const handleStartSinglePlayer = () => {
    // Store game mode in session storage
    sessionStorage.setItem('gameMode', GameMode.SinglePlayer);
    setLocation('/game');
  };

  // Additional state for game creation
  const [gameName, setGameName] = useState("");
  const [createTeamName, setCreateTeamName] = useState("");
  const [createTeamColor, setCreateTeamColor] = useState("#3b82f6"); // Default blue
  const [maxPlayers, setMaxPlayers] = useState(4);

  // Handle create multiplayer game
  const handleCreateMultiplayerGame = async () => {
    if (!playerName.trim()) {
      toast({
        title: "Player name required",
        description: "Please enter your player name to create a game.",
        variant: "destructive"
      });
      return;
    }

    // For PvP mode, require team name
    if (gameMode === GameMode.PvP && !createTeamName.trim()) {
      toast({
        title: "Team name required",
        description: "Please enter a team name for PvP mode.",
        variant: "destructive"
      });
      return;
    }

    // Auto-generate game name if not provided
    const effectiveGameName = gameName.trim() || `${playerName}'s Game`;

    setIsLoading(true);

    try {
      // Create a new game arena via API
      const arenaResponse = await fetch('/api/arenas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: effectiveGameName,
          mode: gameMode,
          maxPlayers: maxPlayers,
          settings: {
            // Additional game settings can go here
          }
        }),
      });

      if (!arenaResponse.ok) {
        throw new Error('Failed to create game');
      }

      const arena = await arenaResponse.json();

      // For PvP mode, create initial team
      if (gameMode === GameMode.PvP) {
        const teamResponse = await fetch(`/api/arenas/${arena.id}/teams`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: createTeamName,
            color: createTeamColor,
          }),
        });

        if (!teamResponse.ok) {
          throw new Error('Failed to create team');
        }
      }

      // Store game information in session storage
      sessionStorage.setItem('gameMode', gameMode);
      sessionStorage.setItem('playerName', playerName);
      sessionStorage.setItem('gameName', effectiveGameName);
      sessionStorage.setItem('arenaId', String(arena.id));
      sessionStorage.setItem('isHost', 'true');

      // For PvP, also save team info
      if (gameMode === GameMode.PvP) {
        sessionStorage.setItem('teamName', createTeamName);
        sessionStorage.setItem('teamColor', createTeamColor);
      }

      toast({
        title: "Game created!",
        description: `${gameMode.toUpperCase()} game "${effectiveGameName}" created. Starting game...`,
      });

      // Navigate to the game page
      setLocation('/game');
    } catch (error) {
      console.error('Error creating game:', error);
      toast({
        title: "Error",
        description: "Failed to create game. Please try again.",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  };

  // Types for available games and teams
  type AvailableGame = {
    id: number;
    name: string;
    mode: GameMode;
    players: number;
    maxPlayers: number;
    status: string;
  };

  type AvailableTeam = {
    id: number;
    name: string;
    color: string;
    playerCount: number;
    gameId: number;
    gameName: string;
  };

  // State for available games and teams
  const [availableGames, setAvailableGames] = useState<AvailableGame[]>([]);
  const [availableTeams, setAvailableTeams] = useState<AvailableTeam[]>([]);
  const [selectedGameId, setSelectedGameId] = useState<number | null>(null);
  const [selectedTeamId, setSelectedTeamId] = useState<number | null>(null);
  const [isRefreshingGames, setIsRefreshingGames] = useState(false);
  const [showNewTeamForm, setShowNewTeamForm] = useState(false);
  const [newTeamName, setNewTeamName] = useState("");
  const [newTeamColor, setNewTeamColor] = useState("#3b82f6"); // Default blue

  // Sample team colors for quick selection
  const teamColors = [
    { name: "Blue", value: "#3b82f6" },
    { name: "Red", value: "#ef4444" },
    { name: "Green", value: "#22c55e" },
    { name: "Purple", value: "#a855f7" },
    { name: "Orange", value: "#f97316" },
  ];

  // Mock function to fetch available games - in production this would be a real API call
  const fetchAvailableGames = async () => {
    setIsRefreshingGames(true);

    try {
      // Fetch available games from the API
      const gamesResponse = await fetch('/api/arenas?status=waiting');

      if (!gamesResponse.ok) {
        throw new Error('Failed to fetch available games');
      }

      const games = await gamesResponse.json();

      // Transform the API response to match our component's expected format
      const availableGames: AvailableGame[] = games.map((game: any) => ({
        id: game.id,
        name: game.name,
        mode: game.mode,
        players: game.playerCount || 0,
        maxPlayers: game.maxPlayers,
        status: game.status
      }));

      // Fetch teams for PvP games
      const teamsPromises = availableGames
        .filter(game => game.mode === GameMode.PvP)
        .map(game =>
          fetch(`/api/arenas/${game.id}/teams`)
            .then(res => res.ok ? res.json() : [])
            .then(teams => teams.map((team: any) => ({
              id: team.id,
              name: team.name,
              color: team.color,
              playerCount: 0, // Players count will be handled server-side
              gameId: game.id,
              gameName: game.name
            })))
        );

      const teamsArrays = await Promise.all(teamsPromises);
      const availableTeams = teamsArrays.flat();

      setAvailableGames(availableGames);
      setAvailableTeams(availableTeams);
    } catch (error) {
      console.error('Error fetching games:', error);
      toast({
        title: "Error",
        description: "Failed to fetch available games. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshingGames(false);
    }
  };

  // Fetch available games when the dialog opens
  useEffect(() => {
    if (showMultiplayer && multiplayerTab === "join") {
      fetchAvailableGames();
    }
  }, [showMultiplayer, multiplayerTab]);

  // Handle join game - this is called when joining an existing game
  const handleJoinMultiplayerGame = async () => {
    if (!playerName.trim()) {
      toast({
        title: "Player name required",
        description: "Please enter your player name to join a game.",
        variant: "destructive"
      });
      return;
    }

    if (!selectedGameId && !selectedTeamId) {
      toast({
        title: "Selection required",
        description: "Please select a game or team to join.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Determine the game mode and ID based on selection
      let gameId = selectedGameId;
      let teamId = null;

      // If a team was selected, use its game ID
      if (selectedTeamId) {
        const team = availableTeams.find(t => t.id === selectedTeamId);
        if (team) {
          gameId = team.gameId;
          teamId = team.id;
        }
      }

      if (!gameId) {
        throw new Error('Game ID not found');
      }

      // Get game details to determine mode
      const gameResponse = await fetch(`/api/arenas/${gameId}`);
      if (!gameResponse.ok) {
        throw new Error('Failed to fetch game details');
      }

      const game = await gameResponse.json();

      // Store multiplayer game options in session storage
      sessionStorage.setItem('gameMode', game.mode);
      sessionStorage.setItem('playerName', playerName);
      sessionStorage.setItem('arenaId', String(gameId));
      sessionStorage.setItem('isHost', 'false');

      if (teamId) {
        sessionStorage.setItem('teamId', String(teamId));
      }

      toast({
        title: "Joining game",
        description: `Joining ${game.name}...`,
      });

      // Navigate to the game page
      setLocation('/game');
    } catch (error) {
      console.error('Error joining game:', error);
      toast({
        title: "Error",
        description: "Failed to join game. Please try again.",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  };

  // Handle create new team in an existing game
  const handleCreateTeamInGame = (gameId: number) => {
    if (!playerName.trim()) {
      toast({
        title: "Player name required",
        description: "Please enter your player name first.",
        variant: "destructive"
      });
      return;
    }

    if (!newTeamName.trim()) {
      toast({
        title: "Team name required",
        description: "Please enter a name for your team.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    // Set multiplayer game options in session storage
    const game = availableGames.find(g => g.id === gameId);
    if (game) {
      sessionStorage.setItem('gameMode', game.mode);
      sessionStorage.setItem('playerName', playerName);
      sessionStorage.setItem('isHost', 'false');
      sessionStorage.setItem('gameId', String(gameId));
      sessionStorage.setItem('newTeamName', newTeamName);
      sessionStorage.setItem('newTeamColor', newTeamColor);
    }

    // Simulate API call to create team and join game
    setTimeout(() => {
      setIsLoading(false);
      setShowNewTeamForm(false);
      toast({
        title: "Team created!",
        description: `Created team "${newTeamName}" and joined game. Starting game...`,
      });
      setLocation('/game');
    }, 1000);
  };

  const toggleStatusIssues = () => {
    setShowStatusIssues(!showStatusIssues);
  };

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleMultiplayer = () => {
    setShowMultiplayer(!showMultiplayer);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-900 via-indigo-900 to-black flex flex-col items-center justify-center relative">
      {/* Auth protection - redirects to login if not authenticated */}
      <AuthProtection />
      
      {/* Top Banner */}
      <div className="absolute top-0 left-0 w-full h-20 flex items-center bg-black/30 border-b border-blue-400/20 px-4">
        <div className="flex-1 flex justify-center">
          <img src="/assets//textures/topsponsor.webp" alt="Top Sponsor" className="h-full object-contain" style={{ maxWidth: '600px', maxHeight: '75px' }} />
        </div>
        <div className="flex items-center">
          <DualLoginButton />
        </div>
      </div>

      {/* Side Banners */}
      <div className="absolute left-0 top-1/2 -translate-y-1/2 w-32 h-64 flex justify-center items-center bg-black/30 border-r border-blue-400/20">
        <img src="/assets//textures/wisp.png" alt="Left Sponsor" className="w-full h-full object-contain" />
      </div>
      <div className="absolute right-0 top-1/2 -translate-y-1/2 w-32 h-64 flex justify-center items-center bg-black/30 border-l border-blue-400/20">
        <img src="/assets//textures/wisp.png" alt="Right Sponsor" className="w-full h-full object-contain" />
      </div>

      <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20 pointer-events-none">
        {/* Animated floating particles */}
        {Array.from({ length: 100 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 rounded-full bg-blue-400 animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              boxShadow: '0 0 4px #4299e1',
              animation: `float ${Math.random() * 10 + 5}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`
            }}
          />
        ))}
      </div>

      <h1 className="title-font text-4xl md:text-6xl mb-8 text-white text-center font-extrabold tracking-wider">
        SPECTERSHIFT<br />
        <span className="text-blue-400">HUNTERS</span>
      </h1>

      <div className="flex flex-col items-center gap-4 max-w-md w-full px-4">
        <GameButton
          onClick={handleStartSinglePlayer}
          label="SINGLE PLAYER"
          icon={<PlayCircle className="h-5 w-5" />}
          primary
        />

        <div className="flex gap-4">
          <GameButton
            disabled={!isPvpEnabled}
            onClick={() => setShowPVPRequirement(true)}
            label={isPvpEnabled ? "PVP ARENA" : "PVP COMING SOON (WIP)"}
            icon={<Swords className="h-5 w-5" />}
          />
        </div>

        <GameButton
          onClick={toggleStatusIssues}
          label="STATUS/KNOWN ISSUES"
          icon={<AlertTriangle className="h-5 w-5" />}
        />

        <div className="flex justify-center mt-2">
          <IconButton
            onClick={toggleInfo}
            aria-label="Game Information"
            icon={<Info className="h-5 w-5" />}
          />
        </div>
      </div>

      {/* Status/Known Issues Dialog */}
      <StatusIssuesDialog
        isOpen={showStatusIssues}
        onClose={() => setShowStatusIssues(false)}
      />

      {/* PVP Requirement Dialog */}
      <PVPRequirementDialog
        isOpen={showPVPRequirement}
        onClose={() => setShowPVPRequirement(false)}
        onProceed={() => {
          setShowPVPRequirement(false);
          setShowPVPArena(true);
        }}
      />

      {/* PVP Arena Dialog */}
      <PVPArenaDialog
        isOpen={showPVPArena}
        onClose={() => setShowPVPArena(false)}
        onJoinTournament={() => {
          // Only navigate to the game when joining as a spectator to an active battle
          setShowPVPArena(false);
          // Navigate to the game page which will load the Coliseum Arena in spectator mode
          setLocation('/game');
        }}
      />

      {/* Multiplayer Dialog */}
      <Dialog open={showMultiplayer} onOpenChange={setShowMultiplayer}>
        <DialogContent className="sm:max-w-md bg-gray-900 border-blue-400 text-white">
          <DialogHeader>
            <DialogTitle className="text-xl text-blue-400">Coliseum Arena</DialogTitle>
          </DialogHeader>

          <Tabs value={multiplayerTab} onValueChange={setMultiplayerTab}>
            <TabsList className="w-full grid grid-cols-2 bg-gray-800">
              <TabsTrigger value="join">Join Tournament</TabsTrigger>
              <TabsTrigger value="create">Create Tournament</TabsTrigger>
            </TabsList>

            <TabsContent value="join" className="mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Your Name</label>
                  <Input
                    placeholder="Enter your name"
                    value={playerName}
                    onChange={(e) => setPlayerName(e.target.value)}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>

                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">Available Games & Teams</label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={fetchAvailableGames}
                    disabled={isRefreshingGames}
                    className="h-8"
                  >
                    {isRefreshingGames ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {/* Available games */}
                <div className="max-h-60 overflow-y-auto pr-1 space-y-2">
                  {/* Teams section */}
                  {availableTeams.length > 0 && (
                    <div className="mb-4">
                      <h3 className="text-xs uppercase text-blue-400 mb-2">Available Teams</h3>
                      <RadioGroup value={selectedTeamId?.toString() || ""} onValueChange={(value) => {
                        setSelectedTeamId(Number(value));
                        setSelectedGameId(null); // Deselect game when selecting team
                      }}>
                        {availableTeams.map(team => (
                          <div key={team.id} className="flex items-center space-x-2 py-2 px-3 rounded-md hover:bg-gray-800 cursor-pointer">
                            <RadioGroupItem value={team.id.toString()} id={`team-${team.id}`} />
                            <div className="flex-1">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-2"
                                  style={{ backgroundColor: team.color }}
                                ></div>
                                <label
                                  htmlFor={`team-${team.id}`}
                                  className="text-sm font-medium cursor-pointer"
                                >
                                  {team.name}
                                </label>
                              </div>
                              <div className="text-xs text-gray-400">
                                In: {team.gameName} • {team.playerCount} players
                              </div>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  )}

                  {/* Games section */}
                  <div>
                    <h3 className="text-xs uppercase text-blue-400 mb-2">Games</h3>
                    <RadioGroup value={selectedGameId?.toString() || ""} onValueChange={(value) => {
                      setSelectedGameId(Number(value));
                      setSelectedTeamId(null); // Deselect team when selecting game
                    }}>
                      {availableGames.map(game => (
                        <div key={game.id} className="relative py-2 px-3 rounded-md hover:bg-gray-800">
                          <div className="flex items-center space-x-2 cursor-pointer">
                            <RadioGroupItem value={game.id.toString()} id={`game-${game.id}`} />
                            <div className="flex-1">
                              <label
                                htmlFor={`game-${game.id}`}
                                className="text-sm font-medium cursor-pointer flex items-center"
                              >
                                {game.name}
                                <Badge
                                  className="ml-2 bg-red-700 hover:bg-red-700"
                                >
                                  TOURNAMENT
                                </Badge>
                              </label>
                              <div className="text-xs text-gray-400">
                                Players: {game.players}/{game.maxPlayers}
                              </div>
                            </div>

                            {/* Create team button for PVP games */}
                            {game.mode === GameMode.PvP && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setSelectedGameId(game.id);
                                  setShowNewTeamForm(true);
                                }}
                                className="text-xs"
                              >
                                <Plus className="h-3.5 w-3.5 mr-1" />
                                Team
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>

                  {availableGames.length === 0 && !isRefreshingGames && (
                    <div className="text-center py-8 text-gray-500">
                      <UserCircle className="h-10 w-10 mx-auto mb-2 opacity-30" />
                      <p className="text-sm">No games available to join</p>
                      <p className="text-xs mt-1">Try refreshing or create your own game</p>
                    </div>
                  )}
                </div>

                {/* Create Team Form for PVP */}
                {showNewTeamForm && (
                  <div className="border border-gray-700 rounded-md p-3 mt-4 space-y-3 bg-gray-800/50">
                    <h3 className="text-sm font-medium text-blue-400">Create New Team</h3>

                    <div className="space-y-2">
                      <label className="text-xs font-medium">Team Name</label>
                      <Input
                        placeholder="Enter team name"
                        value={newTeamName}
                        onChange={(e) => setNewTeamName(e.target.value)}
                        className="bg-gray-800 border-gray-700 h-8 text-sm"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs font-medium">Team Color</label>
                      <div className="flex gap-1">
                        {teamColors.map(color => (
                          <div
                            key={color.value}
                            className={`w-6 h-6 rounded-full cursor-pointer transition-all ${
                              newTeamColor === color.value
                                ? 'ring-2 ring-white'
                                : 'hover:ring-1 hover:ring-gray-400'
                            }`}
                            style={{ backgroundColor: color.value }}
                            onClick={() => setNewTeamColor(color.value)}
                            title={color.name}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-1">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => setShowNewTeamForm(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                        onClick={() => handleCreateTeamInGame(selectedGameId!)}
                        disabled={isLoading}
                      >
                        {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Create & Join'}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Join button */}
                {!showNewTeamForm && (
                  <Button
                    onClick={handleJoinMultiplayerGame}
                    disabled={isLoading || (!selectedGameId && !selectedTeamId)}
                    className="w-full bg-blue-500 hover:bg-blue-600"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Joining...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        {selectedTeamId ? 'Join Team' : 'Join Game'}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </TabsContent>

            <TabsContent value="create" className="mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Your Name</label>
                  <Input
                    placeholder="Enter your name"
                    value={playerName}
                    onChange={(e) => setPlayerName(e.target.value)}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Game Name (optional)</label>
                  <Input
                    placeholder="Enter game name"
                    value={gameName}
                    onChange={(e) => setGameName(e.target.value)}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>

                {/* Game mode is always PvP for tournaments */}
                <input type="hidden" value={GameMode.PvP} onChange={() => setGameMode(GameMode.PvP)} />

                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Players</label>
                  <Select
                    value={maxPlayers.toString()}
                    onValueChange={(value) => setMaxPlayers(Number(value))}
                  >
                    <SelectTrigger className="bg-gray-800 border-gray-700">
                      <SelectValue placeholder="Maximum players" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700 text-white">
                      {[2, 4, 6, 8, 12].map(num => (
                        <SelectItem key={num} value={num.toString()}>
                          {num} players
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Team settings for PvP mode */}
                {gameMode === GameMode.PvP && (
                  <div className="border border-gray-700 rounded-md p-3 mt-2 bg-gray-800/30 space-y-3">
                    <h3 className="text-sm font-medium text-blue-400">Your Team</h3>

                    <div className="space-y-2">
                      <label className="text-xs font-medium">Team Name</label>
                      <Input
                        placeholder="Enter team name"
                        value={createTeamName}
                        onChange={(e) => setCreateTeamName(e.target.value)}
                        className="bg-gray-800 border-gray-700 h-8 text-sm"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs font-medium">Team Color</label>
                      <div className="flex gap-1">
                        {teamColors.map(color => (
                          <div
                            key={color.value}
                            className={`w-6 h-6 rounded-full cursor-pointer transition-all ${
                              createTeamColor === color.value
                                ? 'ring-2 ring-white'
                                : 'hover:ring-1 hover:ring-gray-400'
                            }`}
                            style={{ backgroundColor: color.value }}
                            onClick={() => setCreateTeamColor(color.value)}
                            title={color.name}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleCreateMultiplayerGame}
                  disabled={isLoading}
                  className="w-full bg-blue-500 hover:bg-blue-600 mt-2"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Create Game
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Game Info Card */}
      {showInfo && (
        <Card className="mt-8 max-w-md w-full mx-4 bg-opacity-80 bg-gray-900 text-white border-blue-400">
          <CardContent className="pt-6">
            <h2 className="title-font text-xl mb-4 text-blue-400">Game Instructions</h2>
            <ul className="space-y-2 text-sm">
              <li><span className="font-bold">Movement:</span> WASD keys</li>
              <li><span className="font-bold">Look:</span> Mouse movement</li>
              <li><span className="font-bold">Jump:</span> Space bar</li>
              <li><span className="font-bold">Jetpack:</span> Hold Shift</li>
              <li><span className="font-bold">Fire Rifle:</span> Left mouse button</li>
              <li><span className="font-bold">Switch Ammo:</span> 1, 2, 3 keys</li>
              <li><span className="font-bold">Switch Songs:</span> +/-</li>
              <li><span className="font-bold">Objective:</span> Capture the ethereal Specters to instantly send them back to their dimension by using your Shattershift Rifle's Phase Shift ammo, plus get or generate a pet specter to help!</li>
            </ul>

            <h3 className="title-font text-md mt-4 mb-2 text-blue-400">Multiplayer Modes (WIP - COMING SOON)</h3>
            <ul className="space-y-2 text-sm">
              <li><span className="font-bold">Co-op:</span> Work together to eliminate specters and progress through increasingly challenging levels</li>
              <li><span className="font-bold">PvP:</span> Compete against other players to eliminate the most specters - use powerups to boost your Pet</li>
            </ul>

            <Button
              className="mt-4 w-full bg-blue-500 hover:bg-blue-600"
              onClick={toggleInfo}
            >
              Close
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="absolute bottom-2 text-gray-400 text-xs">
        © 2025 Specter Hunters <a href="/sponsors" className="text-blue-400 hover:underline">Sponsor Inquiries</a> - Background Tracks Made With Riffusion.com
      </div>

      <style>
        {`
          @keyframes float {
            0% {
              transform: translateY(0px);
              opacity: 0;
            }
            20% {
              opacity: 1;
            }
            80% {
              opacity: 1;
            }
            100% {
              transform: translateY(-1000px);
              opacity: 0;
            }
          }

          .animate-float {
            animation: float 10s linear infinite;
          }
        `}
      </style>
    </div>
  );
};

export default Home;