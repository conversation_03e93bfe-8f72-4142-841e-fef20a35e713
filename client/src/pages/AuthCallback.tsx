import React, { useEffect, useState, useRef } from 'react';
import { useLocation } from 'wouter';
import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';

/**
 * The AuthCallback component handles the OAuth callback from OrangeID.
 * This implementation uses a simple approach to process the authentication
 * and relies on localStorage changes to detect success.
 */
const AuthCallback: React.FC = () => {
  const [, setLocation] = useLocation();
  const [message, setMessage] = useState('Processing login...');
  const [error, setError] = useState<string | null>(null);

  // Load the Bedrock Passport script if not already loaded
  const loadBedrockScript = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.Bedrock && window.Bedrock.useBedrockPassport) {
        console.log('Auth: Bedrock already loaded');
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://public-cdn-files.pages.dev/bedrock-passport.umd.js';
      script.async = true;
      script.id = 'bedrock-passport-script';

      script.onload = () => {
        console.log('Auth: Bedrock script loaded successfully');
        resolve();
      };

      script.onerror = (err) => {
        console.error('Auth: Failed to load Bedrock script', err);
        reject(new Error('Failed to load authentication services'));
      };

      document.head.appendChild(script);
    });
  };

  useEffect(() => {
    let isMounted = true;
    let authCheckTimer: number | null = null;

    const processAuth = async () => {
      try {
        // Get tokens from URL
        const params = new URLSearchParams(window.location.search);
        const token = params.get('token');
        const refreshToken = params.get('refreshToken');

        if (!token || !refreshToken) {
          setError('Missing authentication tokens');
          setMessage('Authentication failed');
          return;
        }

        setMessage('Initializing authentication service...');
        await loadBedrockScript();

        // Create a temporary container for the Bedrock component
        const tempContainer = document.createElement('div');
        tempContainer.id = 'bedrock-auth-temp';
        document.body.appendChild(tempContainer);

        setMessage('Processing authentication...');

        // Check if we have a user in localStorage before we start
        const initialUserString = localStorage.getItem('orangeIDUser');

        // Simple script to process the auth - no complex callbacks needed
        const script = document.createElement('script');
        script.textContent = `
          (function() {
            try {
              // Bedrock config - following Orange ID integration standard
              const bedrockConfig = {
                baseUrl: 'https://api.bedrockpassport.com',
                authCallbackUrl: window.location.origin + '/auth/callback',
                tenantId: 'orange-yn4vaq0i6w',
                subscriptionKey: '1c533c7334df48b88f7d85c361c10015'
              };

              // Create the root and render
              const root = ReactDOM.createRoot(document.getElementById('bedrock-auth-temp'));

              // Helper component to process the login
              function LoginProcessor() {
                const { loginCallback, user } = Bedrock.useBedrockPassport();

                React.useEffect(() => {
                  async function processLogin() {
                    try {
                      // Call the login callback
                      const success = await loginCallback("${token}", "${refreshToken}");

                      if (success && user) {
                        // Save user to localStorage
                        localStorage.setItem('orangeIDUser', JSON.stringify(user));
                        localStorage.setItem('orangeIDToken', "${token}");
                        localStorage.setItem('orangeIDRefreshToken', "${refreshToken}");

                        // Dispatch success event
                        window.dispatchEvent(new Event('auth-login-success'));
                        console.log('Auth: Login successful, data saved to localStorage');
                      } else {
                        console.error('Auth: Login callback failed or user data missing');
                      }
                    } catch (error) {
                      console.error('Auth: Error in login processor', error);
                    }
                  }

                  processLogin();
                }, [loginCallback, user]);

                return React.createElement('div', null);
              }

              // Render the provider and processor
              root.render(
                React.createElement(
                  Bedrock.BedrockPassportProvider,
                  bedrockConfig,
                  React.createElement(LoginProcessor)
                )
              );
            } catch (error) {
              console.error('Auth: Error in auth script', error);
            }
          })();
        `;

        // Add the script to the document
        document.body.appendChild(script);

        // Remove the script element (execution has already happened)
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }

        // Instead of using a callback, check localStorage periodically to detect success
        let attempts = 0;
        const maxAttempts = 30; // Check for up to 15 seconds

        const checkAuth = () => {
          attempts++;
          const currentUserString = localStorage.getItem('orangeIDUser');

          // Check if a new user was saved to localStorage
          if (currentUserString && currentUserString !== initialUserString) {
            console.log('Auth: Detected new user in localStorage, auth successful');

            // Clean up
            if (tempContainer && document.body.contains(tempContainer)) {
              document.body.removeChild(tempContainer);
            }

            if (isMounted) {
              setMessage('Login successful! Redirecting...');
              // Clean up URL parameters
              window.history.replaceState({}, document.title, window.location.pathname);

              // Redirect to root domain as per Orange ID standard
              // The main application will handle the redirect to /home
              setTimeout(() => {
                if (isMounted) {
                  window.location.href = '/';
                }
              }, 1000);
            }

            return;
          }

          // If we've reached the max attempts, show an error
          if (attempts >= maxAttempts) {
            if (isMounted) {
              setError('Authentication timed out');
              setMessage('Could not complete login process');
            }
            return;
          }

          // Continue checking
          authCheckTimer = window.setTimeout(checkAuth, 500);
        };

        // Start checking for auth success
        authCheckTimer = window.setTimeout(checkAuth, 1000);

      } catch (err: any) {
        if (!isMounted) return;

        console.error('Auth: Global error in auth callback', err);
        setError(err.message || 'An unexpected error occurred');
        setMessage('Authentication error');

        // Clean up any temporary elements
        const tempContainer = document.getElementById('bedrock-auth-temp');
        if (tempContainer && document.body.contains(tempContainer)) {
          document.body.removeChild(tempContainer);
        }
      }
    };

    processAuth();

    return () => {
      isMounted = false;
      if (authCheckTimer !== null) {
        window.clearTimeout(authCheckTimer);
      }

      // Clean up any temporary elements
      const tempContainer = document.getElementById('bedrock-auth-temp');
      if (tempContainer && document.body.contains(tempContainer)) {
        document.body.removeChild(tempContainer);
      }
    };
  }, [setLocation]);

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900 p-4">
        <div className="bg-red-800/30 p-8 rounded-lg text-center max-w-md">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Login Error</h1>
          <p className="text-white mb-2">{message}</p>
          <p className="text-gray-300 text-sm mb-6">{error}</p>
          <button
            onClick={() => setLocation('/')}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900">
      <MinimalLoadingIndicator size="large" />
      <p className="text-white mt-4 text-lg">{message}</p>
    </div>
  );
};

declare global {
  interface Window {
    React: any;
    ReactDOM: any;
    Bedrock: any;
  }
}

export default AuthCallback;