import { useEffect, useRef, useState, lazy, Suspense } from 'react';
import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { SpecterType } from '../game/types';
import { useToast } from '@/hooks/use-toast';
import { PetSpecter } from '../game/entities/PetSpecter';
import { DungeonDifficulty } from '../game/world/DungeonGenerator';
import { useMultiplayer } from '@/hooks/useMultiplayer';
import { GameMode, PlayerState } from '@shared/schema';
import { useIsMobile } from '@/hooks/use-mobile';
import NFTMintDialogProvider from '@/components/NFTMintDialogProvider';
import NFTBasedPetDialogProvider from '@/components/NFTBasedPetDialogProvider';
import AIPetGenerationDialogProvider from '@/components/AIPetGenerationDialogProvider';
import NFTBrowserDialogProvider from '@/components/NFTBrowserDialogProvider';

// Import THREE directly since it's used in non-component contexts
import * as THREE from 'three';
// Import GameEngine directly since it's not a React component
import GameEngine from '../game/engine/GameEngine';

// Lazy load UI components
const HUD = lazy(() => import('../game/ui/HUD'));
const Minimap = lazy(() => import('../game/ui/Minimap'));
const PetSpecterUI = lazy(() => import('../game/ui/PetSpecterUI'));
const DungeonUI = lazy(() => import('../game/ui/DungeonUI'));
const DungeonRewardsDialog = lazy(() => import('../game/ui/DungeonRewardsDialog'));
const MobileControls = lazy(() => import('../game/controls/MobileControls'));

// Extended GameState interface to include specters
interface ExtendedGameState {
  id: number;
  mode: GameMode;
  status: string;
  currentLevel: number;
  enemiesTotal: number;
  enemiesDefeated: number;
  players: PlayerState[];
  teams?: { id: number; name: string; color: string; score: number }[];
  specters?: Array<{
    id: number,
    position: { x: number, y: number, z: number },
    type?: SpecterType
  }>;
}

const Game = () => {
  const [, setLocation] = useLocation();
  const gameContainerRef = useRef<HTMLDivElement>(null);
  const [gameEngine, setGameEngine] = useState<any>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showGameOver, setShowGameOver] = useState(false);
  const [score, setScore] = useState(0);
  const [ammoType, setAmmoType] = useState<string>('Gravity Well');
  const [ammo, setAmmo] = useState<number[]>([5, 5, 5]); // Gravity, Time, Phase
  const [spectersCaptured, setSpectersCaptured] = useState<number>(0);
  const [health, setHealth] = useState<number>(100);
  const [jetpackFuel, setJetpackFuel] = useState<number>(100);
  const [showHUD, setShowHUD] = useState(true);
  // Pet Specters management
  const [petSpecters, setPetSpecters] = useState<PetSpecter[]>([]);
  const isMobile = useIsMobile();
  const [isPetSpecterUIOpen, setIsPetSpecterUIOpen] = useState(false);
  // Minimap data
  const [specterMapData, setSpecterMapData] = useState<{ id: number; position: { x: number; y: number; z: number }; color: string }[]>([]);
  const [playerPosition, setPlayerPosition] = useState<{ x: number; y: number; z: number } | null>(null);
  // Simple error state
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  // Homing missile state
  const [homingActive, setHomingActive] = useState(false);

  // Multiplayer state
  const [isMultiplayer, setIsMultiplayer] = useState(false);
  const [joinAttempted, setJoinAttempted] = useState(false);
  const [gameMode, setGameMode] = useState<string>('single');
  const [playerName, setPlayerName] = useState<string>('Player');
  const [isHost, setIsHost] = useState(false);
  const [players, setPlayers] = useState<PlayerState[]>([]);
  const [teams, setTeams] = useState<any[]>([]);
  const [messages, setMessages] = useState<any[]>([]);
  // Messages are used in the multiplayer chat system
  const [currentLevel, setCurrentLevel] = useState(1);

  // Dungeon state
  const [isInDungeon, setIsInDungeon] = useState(false);
  const [dungeonLevel, setDungeonLevel] = useState(1);
  const [dungeonDifficulty, setDungeonDifficulty] = useState<DungeonDifficulty>(DungeonDifficulty.EASY);
  const [roomsCleared, setRoomsCleared] = useState(0);
  const [totalRooms, setTotalRooms] = useState(0);
  const [bossDefeated, setBossDefeated] = useState(false);
  const [enemiesDefeated, setEnemiesDefeated] = useState(0);
  const [lootCollected, setLootCollected] = useState(0);
  const [showDungeonRewards, setShowDungeonRewards] = useState(false);
  const [dungeonRewards, setDungeonRewards] = useState<any>({ experience: 0, gold: 0, items: [] });
  const [arenaId, setArenaId] = useState<number | null>(null);
  const [localConnectionId, setLocalConnectionId] = useState<string | null>(null);

  const { toast } = useToast();

  // Set up multiplayer with real WebSocket connection - but only auto-connect if we're in multiplayer mode
  const multiplayer = useMultiplayer({
    // Fix the WebSocket URL to use the correct port (5001) and path (/game-ws) instead of relying on window.location.port
    url: `${window.location.protocol === 'https:' ? 'wss' : 'ws'}://${window.location.hostname}:5001/game-ws`,
    // Only auto-connect if we're in multiplayer mode
    autoConnect: false, // We'll manually connect only when needed
    onConnect: (connectionId) => {
      console.log(`Connected to game server with ID: ${connectionId}`);
      setLocalConnectionId(connectionId);
      toast({
        title: 'Connected',
        description: 'Connected to game server',
        duration: 3000
      });
    },
    onDisconnect: () => {
      console.log('Disconnected from game server');
      toast({
        title: 'Disconnected',
        description: 'Lost connection to game server',
        duration: 3000,
        variant: 'destructive'
      });
    },
    onPlayerJoin: (data) => {
      console.log('Player joined:', data);

      // If we have player data, update our players list
      if (data.player) {
        // Add player to the list, replacing existing entry if it exists
        setPlayers(prev => {
          console.log('Updating players list. Current:', prev, 'Adding:', data.player);
          return [...prev.filter(p => p.id !== data.player.id), data.player];
        });

        toast({
          title: 'Player Joined',
          description: `${data.player?.name || 'A player'} joined the game`,
          duration: 3000
        });

        // If game engine exists, ensure it's in network mode and create/update the player
        if (gameEngine) {
          console.log('Adding player to game engine:', data.player);
          gameEngine.setNetworkMode(true);
          gameEngine.updateOtherPlayer(data.player);
        }
      }
    },
    onPlayerLeave: (data) => {
      console.log('Player left:', data);
      toast({
        title: 'Player Left',
        description: `${data.playerName || 'A player'} left the game`,
        duration: 3000
      });

      // Remove player from our players list
      if (data.playerId) {
        setPlayers(prev => prev.filter(p => p.id !== data.playerId));

        // Also remove player from game engine if it exists
        if (gameEngine) {
          gameEngine.removeOtherPlayer(data.playerId);
        }
      }
    },
    onPlayerUpdate: (player) => {
      // Update player state in the game
      if (gameEngine) {
        // Send the updated player data to the game engine
        if (player.id !== localConnectionId) {
          gameEngine.updateOtherPlayer(player);
        } else {
           console.log("Received self-update, potentially ignoring:", player.id);
        }
      }

      // Update our players list
      setPlayers(prev => {
        const existingPlayerIndex = prev.findIndex(p => p.id === player.id);
        if (existingPlayerIndex >= 0) {
          const updatedPlayers = [...prev];
          updatedPlayers[existingPlayerIndex] = {
            ...updatedPlayers[existingPlayerIndex],
            ...player
          };
          return updatedPlayers;
        } else {
           // Optionally log only when a *new* player is added by an update message
           // console.log('Adding new player via PlayerUpdate:', player);
           return [...prev, player];
        }
      });
    },
    onGameStart: (gameState: ExtendedGameState, localId: string | null) => {
      // --- Debugging Log ---
      console.log(`>>> CRITICAL: onGameStart handler executed. Received gameState and localId: ${localId}`, JSON.stringify(gameState, null, 2));
      // --- End Debugging Log ---

      console.log('Game started (Existing Log):', gameState); // Keep existing log for comparison

      // First, ensure the game engine is in network mode
      if (gameEngine) {
        console.log('Setting game engine to network mode');
        gameEngine.setNetworkMode(true);
      }

      // Update game state in our React component
      if (gameState.players) {
        console.log('Updating players from game state:', gameState.players);
        // Ensure the local player is included in the state update
        // Use the localId passed directly to this handler
        let updatedPlayers = gameState.players;

        // Log the received players and the direct local ID for debugging
        console.log(`Received players: ${updatedPlayers.map(p => p.id).join(', ')}. Direct Local ID: ${localId}`);

        // Use the direct localId to check and potentially add the local player
        if (localId) {
          console.log(`Using direct local ID: ${localId}`);

          // Check if our local player exists in the list
          const localPlayerInList = updatedPlayers.find(p => p.id === localId);
          if (!localPlayerInList) {
            console.warn(`Local player ${localId} not found in initial game state players list using direct ID. Adding placeholder.`);
            // Add a placeholder for the local player
            updatedPlayers = [...updatedPlayers, {
              id: localId,
              name: playerName,
              position: { x: 0, y: 5, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              health: 100,
              jetpackFuel: 100,
              firing: false,
              ammoType: 'Gravity Well',
              score: 0
            }];
          } else {
            console.log(`Local player ${localId} found in game state players list using direct ID.`);
          }
        } else {
          console.warn('Direct Local ID is null WHEN PROCESSING GAME START, cannot verify local player in list.'); // Changed log message
        }

        setPlayers(updatedPlayers); // Update the React state
        console.log('setPlayers called with:', updatedPlayers); // Log the updated state

        // If the game engine exists, add all players to it
        if (gameEngine) {
          // Add each player except ourselves to the game engine
          gameState.players.forEach(player => {
            // Use the direct localId for the check
            if (player.id !== localId) {
              console.log('Adding/Updating other player in game engine from game state:', player);
              gameEngine.updateOtherPlayer(player);
            } else {
              console.log('Identified local player in game state using direct ID:', player);
              // Apply initial state ONLY if engine is new/reset
              if (gameEngine.isNewlyCreated) {
                console.log('Setting local player position from server (initial setup) using direct ID:', player.position);
                gameEngine.setPlayerPosition(player.position);
              } else {
                console.log('Local player already active, not overriding position from game start state.');
              }
            }
          });
        }
      } else {
        console.warn('GameStart message received without players data.');
        setPlayers([]); // Clear players if none received
      }

      if (gameState.teams) {
        setTeams(gameState.teams);
      }

      setCurrentLevel(gameState.currentLevel);

      // If the game state includes specters, update them in the game engine
      if (gameEngine && gameState.specters) {
        gameEngine.syncSpecters(gameState.specters);
      }
    },
    onSpecterCaptured: (data) => {
      console.log('Specter captured:', data);

      // If we have a game engine, handle specter capture
      if (gameEngine) {
        // If the specter has a valid ID, remove it from the game
        if (data.specterId) {
          // Add visual feedback for the capture (even if another player did it)
          if (data.position) {
            const position = new THREE.Vector3(
              data.position.x,
              data.position.y,
              data.position.z
            );
            gameEngine.createCaptureEffect(position);
          }

          // Update specters in the game engine
          if (data.updatedSpecters) {
            gameEngine.syncSpecters(data.updatedSpecters);
          }

          // Show toast notification
          toast({
            title: 'Specter Captured',
            description: `${data.playerName || 'A player'} captured a specter!`,
            duration: 3000
          });
        }
      }
    },
    onLevelComplete: (data) => {
      console.log('Level complete:', data);
      setCurrentLevel(data.nextLevel);
      toast({
        title: 'Level Complete',
        description: `Level ${data.previousLevel} complete! Starting level ${data.nextLevel}...`,
        duration: 5000
      });

      // If we have new specters, update them in the game engine
      if (data.specters && gameEngine) {
        gameEngine.syncSpecters(data.specters);
      }
    },
    onChatMessage: (data) => {
      console.log('Chat message:', data);
      setMessages(prev => [...prev, data]);
    },
    onError: (error) => {
      console.error('Multiplayer error:', error);
      toast({
        title: 'Connection Error',
        description: error.message || 'An error occurred with the game connection',
        duration: 5000,
        variant: 'destructive'
      });
    },
    // Add handlers for weapon effects
    onWeaponEffectCreated: (effectData) => {
      if (gameEngine) {
        gameEngine.createNetworkWeaponEffect(effectData);
      }
    },
    onWeaponEffectRemoved: (effectId) => {
      if (gameEngine) {
        gameEngine.removeNetworkWeaponEffect(effectId);
      }
    }
  });

  // Load multiplayer settings from session storage
  useEffect(() => {
    // Check if we're in a multiplayer game
    const storedGameMode = sessionStorage.getItem('gameMode');
    const storedPlayerName = sessionStorage.getItem('playerName');
    const storedIsHost = sessionStorage.getItem('isHost') === 'true';
    // const storedGameCode = sessionStorage.getItem('gameCode');
    const storedArenaId = sessionStorage.getItem('arenaId');
    const isPvpArenaMode = sessionStorage.getItem('pvpArenaMode') === 'true';
    const isSpectator = sessionStorage.getItem('isSpectator') === 'true';

    // Set the inGame flag to true when starting a game
    sessionStorage.setItem('inGame', 'true');

    // Only proceed with multiplayer setup if we're in a multiplayer game mode
    if (storedGameMode && storedGameMode !== 'single') {
      console.log(`Initializing multiplayer game mode: ${storedGameMode}`);
      setIsMultiplayer(true);
      setGameMode(storedGameMode);

      if (storedPlayerName) {
        setPlayerName(storedPlayerName);
      }

      setIsHost(storedIsHost);

      // Check if we're in PVP Arena mode
      if (isPvpArenaMode) {
        console.log('Initializing PVP Arena mode');
        // We'll set up the Coliseum Arena when the game engine is created
        // Connect to WebSocket server for tournament battles
        console.log('Connecting to WebSocket server for PVP Arena...');
        multiplayer.connect();

        // If in spectator mode, we'll need to set that up
        if (isSpectator) {
          console.log('Setting up spectator mode for PVP Arena');
          // We'll enable spectator mode when the game engine is created
        }

        // Get the battle ID from session storage if available
        const battleId = sessionStorage.getItem('battleId');
        if (battleId) {
          console.log(`Joining battle ${battleId} as spectator`);
          // We'll join the battle when the game engine is created
        } else {
          console.log('No battle ID found in session storage');
        }
      } else if (storedArenaId) {
        // Regular multiplayer mode
        // Always parse to ensure it's a number
        const arenaIdNum = parseInt(storedArenaId, 10);
        console.log(`Parsed arena ID: ${arenaIdNum} (original: ${storedArenaId})`);

        if (isNaN(arenaIdNum)) {
          console.error('Invalid arena ID stored in session');
          toast({
            title: 'Invalid Arena ID',
            description: 'The stored arena ID is invalid. Please rejoin the game.',
            variant: 'destructive',
            duration: 5000
          });
          setLocation('/');
          return;
        }

        setArenaId(arenaIdNum);

        // Connect to WebSocket server
        console.log('Connecting to WebSocket server...');
        multiplayer.connect();

        // We'll now handle joining in the onConnect callback to ensure proper sequence
        console.log('Arena join will be handled after connection confirmation');
      }

      // Log info for testing
      console.log(`Multiplayer game initialized: ${storedGameMode} mode`);
      console.log(`Player: ${storedPlayerName} (${storedIsHost ? 'Host' : 'Guest'})`);
    }
  }, []);

  // Handle cleanup on component unmount
  useEffect(() => {
    return () => {
      // Always disconnect from WebSocket server when unmounting
      // This ensures we don't have lingering connections
      multiplayer.disconnect();

      // Clear any multiplayer session storage when leaving the game
      // to prevent auto-connecting on next visit
      if (isMultiplayer) {
        console.log('Cleaning up multiplayer session storage');
        // Keep the session storage for now, but mark that we're no longer in a game
        sessionStorage.setItem('inGame', 'false');
      }
    };
  }, [isMultiplayer]);

  // Modify the homingStatus checking logic
  useEffect(() => {
    // Only run if game engine is initialized
    if (!gameEngine) return;

    // Set up homing status checking interval
    const homingCheckInterval = setInterval(() => {
      try {
        // Directly check if homing is enabled from the game engine
        const isHomingEnabled = gameEngine.isHomingEnabled();

        // Update state if different - only log when it changes
        if (isHomingEnabled !== homingActive) {
          console.log("Homing status changed to:", isHomingEnabled);
          setHomingActive(isHomingEnabled);

          // Show toast notification when homing is activated
          if (isHomingEnabled) {
            toast({
              title: "Homing Activated!",
              description: "Your shots will automatically target the nearest specter!",
              duration: 3000
            });
          }
        }
      } catch (error) {
        console.error("Error checking homing status:", error);
      }
    }, 100); // Check every 100ms for responsive updates

    // Clean up interval on unmount
    return () => clearInterval(homingCheckInterval);
  }, [gameEngine, homingActive, toast]);

  // Add a direct event listener for homing status changes
  useEffect(() => {
    // Create a handler for the custom homingStatusChanged event
    const handleHomingStatusChange = (event: CustomEvent) => {
      const { active, duration } = event.detail;
      console.log("Received homingStatusChanged event:", active, "Duration:", duration);

      // Update state directly
      setHomingActive(active);

      // Show toast notification when homing is activated
      if (active) {
        toast({
          title: "Homing Activated!",
          description: `Your shots will automatically target the nearest specter for ${duration} seconds!`,
          duration: 3000
        });
      }
    };

    // Add the event listener
    document.addEventListener('homingStatusChanged', handleHomingStatusChange as EventListener);

    // Remove event listener on cleanup
    return () => {
      document.removeEventListener('homingStatusChanged', handleHomingStatusChange as EventListener);
    };
  }, [toast]);

  // Add listener for score updates from other components
  useEffect(() => {
    // Create a handler for the custom scoreUpdate event
    const handleScoreUpdate = (event: CustomEvent) => {
      const { score } = event.detail;
      console.log("Received scoreUpdate event with score:", score);

      // Update the score state
      setScore(score);
    };

    // Add the event listener
    document.addEventListener('scoreUpdate', handleScoreUpdate as EventListener);

    // Remove event listener on cleanup
    return () => {
      document.removeEventListener('scoreUpdate', handleScoreUpdate as EventListener);
    };
  }, []);

  // Add listener for dialog closed events to resume the game
  useEffect(() => {
    // Only set up if we have a game engine
    if (!gameEngine) return;

    // Handler for AI pet dialog opened event
    const handleAIPetDialogOpened = () => {
      console.log('AI Pet dialog opened, ensuring game is paused');
      gameEngine.pause();
      setIsPaused(true);
      setIsDialogOpen(true);
    };

    // Handler for AI pet dialog closed event
    const handleAIPetDialogClosed = () => {
      console.log('AI Pet dialog closed, resuming game');
      gameEngine.resume();
      setIsPaused(false);
      setIsDialogOpen(false);
    };

    // Handler for NFT mint dialog opened event
    const handleNFTMintDialogOpened = () => {
      console.log('NFT Mint dialog opened, ensuring game is paused');
      gameEngine.pause();
      setIsPaused(true);
      setIsDialogOpen(true);
    };

    // Handler for NFT mint dialog closed event
    const handleNFTMintDialogClosed = () => {
      console.log('NFT Mint dialog closed, resuming game');
      gameEngine.resume();
      setIsPaused(false);
      setIsDialogOpen(false);
    };

    // Handler for NFT browser dialog opened event
    const handleNFTBrowserDialogOpened = () => {
      console.log('NFT Browser dialog opened, ensuring game is paused');
      gameEngine.pause();
      setIsPaused(true);
      setIsDialogOpen(true);
    };

    // Handler for NFT browser dialog closed event
    const handleNFTBrowserDialogClosed = () => {
      console.log('NFT Browser dialog closed, resuming game');
      gameEngine.resume();
      setIsPaused(false);
      setIsDialogOpen(false);
    };

    // Add event listeners
    document.addEventListener('aiPetDialogOpened', handleAIPetDialogOpened);
    document.addEventListener('aiPetDialogClosed', handleAIPetDialogClosed);
    document.addEventListener('nftMintDialogOpened', handleNFTMintDialogOpened);
    document.addEventListener('nftMintDialogClosed', handleNFTMintDialogClosed);
    document.addEventListener('nftBrowserDialogOpened', handleNFTBrowserDialogOpened);
    document.addEventListener('nftBrowserDialogClosed', handleNFTBrowserDialogClosed);

    // Remove event listeners on cleanup
    return () => {
      document.removeEventListener('aiPetDialogOpened', handleAIPetDialogOpened);
      document.removeEventListener('aiPetDialogClosed', handleAIPetDialogClosed);
      document.removeEventListener('nftMintDialogOpened', handleNFTMintDialogOpened);
      document.removeEventListener('nftMintDialogClosed', handleNFTMintDialogClosed);
      document.removeEventListener('nftBrowserDialogOpened', handleNFTBrowserDialogOpened);
      document.removeEventListener('nftBrowserDialogClosed', handleNFTBrowserDialogClosed);
    };
  }, [gameEngine]);

  // Initialize game engine
  useEffect(() => {
    if (!gameContainerRef.current) return;

    try {
      console.log("Initializing game engine");
      const newGameEngine = new GameEngine(gameContainerRef.current, isMobile);

      // Enable network mode if in multiplayer - DO THIS DURING INITIALIZATION
      if (isMultiplayer) {
        console.log("Setting network mode to true during initialization");
        newGameEngine.setNetworkMode(true);
      }

      // Set mobile mode if on mobile device
      if (isMobile) {
        console.log("Setting mobile mode to true");
        newGameEngine.setMobileMode(true);
      }

      // Set up event handlers for game engine
      newGameEngine.onSpecterCapture = (specter: SpecterType) => {
        // Update React state score
        setScore(prev => prev + specter.points);
        // Also update the player object's score directly
        newGameEngine.player.score += specter.points;
        setSpectersCaptured(prev => prev + 1);

        // For multiplayer, report specter capture to server
        if (isMultiplayer && arenaId) {
          multiplayer.reportSpecterCapture(
            specter.id,
            specter,
            newGameEngine.player.getPosition()
          );
        }
      };

      newGameEngine.onAmmoChange = (ammo: number[]) => {
        setAmmo(ammo);
      };

      newGameEngine.onAmmoTypeChange = (type: string) => {
        setAmmoType(type);
      };

      newGameEngine.onGameOver = () => {
        setShowGameOver(true);
        setIsPaused(true);
        newGameEngine.pause();
      };

      newGameEngine.onHealthChange = (health: number) => {
        setHealth(health);
      };

      newGameEngine.onJetpackFuelChange = (fuel: number) => {
        setJetpackFuel(fuel);
      };

      newGameEngine.onPetManagementOpen = (pets: PetSpecter[]) => {
        setPetSpecters(pets);
        setIsPetSpecterUIOpen(true);
      };

      newGameEngine.onLevelChange = (level: number) => {
        setCurrentLevel(level);
      };

      // Set up dungeon event handlers
      newGameEngine.onDungeonEnter = () => {
        setIsInDungeon(true);
        setRoomsCleared(0);
        setTotalRooms(10); // Approximate number of rooms
        setBossDefeated(false);
        setEnemiesDefeated(0);
        setLootCollected(0);

        // Get dungeon info from game engine
        const dungeonManager = newGameEngine.getDungeonManager();
        if (dungeonManager) {
          setDungeonLevel(dungeonManager.getDungeonLevel());
          setDungeonDifficulty(dungeonManager.getDungeonDifficulty());
        }
      };

      newGameEngine.onDungeonExit = () => {
        setIsInDungeon(false);
      };

      newGameEngine.onDungeonComplete = (rewards: any) => {
        setDungeonRewards(rewards);
        setShowDungeonRewards(true);
      };

      newGameEngine.onDungeonFailed = () => {
        toast({
          title: 'Dungeon Failed',
          description: 'You were defeated in the dungeon!',
          variant: 'destructive'
        });
      };

      newGameEngine.onDungeonEnemyDefeated = () => {
        setEnemiesDefeated(prev => prev + 1);
      };

      newGameEngine.onDungeonBossDefeated = () => {
        setBossDefeated(true);
      };

      newGameEngine.onDungeonLootCollected = () => {
        setLootCollected(prev => prev + 1);
      };

      newGameEngine.onDungeonRoomCleared = () => {
        setRoomsCleared(prev => prev + 1);
      };

      // Setup callbacks for multiplayer effects
      newGameEngine.onWeaponEffectCreated = (effectData) => {
        if (isMultiplayer) {
          multiplayer.createWeaponEffect(effectData);
        }
      };

      newGameEngine.onWeaponEffectRemoved = (effectId) => {
        if (isMultiplayer) {
          multiplayer.removeWeaponEffect(effectId);
        }
      };

      // Setup player position update for multiplayer
      newGameEngine.onPlayerPositionUpdate = (position, rotation) => {
        if (isMultiplayer && multiplayer.connected && multiplayer.connectionConfirmed) {
          // Only send updates when the connection is fully established
          multiplayer.updatePlayerState({
            position: {
              x: position.x,
              y: position.y,
              z: position.z
            },
            rotation: {
              x: rotation.x,
              y: rotation.y,
              z: rotation.z
            },
            health: health,
            jetpackFuel: jetpackFuel,
            firing: false,
            ammoType: ammoType,
            name: playerName
          });
        }
      };

      setGameEngine(newGameEngine);

      // Make the game engine available globally for dialog providers
      window.gameEngine = newGameEngine;

      // Start the game
      newGameEngine.start();

      // Portal integration has been removed

      // Create dungeon entrance for single player experience
      try {
        newGameEngine.createDungeonEntrance();
      } catch (error) {
        console.warn('Failed to create dungeon entrance:', error);
        // Game can continue without dungeon entrance
      }

      // In multiplayer mode, set the isNewlyCreated flag to false immediately
      // after starting to prevent future position overrides from the server
      if (isMultiplayer) {
        setTimeout(() => {
          if (newGameEngine && newGameEngine.isNewlyCreated) {
            console.log("Setting isNewlyCreated to false to prevent position overrides");
            newGameEngine.isNewlyCreated = false;
          }
        }, 1000); // Give the game engine 1 second to initialize before preventing position overrides
      }

      // Start updating radar data
      const radarUpdateInterval = setInterval(() => {
        if (newGameEngine) {
          try {
            // Get player position
            const playerPos = newGameEngine.getPlayerPosition();
            if (playerPos) {
              setPlayerPosition({
                x: playerPos.x,
                y: playerPos.y,
                z: playerPos.z
              });
            }

            // Get specter map data (includes other players in multiplayer)
            const specterData = newGameEngine.getSpecterMapData();
            if (specterData) {
              setSpecterMapData(specterData);
            }
          } catch (error) {
            console.error("Error updating radar data:", error);
          }
        }
      }, 100);

      return () => {
        clearInterval(radarUpdateInterval);
        if (newGameEngine) {
          newGameEngine.dispose();
        }
      };
    } catch (error) {
      console.error("Error initializing game engine:", error);
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : String(error));
    }
  }, [gameContainerRef]);

  // Effect hook to handle joining the multiplayer arena
  useEffect(() => {
    // Trigger join only when connection ID is confirmed and we have necessary info
    if (isMultiplayer && localConnectionId && arenaId !== null && playerName && !joinAttempted) {
      console.log(`>>> JOIN EFFECT: Conditions met (localConnectionId: ${localConnectionId}). Attempting to join arena ${arenaId} as ${playerName}`);
      multiplayer.joinArena(arenaId, playerName);
      setJoinAttempted(true); // Mark that we've attempted to join
    } else {
       // Optional: Add more detailed logging for debugging why join didn't happen
       if (isMultiplayer) {
           // Keep this log for now as it's important for join debugging
           // console.log(`>>> JOIN EFFECT: Conditions NOT met. localConnectionId: ${localConnectionId}, arenaId: ${arenaId}, playerName: ${playerName}, joinAttempted: ${joinAttempted}`);
       }
    }
    // Dependencies: Trigger when connection ID is set, or arena/player details change before joining
  }, [isMultiplayer, localConnectionId, arenaId, playerName, joinAttempted, multiplayer.joinArena]);

  // Update minimap data from game engine - modified to also update position in multiplayer
  useEffect(() => {
    if (!gameEngine || isPaused) return;

    // Set up an interval to update the minimap data
    const intervalId = setInterval(() => {
      if (gameEngine && !isPaused) {
        // Get the player position
        const playerPos = gameEngine.getPlayerPosition();
        if (playerPos) {
          setPlayerPosition({
            x: playerPos.x,
            y: playerPos.y,
            z: playerPos.z
          });

          // If in multiplayer, ensure we're sending position updates
          if (isMultiplayer && multiplayer.connected && multiplayer.connectionConfirmed) {
            // Get camera direction for rotation using the new safe accessor
            const cameraQuaternion = gameEngine.getCameraQuaternion();
            if (cameraQuaternion) {
              const euler = new THREE.Euler().setFromQuaternion(cameraQuaternion);
              const rotation = new THREE.Vector3(euler.x, euler.y, euler.z);

              // Debug output occasionally
              // Comment out the random position update log
              // if (Math.random() < 0.05) { // 5% chance to log
              //   console.log('Sending position update:', {
              //     pos: playerPos,
              //     rot: rotation,
              //     player: playerName
              //   });
              // }

              if (Math.random() < 0.002) { // Log very infrequently (0.2% chance)
                 console.log('Sending position update (infrequent log):', { pos: playerPos, rot: rotation, player: playerName });
              }

              // Send position update to server
              multiplayer.updatePlayerState({
                position: {
                  x: playerPos.x,
                  y: playerPos.y,
                  z: playerPos.z
                },
                rotation: {
                  x: rotation.x,
                  y: rotation.y,
                  z: rotation.z
                },
                health: health,
                jetpackFuel: jetpackFuel,
                firing: false,
                ammoType: ammoType,
                name: playerName // Add player name to updates
              });
            }
          }
        }

        // Get the specters data for the minimap
        const specters = gameEngine.getSpecterMapData();
        setSpecterMapData(specters.map((specter: { id: number; position: { x: number; y: number; z: number }; color: string }) => ({
          id: specter.id,
          position: {
            x: specter.position.x,
            y: specter.position.y,
            z: specter.position.z
          },
          color: specter.color
        })));
      }
    }, 100); // Update 10 times per second for more responsive multiplayer

    return () => clearInterval(intervalId);
  }, [gameEngine, isPaused, isMultiplayer, multiplayer, health, jetpackFuel, ammoType, playerName]);

  // Handle key press for pause
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !hasError && gameEngine) {
        togglePause();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPaused, gameEngine, hasError]);

  const togglePause = () => {
    if (gameEngine) {
      if (isPaused) {
        gameEngine.resume();
      } else {
        gameEngine.pause();
      }
      setIsPaused(!isPaused);
    }
  };

  const exitGame = () => {
    setLocation('/');
  };

  const restartGame = () => {
    if (gameEngine) {
      gameEngine.restart();
      setScore(0);
      setSpectersCaptured(0);
      setAmmo([5, 5, 5]);
      setAmmoType('Gravity Well');
      setCurrentLevel(1);
      setShowGameOver(false);
      setHasError(false);
    } else {
      // If game engine failed to initialize, just go back to home screen
      setLocation('/');
    }
  };

  const tryAgain = () => {
    // Simple reset of component state
    setHasError(false);
    setErrorMessage('');
    setGameEngine(null);

    // Game will reinitialize itself in the next render cycle
  };

  // Setup position update interval for multiplayer mode
  useEffect(() => {
    // Only run if we're in multiplayer mode and have a game engine
    if (!isMultiplayer || !gameEngine) return;

    // console.log("Setting up dedicated multiplayer position update interval");

    // Create an interval to consistently update player position
    const positionUpdateInterval = setInterval(() => {
      if (!multiplayer.connected || !multiplayer.connectionConfirmed) {
        // Skip updates if not fully connected
        return;
      }

      try {
        const playerPosition = gameEngine.getPlayerPosition();
        if (!playerPosition) {
          // console.warn("No valid player position available for multiplayer update"); // Reduce noise
          return;
        }

        // Get camera direction for rotation
        const cameraQuaternion = gameEngine.getCameraQuaternion();
        if (!cameraQuaternion) {
          // console.warn("No valid camera quaternion available for multiplayer update"); // Reduce noise
          return;
        }

        const euler = new THREE.Euler().setFromQuaternion(cameraQuaternion);
        const rotation = new THREE.Vector3(euler.x, euler.y, euler.z);

        // Send position update to server
        multiplayer.updatePlayerState({
          position: {
            x: playerPosition.x,
            y: playerPosition.y,
            z: playerPosition.z
          },
          rotation: {
            x: rotation.x,
            y: rotation.y,
            z: rotation.z
          },
          health: health,
          jetpackFuel: jetpackFuel,
          firing: false,
          ammoType: ammoType,
          name: playerName
        });

        // Comment out the random position update log
        // if (Math.random() < 0.01) { // 1% chance to log
        //   console.log("Multiplayer position update sent:", {
        //     position: playerPosition,
        //     rotation: rotation,
        //     connectionId: multiplayer.connectionId
        //   });
        // }
        if (Math.random() < 0.002) { // Log very infrequently (0.2% chance)
           console.log("Multiplayer position update sent (infrequent log):", { position: playerPosition, rotation: rotation, connectionId: multiplayer.connectionId });
        }
      } catch (error) {
        console.error("Error in multiplayer position update:", error);
      } // Ensure this closing brace for the try block is present
    }, 100); // Update 10 times per second for responsive multiplayer

    return () => clearInterval(positionUpdateInterval);
  }, [isMultiplayer, gameEngine, multiplayer.connected, multiplayer.connectionConfirmed]); // Removed health, jetpackFuel, ammoType, playerName

  // Add a useEffect to periodically sync game state in multiplayer mode
  useEffect(() => {
    // Only apply this in multiplayer mode with an active game state
    if (!isMultiplayer || !gameEngine) return;

    // Set up interval to refresh multiplayer state
    const syncInterval = setInterval(() => {
      console.log('Periodic game state sync');

      // If we have a game engine, make sure all player positions are updated
      if (gameEngine && players.length > 0) {
        // Ensure all other players are properly represented in the game
        players.forEach(player => {
          // Skip local player
          if (player.id === localConnectionId) return;

          // Update or create the player in the game engine
          gameEngine.updateOtherPlayer(player);
        });
      }
    }, 500); // Sync every .5 seconds

    return () => clearInterval(syncInterval);
  }, [isMultiplayer, gameEngine, players, localConnectionId]);

  // Add a useEffect to listen for spectator mode changes
  useEffect(() => {
    const handleSpectatorModeChange = (event: CustomEvent) => {
      const { enabled } = event.detail;
      // Update UI based on spectator mode
      console.log(`Spectator mode ${enabled ? 'enabled' : 'disabled'}`);
      setShowHUD(!enabled);
    };

    document.addEventListener('spectatorModeChanged', handleSpectatorModeChange as EventListener);

    return () => {
      document.removeEventListener('spectatorModeChanged', handleSpectatorModeChange as EventListener);
    };
  }, []);

  // Error screen
  if (hasError) {
    return (
      <div className="w-screen h-screen flex items-center justify-center bg-black">
        <div className="max-w-md p-6 bg-gray-900 rounded-lg border border-red-500 text-center">
          <h2 className="text-xl text-red-400 mb-4">Couldn't Initialize Game</h2>
          <p className="mb-4 text-gray-300">{errorMessage || "The game couldn't be started. This might be due to limited resources or browser compatibility issues."}</p>
          <div className="flex flex-col gap-2">
            <Button className="w-full bg-blue-500 hover:bg-blue-600" onClick={tryAgain}>
              Try Again
            </Button>
            <Button className="w-full bg-gray-700 hover:bg-gray-600" onClick={exitGame}>
              Return to Menu
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-screen h-screen overflow-hidden">
      <div
        ref={gameContainerRef}
        id="game-container"
        className="absolute inset-0 w-full h-full"
        style={{ display: 'block' }}
      >
        {/* Three.js will render here */}
      </div>

      {/* NFT Dialog Providers */}
      <NFTMintDialogProvider />
      <NFTBasedPetDialogProvider />
      <AIPetGenerationDialogProvider />
      <NFTBrowserDialogProvider />

      <div className="crosshair absolute left-1/2 top-1/2 -ml-2.5 -mt-2.5 pointer-events-none z-10">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="10" cy="10" r="8" stroke="#4299e1" strokeWidth="1" fill="none" />
          <circle cx="10" cy="10" r="1" fill="#4299e1" />
          <line x1="10" y1="4" x2="10" y2="7" stroke="#4299e1" />
          <line x1="10" y1="13" x2="10" y2="16" stroke="#4299e1" />
          <line x1="4" y1="10" x2="7" y2="10" stroke="#4299e1" />
          <line x1="13" y1="10" x2="16" y2="10" stroke="#4299e1" />
        </svg>
      </div>

      {showHUD && (
        <Suspense fallback={<MinimalLoadingIndicator />}>
          <HUD
            score={score}
            ammoType={ammoType}
            ammo={ammo}
            spectersCaptured={spectersCaptured}
            health={health}
            jetpackFuel={jetpackFuel}
            isMultiplayer={isMultiplayer}
            gameMode={gameMode}
            playerName={playerName}
            isHost={isHost}
            players={players}
            teams={teams}
            currentLevel={currentLevel}
            homingActive={homingActive}
            localPlayerId={localConnectionId}
          />
        </Suspense>
      )}

      {/* Dungeon UI */}
      {isInDungeon && (
        <Suspense fallback={<MinimalLoadingIndicator />}>
          <DungeonUI
            isInDungeon={isInDungeon}
            dungeonLevel={dungeonLevel}
            dungeonDifficulty={dungeonDifficulty}
            roomsCleared={roomsCleared}
            totalRooms={totalRooms}
            bossDefeated={bossDefeated}
            enemiesDefeated={enemiesDefeated}
            lootCollected={lootCollected}
          />
        </Suspense>
      )}

      {/* Dungeon Rewards Dialog */}
      <Suspense fallback={<MinimalLoadingIndicator />}>
        <DungeonRewardsDialog
          isOpen={showDungeonRewards}
          onClose={() => setShowDungeonRewards(false)}
          rewards={dungeonRewards}
          dungeonLevel={dungeonLevel}
        />
      </Suspense>

      {/* Minimap */}
      {playerPosition && (
        <div className="absolute bottom-6 right-36 z-20 rounded-full shadow-lg shadow-blue-900/30">
          <Suspense fallback={<MinimalLoadingIndicator />}>
            <Minimap
              playerPosition={playerPosition}
              specters={specterMapData}
              players={isMultiplayer ? players : undefined}
              teams={isMultiplayer ? teams : undefined}
              isMultiplayer={isMultiplayer}
            />
          </Suspense>
        </div>
      )}

      {/* Mobile Controls */}
      {isMobile && gameEngine && (
        <Suspense fallback={<MinimalLoadingIndicator />}>
          <MobileControls
            onMove={(direction: THREE.Vector3) => gameEngine.handleMobileInput(direction)}
            onLook={(x: number, y: number) => gameEngine.handleMobileLook(x, y)}
            onJump={() => gameEngine.handleMobileJump()}
            onShoot={() => gameEngine.handleMobileShoot()}
            onSwitchWeapon={(weaponIndex: number) => gameEngine.handleMobileWeaponSwitch(weaponIndex)}
            onGrapple={() => gameEngine.handleMobileGrapple()}
            onJetpack={(active: boolean) => gameEngine.handleMobileJetpack(active)}
            onInteract={() => gameEngine.handleMobileInteract()}
          />
        </Suspense>
      )}

      {/* Pet Specter Management UI */}
      <Suspense fallback={<MinimalLoadingIndicator />}>
        <PetSpecterUI
          pets={petSpecters}
          isOpen={isPetSpecterUIOpen}
          onClose={() => {
            setIsPetSpecterUIOpen(false);
            if (gameEngine) {
              gameEngine.resume();
            }
          }}
        />
      </Suspense>

      {/* Pet Specter Tip - show it briefly when the player has pets */}
      {petSpecters.length > 0 && (
        <div className="absolute top-24 right-4 z-20 bg-gray-900 bg-opacity-90 p-3 rounded-lg border border-blue-500 text-blue-300 shadow-lg animate-fade-out">
          <div className="flex items-center gap-2">
            <span className="font-bold border border-blue-400 px-2 rounded">P</span>
            <span>Press P to manage your Pet Specters</span>
          </div>
        </div>
      )}

      {/* Pause Dialog - only show when paused and no other dialogs are open */}
      <Dialog open={isPaused && !showGameOver && !isDialogOpen} onOpenChange={(open) => {
        if (!open) togglePause();
      }}>
        <DialogContent className="bg-gray-900 text-white border-blue-400">
          <DialogHeader>
            <DialogTitle className="title-font text-xl text-blue-400">Game Paused</DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-gray-300">
            Take a moment to breathe. The Shatterscape awaits your return.
          </DialogDescription>
          <DialogFooter className="flex flex-col gap-2 sm:flex-row">
            <Button className="w-full bg-blue-500 hover:bg-blue-600" onClick={togglePause}>
              Resume Game
            </Button>
            <Button className="w-full bg-red-600 hover:bg-red-700" onClick={exitGame}>
              Exit to Menu
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Game Over Dialog */}
      <Dialog open={showGameOver} onOpenChange={(open) => {
        if (!open) setShowGameOver(false);
      }}>
        <DialogContent className="bg-gray-900 text-white border-blue-400">
          <DialogHeader>
            <DialogTitle className="title-font text-xl text-blue-400">Hunt Complete</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4 text-gray-300">
              Your hunt through the Shatterscape has ended.
            </p>
            <div className="bg-gray-800 p-4 rounded-md">
              <div className="flex justify-between mb-2">
                <span>Specters Captured:</span>
                <span className="text-blue-400">{spectersCaptured}</span>
              </div>
              <div className="flex justify-between">
                <span>Final Score:</span>
                <span className="text-blue-400">{score}</span>
              </div>
            </div>
          </div>
          <DialogFooter className="flex flex-col gap-2 sm:flex-row">
            <Button className="w-full bg-blue-500 hover:bg-blue-600" onClick={restartGame}>
              Hunt Again
            </Button>
            <Button className="w-full bg-gray-600 hover:bg-gray-700" onClick={exitGame}>
              Return to Base
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <style>
        {`
          @keyframes loading {
            0% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
          }
          .animate-loading {
            animation: loading 1.5s ease-in-out infinite;
          }
          .animate-pulse {
            animation: pulse 1.5s ease-in-out infinite;
          }
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
          @keyframes fade-out {
            0% { opacity: 1; }
            70% { opacity: 1; }
            100% { opacity: 0; display: none; }
          }
          .animate-fade-out {
            animation: fade-out 5s forwards;
          }
        `}
      </style>
    </div>
  );
};

export default Game;