/**
 * SeededRandom class for deterministic random number generation
 * This allows us to generate the same "random" sequence given the same seed
 */
export class SeededRandom {
  private seed: number;
  
  constructor(seed: number) {
    this.seed = seed;
  }
  
  /**
   * Get the current seed
   */
  public getSeed(): number {
    return this.seed;
  }
  
  /**
   * Set a new seed
   */
  public setSeed(seed: number): void {
    this.seed = seed;
  }
  
  /**
   * Generate a random number between 0 and 1
   * Uses a simple linear congruential generator algorithm
   */
  public random(): number {
    // Simple LCG parameters
    const a = 1664525;
    const c = 1013904223;
    const m = Math.pow(2, 32);
    
    // Update seed
    this.seed = (a * this.seed + c) % m;
    
    // Return normalized result (0 to 1)
    return this.seed / m;
  }
  
  /**
   * Generate a random number between min and max (inclusive)
   */
  public between(min: number, max: number): number {
    return min + this.random() * (max - min);
  }
  
  /**
   * Generate a random integer between min and max (inclusive)
   */
  public intBetween(min: number, max: number): number {
    return Math.floor(this.between(min, max + 0.999999));
  }
  
  /**
   * Choose a random element from an array
   */
  public choose<T>(array: T[]): T {
    return array[this.intBetween(0, array.length - 1)];
  }
  
  /**
   * Shuffle an array in-place
   */
  public shuffle<T>(array: T[]): T[] {
    // Fisher-Yates shuffle algorithm
    for (let i = array.length - 1; i > 0; i--) {
      const j = this.intBetween(0, i);
      [array[i], array[j]] = [array[j], array[i]];
    }
    
    return array;
  }
  
  /**
   * Return true with the given probability
   */
  public chance(probability: number): boolean {
    return this.random() < probability;
  }
}
