import * as THREE from 'three';
import * as <PERSON>N<PERSON><PERSON> from 'cannon-es';

/**
 * Coliseum Arena for PVP battles
 * Creates a dedicated battle arena with coliseum theme
 */
export class ColiseumArena {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private arenaCenter: THREE.Vector3;
  private arenaRadius: number = 50;
  private arenaHeight: number = 30;
  private arenaFloor: THREE.Mesh | null = null;
  private arenaWalls: THREE.Group | null = null;
  private arenaCeiling: THREE.Mesh | null = null;
  private decorations: THREE.Group;
  private lights: THREE.Group;
  private spectatorSeats: THREE.Group;
  private spectatorPositions: THREE.Vector3[] = [];
  private isSpectatorMode: boolean = false;

  constructor(scene: THREE.Scene, world: CANNON.World) {
    this.scene = scene;
    this.world = world;
    this.arenaCenter = new THREE.Vector3(0, 0, 0);
    this.decorations = new THREE.Group();
    this.lights = new THREE.Group();
    this.spectatorSeats = new THREE.Group();
  }

  /**
   * Create the coliseum arena
   */
  public create(): void {
    // Set up skybox for the arena
    this.setupSkybox();

    // Create arena floor
    this.createArenaFloor();

    // Create arena walls
    this.createArenaWalls();

    // Create arena ceiling
    this.createArenaCeiling();

    // Add decorations
    this.addDecorations();

    // Add lighting
    this.addLighting();

    // Add spectator seating
    this.addSpectatorSeating();

    // Add the groups to the scene
    this.scene.add(this.decorations);
    this.scene.add(this.lights);
    this.scene.add(this.spectatorSeats);

    console.log('Coliseum arena created');
  }

  /**
   * Set up skybox for the arena
   */
  private setupSkybox(): void {
    // Create a dark skybox with minimal texturing to avoid texture unit issues
    const skyboxGeometry = new THREE.BoxGeometry(500, 500, 500);
    
    // Use a single material for all sides to reduce texture usage
    const skyboxMaterial = new THREE.MeshBasicMaterial({
      color: 0x1a2030,
      side: THREE.BackSide
    });
    
    const skybox = new THREE.Mesh(skyboxGeometry, skyboxMaterial);
    this.scene.add(skybox);
    
    // Add simple stars using points instead of textures
    const starsGeometry = new THREE.BufferGeometry();
    const starsCount = 500;
    const positions = new Float32Array(starsCount * 3);
    const colors = new Float32Array(starsCount * 3);
    const sizes = new Float32Array(starsCount);
    
    for (let i = 0; i < starsCount; i++) {
      // Random position within a sphere
      const radius = 400;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(2 * Math.random() - 1);
      
      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i * 3 + 2] = radius * Math.cos(phi);
      
      // Star color (white with slight variations)
      colors[i * 3] = 0.8 + Math.random() * 0.2;
      colors[i * 3 + 1] = 0.8 + Math.random() * 0.2;
      colors[i * 3 + 2] = 0.8 + Math.random() * 0.2;
      
      // Star size
      sizes[i] = Math.random() * 2;
    }
    
    starsGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    starsGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    starsGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    
    const starsMaterial = new THREE.PointsMaterial({
      size: 1,
      vertexColors: true,
      sizeAttenuation: true
    });
    
    const stars = new THREE.Points(starsGeometry, starsMaterial);
    this.scene.add(stars);
  }

  /**
   * Create the arena floor with SVG texture
   */
  private createArenaFloor(): void {
    console.log('Creating arena floor with texture');
    // Create a circular floor
    const floorGeometry = new THREE.CircleGeometry(this.arenaRadius, 64);

    // Use standard SVG texture for the floor
    const textureLoader = new THREE.TextureLoader();
    console.log('Loading floor texture using standard level texture');
    const floorTexture = textureLoader.load('/assets/textures/level/floor1.svg');
    floorTexture.wrapS = THREE.RepeatWrapping;
    floorTexture.wrapT = THREE.RepeatWrapping;
    // floorTexture.repeat.set(5, 5); // Removed repeat to test appearance

    const floorMaterial = new THREE.MeshStandardMaterial({
      map: floorTexture,
      color: 0xd2b48c,
      roughness: 0.8,
      metalness: 0.2
    });

    this.arenaFloor = new THREE.Mesh(floorGeometry, floorMaterial);
    this.arenaFloor.rotation.x = -Math.PI / 2;
    this.arenaFloor.position.copy(this.arenaCenter);
    this.arenaFloor.receiveShadow = true;
    this.scene.add(this.arenaFloor);

    // Add physics for the floor
    const floorShape = new CANNON.Plane();
    const floorBody = new CANNON.Body({
      mass: 0,
      shape: floorShape,
      material: new CANNON.Material('floor')
    });
    floorBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);
    floorBody.position.set(this.arenaCenter.x, this.arenaCenter.y, this.arenaCenter.z);
    this.world.addBody(floorBody);
  }

  /**
   * Create the arena walls with SVG texture
   */
  private createArenaWalls(): void {
    console.log('Creating arena walls with texture');
    this.arenaWalls = new THREE.Group();

    // Use standard SVG texture for the walls
    const textureLoader = new THREE.TextureLoader();
    console.log('Loading wall texture using standard level texture');
    const wallTexture = textureLoader.load('/assets/textures/level/wall1.svg');
    wallTexture.wrapS = THREE.RepeatWrapping;
    wallTexture.wrapT = THREE.RepeatWrapping;
    // wallTexture.repeat.set(10, 2); // Removed repeat to test appearance

    const wallMaterial = new THREE.MeshStandardMaterial({
      map: wallTexture,
      color: 0xa0522d,
      roughness: 0.7,
      metalness: 0.3
    });

    // Create a cylindrical wall
    const wallGeometry = new THREE.CylinderGeometry(
      this.arenaRadius,
      this.arenaRadius,
      this.arenaHeight,
      32, // Reduced segments for better performance
      1,
      true
    );

    const wall = new THREE.Mesh(wallGeometry, wallMaterial);
    wall.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + this.arenaHeight / 2,
      this.arenaCenter.z
    );
    wall.castShadow = true;
    wall.receiveShadow = true;

    this.arenaWalls.add(wall);
    this.scene.add(this.arenaWalls);

    // Add physics for the walls
    const wallShape = new CANNON.Cylinder(
      this.arenaRadius,
      this.arenaRadius,
      this.arenaHeight,
      16
    );

    const wallBody = new CANNON.Body({
      mass: 0,
      shape: wallShape,
      material: new CANNON.Material('wall')
    });

    wallBody.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + this.arenaHeight / 2,
      this.arenaCenter.z
    );

    // Set the correct orientation for the cylinder wall
    wallBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), Math.PI / 2);

    this.world.addBody(wallBody);
  }

  /**
   * Create the arena ceiling with SVG texture
   */
  private createArenaCeiling(): void {
    console.log('Creating arena ceiling with texture');
    // Create a circular ceiling
    const ceilingGeometry = new THREE.CircleGeometry(this.arenaRadius, 32); // Reduced segments

    // Use standard texture for the ceiling
    const textureLoader = new THREE.TextureLoader();
    console.log('Loading ceiling texture using standard level texture');
    
    // Use floor texture for ceiling to reduce unique textures
    const ceilingTexture = textureLoader.load('/assets/textures/level/floor1.svg');
    ceilingTexture.wrapS = THREE.RepeatWrapping;
    ceilingTexture.wrapT = THREE.RepeatWrapping;
    // ceilingTexture.repeat.set(3, 3); // Removed repeat to test appearance

    const ceilingMaterial = new THREE.MeshStandardMaterial({
      map: ceilingTexture,
      color: 0x8b4513,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    });

    this.arenaCeiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
    this.arenaCeiling.rotation.x = Math.PI / 2;
    this.arenaCeiling.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + this.arenaHeight,
      this.arenaCenter.z
    );
    this.arenaCeiling.receiveShadow = true;
    this.scene.add(this.arenaCeiling);
  }

  /**
   * Add decorations to the arena
   */
  private addDecorations(): void {
    // Add columns around the arena
    const columnCount = 12;
    const columnRadius = 2;
    const columnHeight = this.arenaHeight;

    const columnGeometry = new THREE.CylinderGeometry(
      columnRadius,
      columnRadius,
      columnHeight,
      16
    );

    const columnMaterial = new THREE.MeshStandardMaterial({
      color: 0xd2b48c,
      roughness: 0.7,
      metalness: 0.3
    });

    for (let i = 0; i < columnCount; i++) {
      const angle = (i / columnCount) * Math.PI * 2;
      const x = Math.cos(angle) * (this.arenaRadius - columnRadius - 1);
      const z = Math.sin(angle) * (this.arenaRadius - columnRadius - 1);

      const column = new THREE.Mesh(columnGeometry, columnMaterial);
      column.position.set(
        x,
        this.arenaCenter.y + columnHeight / 2,
        z
      );
      column.castShadow = true;
      column.receiveShadow = true;

      this.decorations.add(column);
    }

    // Add banners
    const bannerCount = 6;
    const bannerWidth = 10;
    const bannerHeight = 15;

    const bannerGeometry = new THREE.PlaneGeometry(bannerWidth, bannerHeight);
    const bannerMaterials = [
      new THREE.MeshStandardMaterial({ color: 0xff0000, side: THREE.DoubleSide }),
      new THREE.MeshStandardMaterial({ color: 0x0000ff, side: THREE.DoubleSide }),
      new THREE.MeshStandardMaterial({ color: 0xffff00, side: THREE.DoubleSide }),
      new THREE.MeshStandardMaterial({ color: 0x00ff00, side: THREE.DoubleSide }),
      new THREE.MeshStandardMaterial({ color: 0xff00ff, side: THREE.DoubleSide }),
      new THREE.MeshStandardMaterial({ color: 0x00ffff, side: THREE.DoubleSide })
    ];

    for (let i = 0; i < bannerCount; i++) {
      const angle = (i / bannerCount) * Math.PI * 2;
      const x = Math.cos(angle) * (this.arenaRadius - 1);
      const z = Math.sin(angle) * (this.arenaRadius - 1);

      const banner = new THREE.Mesh(
        bannerGeometry,
        bannerMaterials[i % bannerMaterials.length]
      );

      banner.position.set(
        x,
        this.arenaCenter.y + this.arenaHeight / 2,
        z
      );

      // Rotate banner to face center
      banner.lookAt(this.arenaCenter);

      // Adjust rotation to hang vertically
      banner.rotation.y += Math.PI;

      this.decorations.add(banner);
    }

    // Add a central platform
    const platformRadius = 15;
    const platformHeight = 1;

    const platformGeometry = new THREE.CylinderGeometry(
      platformRadius,
      platformRadius,
      platformHeight,
      32
    );

    const platformMaterial = new THREE.MeshStandardMaterial({
      color: 0x8b4513,
      roughness: 0.8,
      metalness: 0.2
    });

    const platform = new THREE.Mesh(platformGeometry, platformMaterial);
    platform.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + platformHeight / 2,
      this.arenaCenter.z
    );
    platform.receiveShadow = true;

    this.decorations.add(platform);

    // Add physics for the platform
    const platformShape = new CANNON.Cylinder(
      platformRadius,
      platformRadius,
      platformHeight,
      16
    );

    const platformBody = new CANNON.Body({
      mass: 0,
      shape: platformShape,
      material: new CANNON.Material('platform')
    });

    platformBody.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + platformHeight / 2,
      this.arenaCenter.z
    );

    // Set the correct orientation for the cylinder platform
    platformBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), Math.PI / 2);

    this.world.addBody(platformBody);
  }

  /**
   * Add lighting to the arena
   */
  private addLighting(): void {
    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
    this.lights.add(ambientLight);

    // Add directional light (sun)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;

    // Configure shadow properties
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;

    this.lights.add(directionalLight);

    // Add point lights around the arena
    const pointLightCount = 6;
    const pointLightHeight = this.arenaHeight * 0.8;
    const pointLightRadius = this.arenaRadius * 0.8;
    const pointLightColors = [
      0xff5555, 0x55ff55, 0x5555ff,
      0xffff55, 0xff55ff, 0x55ffff
    ];

    for (let i = 0; i < pointLightCount; i++) {
      const angle = (i / pointLightCount) * Math.PI * 2;
      const x = Math.cos(angle) * pointLightRadius;
      const z = Math.sin(angle) * pointLightRadius;

      const pointLight = new THREE.PointLight(
        pointLightColors[i % pointLightColors.length],
        0.5,
        100
      );

      pointLight.position.set(
        x,
        this.arenaCenter.y + pointLightHeight,
        z
      );

      pointLight.castShadow = true;

      // Add a small sphere to represent the light
      const lightSphereGeometry = new THREE.SphereGeometry(1, 16, 16);
      const lightSphereMaterial = new THREE.MeshBasicMaterial({
        color: pointLightColors[i % pointLightColors.length],
        transparent: true,
        opacity: 0.7
      });

      const lightSphere = new THREE.Mesh(lightSphereGeometry, lightSphereMaterial);
      lightSphere.position.copy(pointLight.position);

      this.lights.add(pointLight);
      this.decorations.add(lightSphere);
    }

    // Add a spotlight on the center of the arena
    const spotLight = new THREE.SpotLight(0xffffff, 1);
    spotLight.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y + this.arenaHeight,
      this.arenaCenter.z
    );

    spotLight.target.position.set(
      this.arenaCenter.x,
      this.arenaCenter.y,
      this.arenaCenter.z
    );

    spotLight.angle = Math.PI / 6;
    spotLight.penumbra = 0.2;
    spotLight.decay = 1;
    spotLight.distance = 200;

    spotLight.castShadow = true;
    spotLight.shadow.mapSize.width = 1024;
    spotLight.shadow.mapSize.height = 1024;

    this.lights.add(spotLight);
    this.lights.add(spotLight.target);
  }

  /**
   * Get the arena center position
   */
  public getArenaCenter(): THREE.Vector3 {
    return this.arenaCenter.clone();
  }

  /**
   * Get the arena radius
   */
  public getArenaRadius(): number {
    return this.arenaRadius;
  }

  /**
   * Add spectator seating around the arena
   */
  private addSpectatorSeating(): void {
    // Create spectator seats in a tiered arrangement around the arena
    const seatRows = 3;
    const seatsPerRow = 24;
    const seatWidth = 2;
    const seatHeight = 1;
    const seatDepth = 2;
    const rowSpacing = 3;

    const seatGeometry = new THREE.BoxGeometry(seatWidth, seatHeight, seatDepth);
    const seatMaterials = [
      new THREE.MeshStandardMaterial({ color: 0x8B4513 }), // Brown
      new THREE.MeshStandardMaterial({ color: 0xA52A2A }), // Brown-red
      new THREE.MeshStandardMaterial({ color: 0xCD853F })  // Tan
    ];

    // Create seats in rows
    for (let row = 0; row < seatRows; row++) {
      const rowRadius = this.arenaRadius + 5 + (row * rowSpacing);
      const rowHeight = 5 + (row * 3); // Each row is higher than the previous

      for (let seat = 0; seat < seatsPerRow; seat++) {
        const angle = (seat / seatsPerRow) * Math.PI * 2;
        const x = Math.cos(angle) * rowRadius;
        const z = Math.sin(angle) * rowRadius;

        // Create seat
        const seatMesh = new THREE.Mesh(
          seatGeometry,
          seatMaterials[row % seatMaterials.length]
        );

        seatMesh.position.set(
          x,
          this.arenaCenter.y + rowHeight,
          z
        );

        // Rotate seat to face center
        seatMesh.lookAt(new THREE.Vector3(
          this.arenaCenter.x,
          this.arenaCenter.y + rowHeight,
          this.arenaCenter.z
        ));

        seatMesh.castShadow = true;
        seatMesh.receiveShadow = true;

        this.spectatorSeats.add(seatMesh);

        // Add spectator position (slightly above and in front of seat)
        const spectatorPos = new THREE.Vector3(
          x * 0.9, // Move slightly toward center
          this.arenaCenter.y + rowHeight + 1.5, // Above seat
          z * 0.9 // Move slightly toward center
        );

        this.spectatorPositions.push(spectatorPos);
      }
    }

    // Add railings between rows
    for (let row = 0; row < seatRows; row++) {
      const railingRadius = this.arenaRadius + 5 + (row * rowSpacing) - 1;
      const railingHeight = 5 + (row * 3) + 1.5;

      const railingGeometry = new THREE.TorusGeometry(railingRadius, 0.2, 8, 64);
      const railingMaterial = new THREE.MeshStandardMaterial({ color: 0xA0A0A0 });

      const railing = new THREE.Mesh(railingGeometry, railingMaterial);
      railing.position.set(
        this.arenaCenter.x,
        this.arenaCenter.y + railingHeight,
        this.arenaCenter.z
      );

      railing.rotation.x = Math.PI / 2;

      railing.castShadow = true;
      railing.receiveShadow = true;

      this.spectatorSeats.add(railing);
    }
  }

  /**
   * Set spectator mode
   */
  public setSpectatorMode(enabled: boolean): void {
    this.isSpectatorMode = enabled;
  }

  /**
   * Get a random spectator position
   */
  public getRandomSpectatorPosition(): THREE.Vector3 {
    if (this.spectatorPositions.length === 0) {
      return new THREE.Vector3(0, 10, 0);
    }

    const randomIndex = Math.floor(Math.random() * this.spectatorPositions.length);
    return this.spectatorPositions[randomIndex].clone();
  }

  /**
   * Check if in spectator mode
   */
  public getIsSpectatorMode(): boolean {
    return this.isSpectatorMode;
  }

  /**
   * Dispose of all resources
   */
  public dispose(): void {
    // Remove and dispose floor
    if (this.arenaFloor) {
      this.scene.remove(this.arenaFloor);
      if (this.arenaFloor.geometry) this.arenaFloor.geometry.dispose();
      if (this.arenaFloor.material) {
        if (Array.isArray(this.arenaFloor.material)) {
          this.arenaFloor.material.forEach(material => {
            const standardMat = material as THREE.MeshStandardMaterial;
            if (standardMat && standardMat.map) {
              standardMat.map.dispose();
            }
            material.dispose();
          });
        } else {
          const standardMat = this.arenaFloor.material as THREE.MeshStandardMaterial;
          if (standardMat && standardMat.map) {
            standardMat.map.dispose();
          }
          this.arenaFloor.material.dispose();
        }
      }
      this.arenaFloor = null;
    }

    // Remove and dispose walls
    if (this.arenaWalls) {
      this.arenaWalls.children.forEach(child => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => {
                const standardMat = material as THREE.MeshStandardMaterial;
                if (standardMat && standardMat.map) {
                  standardMat.map.dispose();
                }
                material.dispose();
              });
            } else {
              const standardMat = child.material as THREE.MeshStandardMaterial;
              if (standardMat && standardMat.map) {
                standardMat.map.dispose();
              }
              child.material.dispose();
            }
          }
        }
      });
      this.scene.remove(this.arenaWalls);
      this.arenaWalls = null;
    }

    // Remove and dispose ceiling
    if (this.arenaCeiling) {
      this.scene.remove(this.arenaCeiling);
      if (this.arenaCeiling.geometry) this.arenaCeiling.geometry.dispose();
      if (this.arenaCeiling.material) {
        if (Array.isArray(this.arenaCeiling.material)) {
          this.arenaCeiling.material.forEach(material => {
            const standardMat = material as THREE.MeshStandardMaterial;
            if (standardMat && standardMat.map) {
              standardMat.map.dispose();
            }
            material.dispose();
          });
        } else {
          const standardMat = this.arenaCeiling.material as THREE.MeshStandardMaterial;
          if (standardMat && standardMat.map) {
            standardMat.map.dispose();
          }
          this.arenaCeiling.material.dispose();
        }
      }
      this.arenaCeiling = null;
    }

    // Remove and dispose decorations
    if (this.decorations) {
      this.decorations.children.forEach(child => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => {
                const standardMat = material as THREE.MeshStandardMaterial;
                if (standardMat && standardMat.map) {
                  standardMat.map.dispose();
                }
                material.dispose();
              });
            } else {
              const standardMat = child.material as THREE.MeshStandardMaterial;
              if (standardMat && standardMat.map) {
                standardMat.map.dispose();
              }
              child.material.dispose();
            }
          }
        }
      });
      this.scene.remove(this.decorations);
    }

    // Remove and dispose lights
    if (this.lights) {
      this.scene.remove(this.lights);
    }

    // Remove and dispose spectator seats
    if (this.spectatorSeats) {
      this.spectatorSeats.children.forEach(child => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => {
                const standardMat = material as THREE.MeshStandardMaterial;
                if (standardMat && standardMat.map) {
                  standardMat.map.dispose();
                }
                material.dispose();
              });
            } else {
              const standardMat = child.material as THREE.MeshStandardMaterial;
              if (standardMat && standardMat.map) {
                standardMat.map.dispose();
              }
              child.material.dispose();
            }
          }
        }
      });
      this.scene.remove(this.spectatorSeats);
    }

    console.log('Coliseum arena disposed');
  }
}
