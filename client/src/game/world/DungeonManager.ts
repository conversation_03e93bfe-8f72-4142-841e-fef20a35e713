import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';
import { DungeonGenerator, DungeonDifficulty } from './DungeonGenerator';
import { PetSpecter, SpecterTraitType } from '../entities/PetSpecter';
import Player from '../entities/Player';
import { audioManager as AudioManager } from '../audio/AudioManager';

/**
 * DungeonManager class for managing dungeon gameplay
 * This class acts as an interface between the GameEngine and the dungeon system
 */
export class DungeonManager {
  private mainScene: THREE.Scene; // Main game scene
  private dungeonScene: THREE.Scene | null = null; // Separate scene for dungeon
  private world: CANNON.World;
  private player: Player;
  private petSpecter: PetSpecter | null = null;
  private dungeonGenerator: DungeonGenerator | null = null;
  private inDungeon: boolean = false;
  private dungeonLevel: number = 1;
  private dungeonDifficulty: DungeonDifficulty = DungeonDifficulty.EASY;
  private dungeonEntrancePosition: THREE.Vector3 | null = null;
  private playerStartPosition: THREE.Vector3 | null = null;
  private dungeonEntranceMesh: THREE.Mesh | null = null; // Store the entrance mesh
  private mainSceneSkybox: THREE.Object3D | null = null; // Store reference to main scene skybox
  private dungeonSkybox: THREE.Mesh | null = null; // Skybox for dungeon scene
  private dungeonStarsGroup: THREE.Group | null = null; // Stars for dungeon skybox
  private dungeonGroundPlane: THREE.Mesh | null = null; // Ground plane that follows the player
  private dungeonRewards: any = {
    experience: 0,
    gold: 0,
    items: []
  };

  // Callbacks for game events
  public onDungeonEnter: () => void = () => {};
  public onDungeonExit: () => void = () => {};
  public onDungeonComplete: (rewards: any) => void = () => {};
  public onDungeonFailed: () => void = () => {};
  public onEnemyDefeated: (enemyData: any) => void = () => {};
  public onBossDefeated: (bossData: any) => void = () => {};
  public onLootCollected: (lootData: any) => void = () => {};
  public onRoomCleared: (roomKey: string) => void = () => {};

  constructor(scene: THREE.Scene, world: CANNON.World, player: Player) {
    this.mainScene = scene;
    this.world = world;
    this.player = player;
  }

  /**
   * Set the player's pet specter for dungeon
   */
  setPetSpecter(petSpecter: PetSpecter | null): void {
    this.petSpecter = petSpecter;

    // If we're already in the dungeon, make sure the pet is in the dungeon scene
    if (this.inDungeon && this.dungeonScene && petSpecter) {
      // First, remove the pet from the main scene
      const petMesh = petSpecter.getMesh();
      if (petMesh) {
        this.mainScene.remove(petMesh);

        // Then add it to the dungeon scene
        this.dungeonScene.add(petMesh);

        console.log('Transferred pet specter to dungeon scene');
      }
    }
  }

  /**
   * Create a dungeon entrance at the specified position
   */
  createDungeonEntrance(position: THREE.Vector3): void {
    // Check if we already have a dungeon entrance
    if (this.dungeonEntrancePosition) {
      console.log('Dungeon entrance already exists, not creating another one');
      return;
    }

    // Store entrance position
    this.dungeonEntrancePosition = position.clone();

    // Create visual portal
    const portalGeometry = new THREE.TorusGeometry(2, 0.3, 16, 32);
    const portalMaterial = new THREE.MeshStandardMaterial({
      color: 0x3366cc,
      emissive: 0x3366cc,
      emissiveIntensity: 0.5,
      metalness: 0.8,
      roughness: 0.2
    });

    const portal = new THREE.Mesh(portalGeometry, portalMaterial);
    portal.position.copy(position);
    portal.position.y += 1; // Raise slightly above ground
    portal.rotation.x = Math.PI / 2; // Flat orientation
    portal.userData.isDungeonEntrance = true; // Mark for identification
    this.dungeonEntranceMesh = portal; // Store the mesh
    this.mainScene.add(portal);

    // Add portal particles
    const particleCount = 50;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const radius = 1.5 + Math.random() * 0.5;

      particlePositions[i * 3] = position.x + Math.cos(angle) * radius;
      particlePositions[i * 3 + 1] = position.y + 1 + Math.random() * 2;
      particlePositions[i * 3 + 2] = position.z + Math.sin(angle) * radius;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: 0x66ccff,
      size: 0.1,
      transparent: true,
      opacity: 0.7
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    particles.userData.isDungeonEntranceParticles = true; // Mark for identification
    this.mainScene.add(particles);

    // Add dungeon sign
    const signGeometry = new THREE.PlaneGeometry(3, 1);
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;

    const context = canvas.getContext('2d');
    if (context) {
      context.fillStyle = '#000000';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.fillStyle = '#ffffff';
      context.font = 'bold 48px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText('DUNGEON ENTRANCE', canvas.width / 2, canvas.height / 2);
      context.font = '24px Arial';
      context.fillText('(Requires Pet Specter)', canvas.width / 2, canvas.height / 2 + 40);
    }

    const signTexture = new THREE.CanvasTexture(canvas);
    const signMaterial = new THREE.MeshBasicMaterial({
      map: signTexture,
      transparent: true,
      opacity: 0.9
    });

    const sign = new THREE.Mesh(signGeometry, signMaterial);
    sign.position.copy(position);
    sign.position.y += 3;
    sign.position.z -= 2;
    sign.userData.isDungeonEntranceSign = true; // Mark for identification
    this.mainScene.add(sign);
  }

  /**
   * Get the dungeon entrance mesh object
   */
  public getDungeonEntranceObject(): THREE.Mesh | null {
    return this.dungeonEntranceMesh;
  }

  /**
   * Check if player is near dungeon entrance
   */
  checkDungeonEntrance(): boolean {
    if (!this.dungeonEntrancePosition || !this.petSpecter) return false;

    const playerPosition = this.player.getPosition();
    const distance = playerPosition.distanceTo(this.dungeonEntrancePosition);

    // If player is within range of entrance and has a pet specter
    if (distance < 3) {
      return true;
    }

    return false;
  }

  /**
   * Enter the dungeon
   */
  enterDungeon(): void {
    console.log('DungeonManager.enterDungeon called');

    if (!this.petSpecter) {
      console.error('Cannot enter dungeon without a pet specter');
      return;
    }

    console.log('Pet specter available:', this.petSpecter);

    // Store player's current position to return to later
    this.playerStartPosition = this.player.getPosition().clone();
    console.log('Stored player start position:', this.playerStartPosition);

    // Set dungeon flag
    this.inDungeon = true;
    console.log('Set inDungeon flag to true');

    // Create a separate scene for the dungeon
    this.dungeonScene = new THREE.Scene();

    // Add lighting to the dungeon scene
    this.setupDungeonLighting();

    // Add skybox to prevent seeing the void outside rooms
    this.setupDungeonSkybox();

    // Add ground plane to prevent seeing the void below rooms
    this.setupDungeonGroundPlane();

    // Store reference to main scene skybox
    this.mainScene.traverse((object) => {
      if (object.name === 'skybox') {
        this.mainSceneSkybox = object;
      }
    });

    // Create dungeon
    console.log('Creating dungeon generator with difficulty:', this.dungeonDifficulty, 'and level:', this.dungeonLevel);
    this.dungeonGenerator = new DungeonGenerator(
      this.dungeonScene, // Use the dungeon scene instead of main scene
      this.world,
      this.dungeonDifficulty,
      this.dungeonLevel,
      this.petSpecter
    );

    // We'll transfer the pet after the player is positioned in the dungeon

    // Set up dungeon event handlers
    console.log('Setting up dungeon event handlers');
    this.setupDungeonEventHandlers();

    // Generate the dungeon
    console.log('Generating dungeon');
    this.dungeonGenerator.generateDungeon();

    // Move player to dungeon entrance room
    console.log('Getting entrance room');
    const entranceRoom = this.dungeonGenerator.getCurrentRoom();
    if (entranceRoom) {
      console.log('Found entrance room, getting position');
      const entrancePosition = entranceRoom.getPosition().clone();
      entrancePosition.y += 1; // Raise slightly above ground
      console.log('Moving player to entrance position:', entrancePosition);

      // Check if player has setPosition method
      if (typeof this.player.setPosition === 'function') {
        this.player.setPosition(entrancePosition);
        console.log('Player position set successfully');

        // Adjust player physics for dungeon movement
        if (typeof this.player.adjustForDungeonPhysics === 'function') {
          this.player.adjustForDungeonPhysics();
          console.log('Adjusted player physics for dungeon movement');
        }
      } else {
        console.error('Player does not have setPosition method!');
        // Fallback: Try to set position directly on the physics body
        try {
          const body = this.player.getPhysicsBody();
          if (body) {
            body.position.set(entrancePosition.x, entrancePosition.y, entrancePosition.z);
            console.log('Set player position directly on physics body');
          } else {
            console.error('Could not get player physics body');
          }
        } catch (err) {
          console.error('Error setting player position directly:', err);
        }
      }

      // Now that the player is positioned in the dungeon, transfer the pet specter
      if (this.petSpecter) {
        console.log('Transferring pet specter to dungeon scene after player positioning');

        try {
          // First, remove the pet from the main scene
          const petMesh = this.petSpecter.getMesh();
          if (petMesh) {
            this.mainScene.remove(petMesh);

            // Then add it to the dungeon scene
            this.dungeonScene.add(petMesh);

            // Create a marker for the pet to find the player
            const petMarker = new THREE.Object3D();
            petMarker.userData = {
              isPlayer: true,
              playerReference: this.player
            };
            this.dungeonScene.add(petMarker);

            // Get the player's position AFTER they've been moved to the entrance room
            const playerPos = this.player.getPosition();
            console.log('Player position for pet placement (after player positioning):', playerPos);
            // Position pet directly in front of the player inside the dungeon
            // Get player's forward direction
            const playerForward = new THREE.Vector3(0, 0, 1);
            // Apply player's rotation to get the actual forward direction
            const playerRotation = this.player.getRotation();
            if (playerRotation) {
              playerForward.applyQuaternion(playerRotation);
            }
            // Position pet 2 units in front of player
            petMesh.position.set(
              playerPos.x + playerForward.x * 2,
              playerPos.y,
              playerPos.z + playerForward.z * 2
            );

            // Make sure the pet is visible
            petMesh.visible = true;
            console.log('Pet position set to:', petMesh.position);

            // Add a point light attached to the pet for better visibility
            const petLight = new THREE.PointLight(0x3366ff, 1, 10);
            petLight.position.set(0, 1, 0); // Slightly above the pet
            petMesh.add(petLight);
            console.log('Added light to pet specter');

            // If the pet has a setTarget method, use it
            if (typeof this.petSpecter.setTarget === 'function') {
              this.petSpecter.setTarget(this.player);
              console.log('Pet target set to player');
            }
          }
        } catch (error) {
          console.error('Error positioning pet specter:', error);
        }

        console.log('Transferred pet specter to dungeon scene');
      }
    } else {
      console.error('No entrance room found!');
    }

    // Play dungeon enter sound
    console.log('Playing dungeon enter sound');
    AudioManager.playSoundEffect('dungeonEnter');

    // Notify about dungeon entry
    console.log('Calling onDungeonEnter callback');
    this.onDungeonEnter();

    console.log(`Entered dungeon at level ${this.dungeonLevel} with difficulty ${this.dungeonDifficulty}`);
  }

  /**
   * Set up event handlers for dungeon events
   */
  private setupDungeonEventHandlers(): void {
    if (!this.dungeonGenerator) return;

    // Handle enemy defeated
    this.dungeonGenerator.onEnemyDefeated = (enemyData: any) => {
      // Add to rewards
      this.dungeonRewards.experience += enemyData.points;

      // Notify about enemy defeat
      this.onEnemyDefeated(enemyData);
    };

    // Handle boss defeated
    this.dungeonGenerator.onBossDefeated = (bossData: any) => {
      // Add to rewards
      this.dungeonRewards.experience += bossData.points * 2;
      this.dungeonRewards.gold += 100 * this.dungeonLevel;

      // Notify about boss defeat
      this.onBossDefeated(bossData);
    };

    // Handle loot collected
    this.dungeonGenerator.onLootCollected = (lootData: any) => {
      // Add to rewards based on loot type
      switch (lootData.type) {
        case 'gold':
          this.dungeonRewards.gold += lootData.value;
          break;
        case 'experience_orb':
          this.dungeonRewards.experience += lootData.value;
          break;
        case 'equipment':
        case 'rare_material':
          this.dungeonRewards.items.push(lootData);
          break;
      }

      // Notify about loot collection
      this.onLootCollected(lootData);
    };

    // Handle room cleared
    this.dungeonGenerator.onRoomCleared = (roomKey: string) => {
      // Notify about room cleared
      this.onRoomCleared(roomKey);
    };

    // Handle dungeon completed
    this.dungeonGenerator.onDungeonCompleted = () => {
      // Increase dungeon level for next time
      this.dungeonLevel++;

      // If player has completed 3 dungeons at current difficulty, increase difficulty
      if (this.dungeonLevel % 3 === 0) {
        this.increaseDifficulty();
      }

      // Schedule exit from dungeon
      setTimeout(() => {
        this.exitDungeon(true);
      }, 3000);
    };

    // Handle dungeon failed
    this.dungeonGenerator.onDungeonFailed = () => {
      // Schedule exit from dungeon
      setTimeout(() => {
        this.exitDungeon(false);
      }, 3000);
    };
  }

  /**
   * Increase dungeon difficulty
   */
  private increaseDifficulty(): void {
    switch (this.dungeonDifficulty) {
      case DungeonDifficulty.EASY:
        this.dungeonDifficulty = DungeonDifficulty.MEDIUM;
        break;
      case DungeonDifficulty.MEDIUM:
        this.dungeonDifficulty = DungeonDifficulty.HARD;
        break;
      case DungeonDifficulty.HARD:
        this.dungeonDifficulty = DungeonDifficulty.NIGHTMARE;
        break;
    }
  }

  /**
   * Exit the dungeon
   */
  exitDungeon(completed: boolean): void {
    if (!this.inDungeon || !this.playerStartPosition) return;

    // Set dungeon flag
    this.inDungeon = false;

    // Clear dungeon
    if (this.dungeonGenerator) {
      this.dungeonGenerator.clearDungeon();
      this.dungeonGenerator = null;
    }

    // Clear dungeon scene
    if (this.dungeonScene) {
      // Clear skybox and ground plane references
      this.dungeonSkybox = null;
      this.dungeonStarsGroup = null;
      this.dungeonGroundPlane = null;

      // Dispose of all geometries and materials in the dungeon scene
      this.dungeonScene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else if (object.material) {
            object.material.dispose();
          }
        }
      });

      // Clear the dungeon scene
      while (this.dungeonScene.children.length > 0) {
        this.dungeonScene.remove(this.dungeonScene.children[0]);
      }

      this.dungeonScene = null;
    }

    // Move player back to original position
    this.player.setPosition(this.playerStartPosition);

    // Reset player physics for surface world
    if (typeof this.player.resetToSurfacePhysics === 'function') {
      this.player.resetToSurfacePhysics();
      console.log('Reset player physics for surface world');
    }

    // Transfer pet specter back to main scene if available
    if (this.petSpecter) {
      // Update pet position to be near the player
      const playerPos = this.player.getPosition();
      this.petSpecter.setPosition(new THREE.Vector3(
        playerPos.x + Math.random() * 2 - 1,
        playerPos.y + 1,
        playerPos.z + Math.random() * 2 - 1
      ));

      // Add it back to the main scene
      const petMesh = this.petSpecter.getMesh();
      if (petMesh) {
        this.mainScene.add(petMesh);
      }

      console.log('Transferred pet specter back to main scene');
    }

    // Play dungeon exit sound
    AudioManager.playSoundEffect('dungeonExit');

    // Notify about dungeon exit
    this.onDungeonExit();

    // If dungeon was completed, notify about rewards
    if (completed) {
      this.onDungeonComplete(this.dungeonRewards);

      // Apply rewards to pet specter
      if (this.petSpecter) {
        // Distribute experience across all traits
        const xpPerTrait = Math.floor(this.dungeonRewards.experience / 5);
        this.petSpecter.gainTraitXP(SpecterTraitType.ATTACK, xpPerTrait);
        this.petSpecter.gainTraitXP(SpecterTraitType.DEFENSE, xpPerTrait);
        this.petSpecter.gainTraitXP(SpecterTraitType.SPEED, xpPerTrait);
        this.petSpecter.gainTraitXP(SpecterTraitType.INTELLIGENCE, xpPerTrait);
        this.petSpecter.gainTraitXP(SpecterTraitType.LOYALTY, xpPerTrait);
      }
    } else {
      this.onDungeonFailed();
    }

    // Reset rewards
    this.dungeonRewards = {
      experience: 0,
      gold: 0,
      items: []
    };

    console.log(`Exited dungeon. Completed: ${completed}`);

    // Dispose of dungeon entrance visual elements
    if (this.dungeonEntranceMesh) {
      this.mainScene.remove(this.dungeonEntranceMesh);
      this.dungeonEntranceMesh.geometry.dispose();
      if (Array.isArray(this.dungeonEntranceMesh.material)) {
        this.dungeonEntranceMesh.material.forEach(m => m.dispose());
      } else {
        this.dungeonEntranceMesh.material.dispose();
      }
      this.dungeonEntranceMesh = null;
    }

    // Also remove particles and sign (assuming they are tracked or found by name/userdata)
    const entranceElementsToRemove = this.mainScene.children.filter(obj =>
      obj.userData.isDungeonEntranceParticles || obj.userData.isDungeonEntranceSign
    );
  }

  /**
   * Update dungeon state
   */
  update(delta: number): void {
    if (!this.inDungeon || !this.dungeonGenerator || !this.dungeonScene) return;

    // Update dungeon
    this.dungeonGenerator.update(delta, this.player.getPosition());

    // Check for loot collection
    this.checkLootCollection();

    // Check for enemy damage to player
    this.checkEnemyDamage(delta);

    // Update skybox and ground plane positions to follow player
    this.updateDungeonEnvironmentPosition();
  }

  /**
   * Updates the dungeon skybox and ground plane positions to follow the player
   * This creates the illusion of an infinite dungeon
   */
  private updateDungeonEnvironmentPosition(): void {
    // Only proceed if we have a valid dungeon scene
    if (!this.dungeonScene) return;

    // Get player position
    const playerPos = this.player.getPosition();

    // Update skybox position to center on player if it exists
    if (this.dungeonSkybox) {
      this.dungeonSkybox.position.set(playerPos.x, playerPos.y, playerPos.z);
    }

    // Update stars position to follow player if they exist
    if (this.dungeonStarsGroup) {
      this.dungeonStarsGroup.position.set(playerPos.x, playerPos.y, playerPos.z);
    }

    // Update ground plane position to follow player if it exists
    if (this.dungeonGroundPlane) {
      this.dungeonGroundPlane.position.set(playerPos.x, 0, playerPos.z);
    }
  }

  /**
   * Creates a skybox around the dungeon scene for improved visuals
   * This prevents players from seeing the void outside rooms
   */
  private setupDungeonSkybox(): void {
    if (!this.dungeonScene) return;

    // Create a directly visible skybox as a mesh
    const skyboxSize = 500; // Large enough to contain our dungeon

    // Create a box geometry for our skybox
    const geometry = new THREE.BoxGeometry(skyboxSize, skyboxSize, skyboxSize);

    // Create materials for each side of the box - DOOM-like dark colors with a hint of blue
    const materials = [
      new THREE.MeshBasicMaterial({
        color: 0x1a1a22, // Dark gray-blue
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // right
      new THREE.MeshBasicMaterial({
        color: 0x1a1a22, // Dark gray-blue
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // left
      new THREE.MeshBasicMaterial({
        color: 0x0a0a12, // Very dark gray-blue for ceiling
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // top
      new THREE.MeshBasicMaterial({
        color: 0x1a1a22, // Dark gray-blue
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // bottom
      new THREE.MeshBasicMaterial({
        color: 0x1a1a22, // Dark gray-blue
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // front
      new THREE.MeshBasicMaterial({
        color: 0x1a1a22, // Dark gray-blue
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // back
    ];

    // Create skybox mesh and store reference
    this.dungeonSkybox = new THREE.Mesh(geometry, materials);
    this.dungeonSkybox.name = 'dungeonSkybox'; // Name it for easy identification

    // Add to dungeon scene
    this.dungeonScene.add(this.dungeonSkybox);

    // Add stars to the skybox for extra visual detail
    this.addStarsToDungeonSkybox(skyboxSize);
  }

  /**
   * Sets up lighting for the dungeon scene
   * Optimized for better performance while maintaining visibility
   */
  private setupDungeonLighting(): void {
    if (!this.dungeonScene) return;

    // Set a stronger ambient light for the dungeon to ensure walls are visible
    // This provides base illumination with minimal performance impact
    const dungeonAmbientLight = new THREE.AmbientLight(0x888888, 2.0);
    this.dungeonScene.add(dungeonAmbientLight);

    // Add a single directional light for overall illumination
    // More efficient than multiple point lights
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(0, 10, 0);
    directionalLight.castShadow = false; // Disable shadows for performance
    this.dungeonScene.add(directionalLight);

    // Add a single point light that will follow the player
    // This is more efficient than having multiple static lights
    const playerLight = new THREE.PointLight(0xffffff, 1.5, 30);
    playerLight.position.set(0, 5, 0);
    playerLight.castShadow = false;

    // Add the light to the camera so it moves with the player
    this.player.getCamera().add(playerLight);
    this.dungeonScene.add(this.player.getCamera());
  }

  /**
   * Creates a large ground plane that follows the player
   * This prevents seeing the void below the dungeon rooms
   */
  private setupDungeonGroundPlane(): void {
    if (!this.dungeonScene) return;

    // Create a large ground plane
    const groundSize = 1000; // Very large to ensure coverage
    const groundGeometry = new THREE.PlaneGeometry(groundSize, groundSize);

    // Create a texture loader
    const textureLoader = new THREE.TextureLoader();

    // Use an existing floor texture from the assets folder
    const floorTexture = textureLoader.load('assets/textures/level/floor1.svg');
    floorTexture.wrapS = THREE.RepeatWrapping;
    floorTexture.wrapT = THREE.RepeatWrapping;
    floorTexture.repeat.set(groundSize / 10, groundSize / 10); // Repeat texture for better detail

    // Create material with the texture
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x333333, // Darker color to match dungeon aesthetic
      roughness: 0.8,
      metalness: 0.2,
      map: floorTexture
    });

    // Create the ground plane mesh
    this.dungeonGroundPlane = new THREE.Mesh(groundGeometry, groundMaterial);

    // Position it below the dungeon rooms (slightly below y=0)
    this.dungeonGroundPlane.position.set(0, -0.1, 0);
    this.dungeonGroundPlane.rotation.x = -Math.PI / 2; // Rotate to be horizontal

    // Add to dungeon scene
    this.dungeonScene.add(this.dungeonGroundPlane);
  }

  /**
   * Adds stars to the dungeon skybox for visual detail
   * Optimized for better performance using instanced mesh
   */
  private addStarsToDungeonSkybox(skyboxSize: number): void {
    if (!this.dungeonScene) return;

    // Create a group to hold stars and store reference
    this.dungeonStarsGroup = new THREE.Group();

    // Use fewer stars for better performance
    const starCount = 100; // Reduced from 300

    // Use points instead of meshes for better performance
    const positions = new Float32Array(starCount * 3);
    const sizes = new Float32Array(starCount);

    // Generate random star positions
    for (let i = 0; i < starCount; i++) {
      const distance = skyboxSize * 0.48;
      const randomDirection = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        Math.random() * 0.5, // More stars in upper hemisphere
        (Math.random() - 0.5) * 2
      ).normalize();

      const pos = randomDirection.multiplyScalar(distance);

      positions[i * 3] = pos.x;
      positions[i * 3 + 1] = pos.y;
      positions[i * 3 + 2] = pos.z;

      // Random size
      sizes[i] = Math.random() * 2 + 1;
    }

    // Create geometry with positions and sizes
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    // Create material for points
    const material = new THREE.PointsMaterial({
      color: 0x666666,
      size: 2,
      sizeAttenuation: true
    });

    // Create points system
    const stars = new THREE.Points(geometry, material);
    this.dungeonStarsGroup.add(stars);

    this.dungeonScene.add(this.dungeonStarsGroup);
  }

  /**
   * Check for loot collection
   */
  private checkLootCollection(): void {
    if (!this.dungeonGenerator) return;

    const playerPosition = this.player.getPosition();
    const loot = this.dungeonGenerator.getLoot();

    for (const lootItem of loot) {
      lootItem.checkCollection(playerPosition);
    }
  }

  /**
   * Check for enemy damage to player
   */
  private checkEnemyDamage(delta: number): void {
    if (!this.dungeonGenerator) return;

    const playerPosition = this.player.getPosition();
    const enemies = this.dungeonGenerator.getEnemies();
    const bosses = this.dungeonGenerator.getBosses();

    // Check regular enemies
    for (const enemy of enemies) {
      const enemyPosition = enemy.getPosition();
      const distance = enemyPosition.distanceTo(playerPosition);

      // If enemy is close enough to attack
      if (distance < 2 && !enemy.isEnemyDefeated()) {
        // Apply damage to player (once per second)
        if (Math.random() < delta) {
          const damage = enemy.getAttackPower();
          this.player.takeDamage(damage);

          // If player dies, fail the dungeon
          if (this.player.getHealth() <= 0 && this.dungeonGenerator) {
            this.dungeonGenerator.failDungeon();
          }
        }
      }
    }

    // Check bosses
    for (const boss of bosses) {
      const bossPosition = boss.getPosition();
      const distance = bossPosition.distanceTo(playerPosition);

      // If boss is close enough to attack
      if (distance < 3 && !boss.isDefeated()) {
        // Apply damage to player (once per second)
        if (Math.random() < delta) {
          const damage = boss.getAttackPower();
          this.player.takeDamage(damage);

          // If player dies, fail the dungeon
          if (this.player.getHealth() <= 0 && this.dungeonGenerator) {
            this.dungeonGenerator.failDungeon();
          }
        }
      }
    }
  }

  /**
   * Check if player is in a dungeon
   */
  isInDungeon(): boolean {
    return this.inDungeon;
  }

  /**
   * Get current dungeon level
   */
  getDungeonLevel(): number {
    return this.dungeonLevel;
  }

  /**
   * Get the dungeon scene
   */
  getDungeonScene(): THREE.Scene | null {
    return this.dungeonScene;
  }

  /**
   * Get the active scene (dungeon scene if in dungeon, otherwise main scene)
   */
  getActiveScene(): THREE.Scene {
    return this.inDungeon && this.dungeonScene ? this.dungeonScene : this.mainScene;
  }

  /**
   * Get the dungeon generator
   */
  getDungeonGenerator(): DungeonGenerator | null {
    return this.dungeonGenerator;
  }

  /**
   * Get current dungeon difficulty
   */
  getDungeonDifficulty(): DungeonDifficulty {
    return this.dungeonDifficulty;
  }

  /**
   * Set dungeon level
   */
  setDungeonLevel(level: number): void {
    this.dungeonLevel = level;
  }

  /**
   * Set dungeon difficulty
   */
  setDungeonDifficulty(difficulty: DungeonDifficulty): void {
    this.dungeonDifficulty = difficulty;
  }

  /**
   * Disable the dungeon manager
   * Used to prevent dungeon functionality in PVP Arena mode
   */
  disable(): void {
    console.log('Disabling dungeon manager');

    // Clear dungeon entrance if it exists
    if (this.dungeonEntrancePosition) {
      // Find and remove dungeon entrance objects
      this.mainScene.traverse((object) => {
        if (object.userData &&
            (object.userData.isDungeonEntrance ||
             object.userData.isDungeonEntranceParticles ||
             object.userData.isDungeonEntranceSign)) {
          this.mainScene.remove(object);

          // Dispose of geometry and materials
          if (object instanceof THREE.Mesh) {
            if (object.geometry) object.geometry.dispose();
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else if (object.material) {
              object.material.dispose();
            }
          }
        }
      });

      // Clear entrance position
      this.dungeonEntrancePosition = null;
    }

    // Clear dungeon if we're in one
    if (this.inDungeon) {
      this.exitDungeon(false);
    }

    // Clear dungeon generator
    if (this.dungeonGenerator) {
      this.dungeonGenerator.clearDungeon();
      this.dungeonGenerator = null;
    }
  }

  /**
   * Dispose of all resources
   */
  dispose(): void {
    // Clear dungeon
    if (this.dungeonGenerator) {
      this.dungeonGenerator.clearDungeon();
      this.dungeonGenerator = null;
    }

    // Remove dungeon entrance
    this.mainScene.traverse(object => {
      if (
        object.userData.isDungeonEntrance ||
        object.userData.isDungeonEntranceParticles ||
        object.userData.isDungeonEntranceSign
      ) {
        this.mainScene.remove(object);

        // Dispose of geometries and materials
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();

          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else if (object.material) {
            object.material.dispose();
          }
        }
      }
    });

    // Clear dungeon scene if it exists
    if (this.dungeonScene) {
      // Dispose of all geometries and materials in the dungeon scene
      this.dungeonScene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else if (object.material) {
            object.material.dispose();
          }
        }
      });

      // Clear the dungeon scene
      while (this.dungeonScene.children.length > 0) {
        this.dungeonScene.remove(this.dungeonScene.children[0]);
      }

      this.dungeonScene = null;
    }

    // Reset state
    this.inDungeon = false;
    this.dungeonEntrancePosition = null;
    this.playerStartPosition = null;
    this.petSpecter = null;
  }
}
