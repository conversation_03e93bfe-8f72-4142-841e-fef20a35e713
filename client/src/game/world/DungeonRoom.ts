import * as THREE from 'three';
import * as CA<PERSON><PERSON><PERSON> from 'cannon-es';
import { SeededRandom } from '../utils/SeededRandom';
import { DungeonRoomType, DungeonDifficulty } from './DungeonGenerator';

/**
 * DungeonRoom class for creating individual rooms in a dungeon
 */
export class DungeonRoom {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private position: THREE.Vector3;
  private size: number;
  private height: number;
  private type: DungeonRoomType;
  private difficulty: DungeonDifficulty;
  private dungeonLevel: number;
  private random: SeededRandom;
  private meshes: THREE.Object3D[] = [];
  private bodies: CANNON.Body[] = [];
  private boundingBox: THREE.Box3;
  private exitUnlocked: boolean = false;

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    size: number,
    height: number,
    type: DungeonRoomType,
    difficulty: DungeonDifficulty,
    dungeonLevel: number,
    seed: number
  ) {
    this.scene = scene;
    this.world = world;
    this.position = position;
    this.size = size;
    this.height = height;
    this.type = type;
    this.difficulty = difficulty;
    this.dungeonLevel = dungeonLevel;
    this.random = new SeededRandom(seed);

    // Create bounding box for collision detection
    this.boundingBox = new THREE.Box3(
      new THREE.Vector3(
        position.x - size/2,
        position.y,
        position.z - size/2
      ),
      new THREE.Vector3(
        position.x + size/2,
        position.y + height,
        position.z + size/2
      )
    );

    // Generate room based on type
    this.generateRoom();
  }

  /**
   * Generate room based on type
   */
  private generateRoom(): void {
    // Create floor
    this.createFloor();

    // Create walls
    this.createWalls();

    // Create ceiling
    this.createCeiling();

    // Add basic room lighting
    this.addBasicLighting();

    // Add room-specific features based on type
    switch (this.type) {
      case DungeonRoomType.ENTRANCE:
        this.createEntranceRoom();
        break;
      case DungeonRoomType.REGULAR:
        this.createRegularRoom();
        break;
      case DungeonRoomType.BOSS:
        this.createBossRoom();
        break;
      case DungeonRoomType.EXIT:
        this.createExitRoom();
        break;
    }
  }

  /**
   * Add basic lighting to the room
   * Optimized for better performance while maintaining visibility
   */
  private addBasicLighting(): void {
    // Add a single stronger point light in the center of the room
    // This reduces the number of lights while maintaining visibility
    const roomLight = new THREE.PointLight(0xffffff, 2.0, this.size * 2.0);
    roomLight.position.set(this.position.x, this.height / 2, this.position.z);

    // Optimize shadow settings
    roomLight.castShadow = false; // Disable shadows for performance

    this.scene.add(roomLight);
    this.meshes.push(roomLight);

    // Add a single ambient light to ensure minimum visibility
    // This is more efficient than multiple point lights
    const ambientLight = new THREE.AmbientLight(0xcccccc, 0.5);
    this.scene.add(ambientLight);
    this.meshes.push(ambientLight);
  }

  /**
   * Create floor for the room
   */
  private createFloor(): void {
    // Create floor geometry
    const floorGeometry = new THREE.PlaneGeometry(this.size, this.size);

    // Create a texture loader
    const textureLoader = new THREE.TextureLoader();

    // Determine floor texture number based on room type
    let floorTextureNumber: number;
    let floorColor: number;

    switch (this.type) {
      case DungeonRoomType.ENTRANCE:
        floorTextureNumber = 1;
        floorColor = 0x3366cc; // Blue
        break;
      case DungeonRoomType.REGULAR:
        floorTextureNumber = 2;
        floorColor = 0x666666; // Gray
        break;
      case DungeonRoomType.BOSS:
        floorTextureNumber = 5;
        floorColor = 0x990000; // Dark red
        break;
      case DungeonRoomType.EXIT:
        floorTextureNumber = 5; // Use floor5.svg instead of non-existent floor6.svg
        floorColor = 0x33cccc; // Cyan
        break;
      default:
        floorTextureNumber = 1;
        floorColor = 0x666666; // Gray
    }

    // Load the appropriate floor texture
    // Use existing textures from the game assets
    const texturePath = `/assets/textures/level/floor${floorTextureNumber}.svg`;
    console.log(`Loading floor texture: ${texturePath}`);

    // Create a temporary texture for initial material creation
    const tempTexture = new THREE.Texture();
    tempTexture.wrapS = THREE.RepeatWrapping;
    tempTexture.wrapT = THREE.RepeatWrapping;
    tempTexture.repeat.set(4, 4);

    // Create floor material with color (texture will be loaded later)
    const floorMaterial = new THREE.MeshStandardMaterial({
      color: floorColor,
      roughness: 0.8,
      metalness: 0.2,
      map: tempTexture
    });

    // Load the actual texture
    textureLoader.load(
      texturePath,
      (texture) => {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        floorMaterial.map = texture;
        floorMaterial.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn(`Error loading floor texture ${texturePath}:`, error);
        // Try fallback texture
        textureLoader.load('/assets/textures/level/floor1.svg', (fallback) => {
          fallback.wrapS = THREE.RepeatWrapping;
          fallback.wrapT = THREE.RepeatWrapping;
          fallback.repeat.set(4, 4);
          floorMaterial.map = fallback;
          floorMaterial.needsUpdate = true;
        });
      }
    );

    // Create floor mesh
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);

    // Position floor
    floor.position.set(this.position.x, 0, this.position.z);
    floor.rotation.x = -Math.PI / 2; // Rotate to be horizontal

    // Add to scene
    this.scene.add(floor);
    this.meshes.push(floor);

    // Add physics for floor with proper material
    const floorShape = new CANNON.Plane();
    const floorMaterialPhysics = new CANNON.Material('dungeonFloor');
    console.log('Creating dungeon floor with material:', floorMaterialPhysics.name);

    // CRITICAL FIX: Set special properties for the floor material
    floorMaterialPhysics.friction = 0.05; // Almost no friction
    floorMaterialPhysics.restitution = 0.0; // No bounce

    const floorBody = new CANNON.Body({
      mass: 0, // Static body
      position: new CANNON.Vec3(this.position.x, 0, this.position.z),
      shape: floorShape,
      material: floorMaterialPhysics,
      collisionFilterGroup: 1, // Standard group
      collisionFilterMask: 1, // Only collide with standard group
      fixedRotation: true // Prevent rotation
    });

    // Rotate to be horizontal
    floorBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);

    // Create contact material with player material
    // This ensures consistent movement between surface and dungeon
    // Get all contact materials to find the player material
    const contactMaterials = this.world.contactmaterials;
    let playerMaterial: CANNON.Material | null = null;

    // Look for existing contact materials that might have the player material
    for (let i = 0; i < contactMaterials.length; i++) {
      const cm = contactMaterials[i];
      if (cm.materials[0].name === 'player') {
        playerMaterial = cm.materials[0];
        break;
      } else if (cm.materials[1].name === 'player') {
        playerMaterial = cm.materials[1];
        break;
      }
    }

    // If we found the player material, create a contact material
    if (playerMaterial) {
      console.log('Found player material:', playerMaterial.name);
      const floorPlayerContactMaterial = new CANNON.ContactMaterial(
        floorMaterialPhysics,
        playerMaterial,
        {
          friction: 0.0, // CRITICAL FIX: Zero friction for dungeon floor
          restitution: 0.0, // No bounce at all
          contactEquationStiffness: 1e8, // Higher stiffness for more stable contact
          contactEquationRelaxation: 3, // Lower relaxation for more stable contact
          frictionEquationStiffness: 1e8, // Higher stiffness for friction equations
          frictionEquationRelaxation: 3 // Lower relaxation for friction equations
        }
      );
      this.world.addContactMaterial(floorPlayerContactMaterial);
      console.log('Added floor-player contact material with friction:', floorPlayerContactMaterial.friction);
    } else {
      console.error('Player material not found! This will cause movement issues.');
    }

    // Add to world
    this.world.addBody(floorBody);
    this.bodies.push(floorBody);
  }

  /**
   * Create walls for the room
   */
  private createWalls(): void {
    // Wall thickness
    const wallThickness = 1;

    // Create walls on all four sides with doorways

    // Create a texture loader
    const textureLoader = new THREE.TextureLoader();

    // Use existing SVG textures from the assets folder
    // Choose a wall texture based on room type (using the same mapping logic as floors)
    let wallTextureNumber;

    switch (this.type) {
      case DungeonRoomType.ENTRANCE:
        wallTextureNumber = 1; // wall1.svg
        break;
      case DungeonRoomType.REGULAR:
        wallTextureNumber = 2; // wall2.svg
        break;
      case DungeonRoomType.BOSS:
        wallTextureNumber = 5; // wall5.svg
        break;
      case DungeonRoomType.EXIT:
        wallTextureNumber = 3; // wall3.svg
        break;
      default:
        wallTextureNumber = 1; // wall1.svg
    }

    // Load the appropriate wall texture using existing game textures
    const wallTexturePath = `/assets/textures/level/wall${wallTextureNumber}.svg`;
    console.log(`Loading wall texture: ${wallTexturePath}`);

    // Create wall material first so we can update it in the error callback
    // Use brighter color and lower roughness to make walls more visible
    const wallMaterial = new THREE.MeshStandardMaterial({
      color: 0xcccccc, // Brighter color
      roughness: 0.5,  // Lower roughness for better light reflection
      metalness: 0.3,  // Slightly higher metalness
      side: THREE.DoubleSide, // Make sure walls are visible from both sides
      emissive: 0x222222, // Slight emissive to ensure visibility even in darker areas
      emissiveIntensity: 0.1
    });

    // Load the wall texture
    textureLoader.load(
      wallTexturePath,
      (texture) => {
        console.log(`Successfully loaded wall texture: ${wallTexturePath}`);
        // Repeat texture to avoid stretching
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 2);
        wallMaterial.map = texture;
        wallMaterial.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn(`Error loading wall texture ${wallTexturePath}:`, error);
        // Try fallback texture
        textureLoader.load('/assets/textures/level/wall1.svg', (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.repeat.set(4, 2);
          wallMaterial.map = texture;
          wallMaterial.needsUpdate = true;
        });
      }
    );

    // Create doorway in the middle of the north wall
    const doorWidth = 6;
    const doorHeight = 4;
    const segmentWidth = (this.size - doorWidth) / 2;

    // Left segment of north wall
    const northWallLeftGeometry = new THREE.BoxGeometry(segmentWidth, this.height, wallThickness);
    const northWallLeft = new THREE.Mesh(northWallLeftGeometry, wallMaterial);
    northWallLeft.position.set(
      this.position.x - doorWidth/2 - segmentWidth/2,
      this.height / 2,
      this.position.z - this.size / 2
    );
    this.scene.add(northWallLeft);
    this.meshes.push(northWallLeft);

    // Right segment of north wall
    const northWallRightGeometry = new THREE.BoxGeometry(segmentWidth, this.height, wallThickness);
    const northWallRight = new THREE.Mesh(northWallRightGeometry, wallMaterial);
    northWallRight.position.set(
      this.position.x + doorWidth/2 + segmentWidth/2,
      this.height / 2,
      this.position.z - this.size / 2
    );
    this.scene.add(northWallRight);
    this.meshes.push(northWallRight);

    // Top segment of north wall (above doorway)
    const northWallTopGeometry = new THREE.BoxGeometry(doorWidth, this.height - doorHeight, wallThickness);
    const northWallTop = new THREE.Mesh(northWallTopGeometry, wallMaterial);
    northWallTop.position.set(
      this.position.x,
      this.height - (this.height - doorHeight)/2,
      this.position.z - this.size / 2
    );
    this.scene.add(northWallTop);
    this.meshes.push(northWallTop);

    // Add physics for north wall segments
    const northWallLeftShape = new CANNON.Box(new CANNON.Vec3(
      segmentWidth / 2,
      this.height / 2,
      wallThickness / 2
    ));
    // Create wall material
    const wallMaterialPhysics = new CANNON.Material('dungeonWall');

    const northWallLeftBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        northWallLeft.position.x,
        northWallLeft.position.y,
        northWallLeft.position.z
      ),
      shape: northWallLeftShape,
      material: wallMaterialPhysics
    });

    // Create contact material with player material if it exists
    // Look for player material in contact materials
    let playerMaterialForWalls: CANNON.Material | null = null;
    const contactMaterials = this.world.contactmaterials;

    // Look for existing contact materials that might have the player material
    for (let i = 0; i < contactMaterials.length; i++) {
      const cm = contactMaterials[i];
      if (cm.materials[0].name === 'player') {
        playerMaterialForWalls = cm.materials[0];
        break;
      } else if (cm.materials[1].name === 'player') {
        playerMaterialForWalls = cm.materials[1];
        break;
      }
    }

    if (playerMaterialForWalls) {
      const wallPlayerContactMaterial = new CANNON.ContactMaterial(
        wallMaterialPhysics,
        playerMaterialForWalls,
        {
          friction: 0.1, // Lower friction for smoother movement
          restitution: 0.1, // Lower restitution to prevent bouncing
          contactEquationStiffness: 1e8, // Higher stiffness for more stable contact
          contactEquationRelaxation: 3 // Lower relaxation for more stable contact
        }
      );
      this.world.addContactMaterial(wallPlayerContactMaterial);
    }
    this.world.addBody(northWallLeftBody);
    this.bodies.push(northWallLeftBody);

    const northWallRightShape = new CANNON.Box(new CANNON.Vec3(
      segmentWidth / 2,
      this.height / 2,
      wallThickness / 2
    ));
    const northWallRightBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        northWallRight.position.x,
        northWallRight.position.y,
        northWallRight.position.z
      ),
      shape: northWallRightShape
    });
    this.world.addBody(northWallRightBody);
    this.bodies.push(northWallRightBody);

    const northWallTopShape = new CANNON.Box(new CANNON.Vec3(
      doorWidth / 2,
      (this.height - doorHeight) / 2,
      wallThickness / 2
    ));
    const northWallTopBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        northWallTop.position.x,
        northWallTop.position.y,
        northWallTop.position.z
      ),
      shape: northWallTopShape
    });
    this.world.addBody(northWallTopBody);
    this.bodies.push(northWallTopBody);

    // Create doorway in the middle of the south wall
    // Left segment of south wall
    const southWallLeftGeometry = new THREE.BoxGeometry(segmentWidth, this.height, wallThickness);
    const southWallLeft = new THREE.Mesh(southWallLeftGeometry, wallMaterial);
    southWallLeft.position.set(
      this.position.x - doorWidth/2 - segmentWidth/2,
      this.height / 2,
      this.position.z + this.size / 2
    );
    this.scene.add(southWallLeft);
    this.meshes.push(southWallLeft);

    // Right segment of south wall
    const southWallRightGeometry = new THREE.BoxGeometry(segmentWidth, this.height, wallThickness);
    const southWallRight = new THREE.Mesh(southWallRightGeometry, wallMaterial);
    southWallRight.position.set(
      this.position.x + doorWidth/2 + segmentWidth/2,
      this.height / 2,
      this.position.z + this.size / 2
    );
    this.scene.add(southWallRight);
    this.meshes.push(southWallRight);

    // Top segment of south wall (above doorway)
    const southWallTopGeometry = new THREE.BoxGeometry(doorWidth, this.height - doorHeight, wallThickness);
    const southWallTop = new THREE.Mesh(southWallTopGeometry, wallMaterial);
    southWallTop.position.set(
      this.position.x,
      this.height - (this.height - doorHeight)/2,
      this.position.z + this.size / 2
    );
    this.scene.add(southWallTop);
    this.meshes.push(southWallTop);

    // Add physics for south wall segments
    const southWallLeftShape = new CANNON.Box(new CANNON.Vec3(
      segmentWidth / 2,
      this.height / 2,
      wallThickness / 2
    ));
    const southWallLeftBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        southWallLeft.position.x,
        southWallLeft.position.y,
        southWallLeft.position.z
      ),
      shape: southWallLeftShape
    });
    this.world.addBody(southWallLeftBody);
    this.bodies.push(southWallLeftBody);

    const southWallRightShape = new CANNON.Box(new CANNON.Vec3(
      segmentWidth / 2,
      this.height / 2,
      wallThickness / 2
    ));
    const southWallRightBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        southWallRight.position.x,
        southWallRight.position.y,
        southWallRight.position.z
      ),
      shape: southWallRightShape
    });
    this.world.addBody(southWallRightBody);
    this.bodies.push(southWallRightBody);

    const southWallTopShape = new CANNON.Box(new CANNON.Vec3(
      doorWidth / 2,
      (this.height - doorHeight) / 2,
      wallThickness / 2
    ));
    const southWallTopBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        southWallTop.position.x,
        southWallTop.position.y,
        southWallTop.position.z
      ),
      shape: southWallTopShape
    });
    this.world.addBody(southWallTopBody);
    this.bodies.push(southWallTopBody);

    // Create doorway in the middle of the east wall
    // Bottom segment of east wall
    const eastWallBottomGeometry = new THREE.BoxGeometry(wallThickness, this.height, segmentWidth);
    const eastWallBottom = new THREE.Mesh(eastWallBottomGeometry, wallMaterial);
    eastWallBottom.position.set(
      this.position.x + this.size / 2,
      this.height / 2,
      this.position.z - doorWidth/2 - segmentWidth/2
    );
    this.scene.add(eastWallBottom);
    this.meshes.push(eastWallBottom);

    // Top segment of east wall
    const eastWallTopGeometry = new THREE.BoxGeometry(wallThickness, this.height, segmentWidth);
    const eastWallTop = new THREE.Mesh(eastWallTopGeometry, wallMaterial);
    eastWallTop.position.set(
      this.position.x + this.size / 2,
      this.height / 2,
      this.position.z + doorWidth/2 + segmentWidth/2
    );
    this.scene.add(eastWallTop);
    this.meshes.push(eastWallTop);

    // Middle segment of east wall (above doorway)
    const eastWallMiddleGeometry = new THREE.BoxGeometry(wallThickness, this.height - doorHeight, doorWidth);
    const eastWallMiddle = new THREE.Mesh(eastWallMiddleGeometry, wallMaterial);
    eastWallMiddle.position.set(
      this.position.x + this.size / 2,
      this.height - (this.height - doorHeight)/2,
      this.position.z
    );
    this.scene.add(eastWallMiddle);
    this.meshes.push(eastWallMiddle);

    // Add physics for east wall segments
    const eastWallBottomShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      this.height / 2,
      segmentWidth / 2
    ));
    const eastWallBottomBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        eastWallBottom.position.x,
        eastWallBottom.position.y,
        eastWallBottom.position.z
      ),
      shape: eastWallBottomShape
    });
    this.world.addBody(eastWallBottomBody);
    this.bodies.push(eastWallBottomBody);

    const eastWallTopShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      this.height / 2,
      segmentWidth / 2
    ));
    const eastWallTopBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        eastWallTop.position.x,
        eastWallTop.position.y,
        eastWallTop.position.z
      ),
      shape: eastWallTopShape
    });
    this.world.addBody(eastWallTopBody);
    this.bodies.push(eastWallTopBody);

    const eastWallMiddleShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      (this.height - doorHeight) / 2,
      doorWidth / 2
    ));
    const eastWallMiddleBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        eastWallMiddle.position.x,
        eastWallMiddle.position.y,
        eastWallMiddle.position.z
      ),
      shape: eastWallMiddleShape
    });
    this.world.addBody(eastWallMiddleBody);
    this.bodies.push(eastWallMiddleBody);

    // Create doorway in the middle of the west wall
    // Bottom segment of west wall
    const westWallBottomGeometry = new THREE.BoxGeometry(wallThickness, this.height, segmentWidth);
    const westWallBottom = new THREE.Mesh(westWallBottomGeometry, wallMaterial);
    westWallBottom.position.set(
      this.position.x - this.size / 2,
      this.height / 2,
      this.position.z - doorWidth/2 - segmentWidth/2
    );
    this.scene.add(westWallBottom);
    this.meshes.push(westWallBottom);

    // Top segment of west wall
    const westWallTopGeometry = new THREE.BoxGeometry(wallThickness, this.height, segmentWidth);
    const westWallTop = new THREE.Mesh(westWallTopGeometry, wallMaterial);
    westWallTop.position.set(
      this.position.x - this.size / 2,
      this.height / 2,
      this.position.z + doorWidth/2 + segmentWidth/2
    );
    this.scene.add(westWallTop);
    this.meshes.push(westWallTop);

    // Middle segment of west wall (above doorway)
    const westWallMiddleGeometry = new THREE.BoxGeometry(wallThickness, this.height - doorHeight, doorWidth);
    const westWallMiddle = new THREE.Mesh(westWallMiddleGeometry, wallMaterial);
    westWallMiddle.position.set(
      this.position.x - this.size / 2,
      this.height - (this.height - doorHeight)/2,
      this.position.z
    );
    this.scene.add(westWallMiddle);
    this.meshes.push(westWallMiddle);

    // Add physics for west wall segments
    const westWallBottomShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      this.height / 2,
      segmentWidth / 2
    ));
    const westWallBottomBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        westWallBottom.position.x,
        westWallBottom.position.y,
        westWallBottom.position.z
      ),
      shape: westWallBottomShape
    });
    this.world.addBody(westWallBottomBody);
    this.bodies.push(westWallBottomBody);

    const westWallTopShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      this.height / 2,
      segmentWidth / 2
    ));
    const westWallTopBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        westWallTop.position.x,
        westWallTop.position.y,
        westWallTop.position.z
      ),
      shape: westWallTopShape
    });
    this.world.addBody(westWallTopBody);
    this.bodies.push(westWallTopBody);

    const westWallMiddleShape = new CANNON.Box(new CANNON.Vec3(
      wallThickness / 2,
      (this.height - doorHeight) / 2,
      doorWidth / 2
    ));
    const westWallMiddleBody = new CANNON.Body({
      mass: 0,
      position: new CANNON.Vec3(
        westWallMiddle.position.x,
        westWallMiddle.position.y,
        westWallMiddle.position.z
      ),
      shape: westWallMiddleShape
    });
    this.world.addBody(westWallMiddleBody);
    this.bodies.push(westWallMiddleBody);
  }

  /**
   * Create ceiling for the room
   */
  private createCeiling(): void {
    // Create ceiling geometry
    const ceilingGeometry = new THREE.PlaneGeometry(this.size, this.size);

    // Create a texture loader
    const textureLoader = new THREE.TextureLoader();

    // Use existing SVG textures from the assets folder
    // Choose a ceiling texture based on room type (using the same mapping logic as floors)
    let ceilingTextureNumber;

    switch (this.type) {
      case DungeonRoomType.ENTRANCE:
        ceilingTextureNumber = 1; // ceiling1.svg
        break;
      case DungeonRoomType.REGULAR:
        ceilingTextureNumber = 2; // ceiling2.svg
        break;
      case DungeonRoomType.BOSS:
        ceilingTextureNumber = 5; // ceiling5.svg
        break;
      case DungeonRoomType.EXIT:
        ceilingTextureNumber = 3; // ceiling3.svg
        break;
      default:
        ceilingTextureNumber = 1; // ceiling1.svg
    }

    // Load the appropriate ceiling texture using existing game textures
    const ceilingTexturePath = `/assets/textures/level/ceiling${ceilingTextureNumber}.svg`;
    console.log(`Loading ceiling texture: ${ceilingTexturePath}`);

    // Create ceiling material first so we can update it in the error callback
    const ceilingMaterial = new THREE.MeshStandardMaterial({
      color: 0x666666,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    });

    textureLoader.load(
      ceilingTexturePath,
      (texture) => {
        console.log(`Successfully loaded ceiling texture: ${ceilingTexturePath}`);
        // Repeat texture to avoid stretching
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        ceilingMaterial.map = texture;
        ceilingMaterial.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn(`Error loading ceiling texture ${ceilingTexturePath}:`, error);
        // Try fallback texture
        textureLoader.load('/assets/textures/level/ceiling1.svg', (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.repeat.set(4, 4);
          ceilingMaterial.map = texture;
          ceilingMaterial.needsUpdate = true;
        });
      }
    );

    // Create ceiling mesh
    const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);

    // Position ceiling
    ceiling.position.set(this.position.x, this.height, this.position.z);
    ceiling.rotation.x = Math.PI / 2; // Rotate to be horizontal

    // Add to scene
    this.scene.add(ceiling);
    this.meshes.push(ceiling);

    // Add physics for ceiling with proper material
    const ceilingShape = new CANNON.Plane();
    const ceilingMaterialPhysics = new CANNON.Material('dungeonCeiling');
    const ceilingBody = new CANNON.Body({
      mass: 0, // Static body
      position: new CANNON.Vec3(this.position.x, this.height, this.position.z),
      shape: ceilingShape,
      material: ceilingMaterialPhysics
    });

    // Rotate to be horizontal
    ceilingBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), Math.PI / 2);

    // Add to world
    this.world.addBody(ceilingBody);
    this.bodies.push(ceilingBody);
  }

  /**
   * Create entrance room features
   */
  private createEntranceRoom(): void {
    // Add entrance portal
    const portalGeometry = new THREE.TorusGeometry(2, 0.3, 16, 32);
    const portalMaterial = new THREE.MeshStandardMaterial({
      color: 0x3366cc,
      emissive: 0x3366cc,
      emissiveIntensity: 0.5,
      metalness: 0.8,
      roughness: 0.2
    });

    const portal = new THREE.Mesh(portalGeometry, portalMaterial);
    portal.position.set(this.position.x, 2, this.position.z);
    portal.rotation.x = Math.PI / 2;
    this.scene.add(portal);
    this.meshes.push(portal);

    // Add portal particles
    const particleCount = 100;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const radius = 1.5 + Math.random() * 0.5;

      particlePositions[i * 3] = this.position.x + Math.cos(angle) * radius;
      particlePositions[i * 3 + 1] = 1 + Math.random() * 2;
      particlePositions[i * 3 + 2] = this.position.z + Math.sin(angle) * radius;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: 0x66ccff,
      size: 0.1,
      transparent: true,
      opacity: 0.7
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);
    this.meshes.push(particles);

    // Add welcome sign
    const signGeometry = new THREE.PlaneGeometry(3, 1);
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;

    const context = canvas.getContext('2d');
    if (context) {
      context.fillStyle = '#000000';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.fillStyle = '#ffffff';
      context.font = 'bold 48px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText('DUNGEON ENTRANCE', canvas.width / 2, canvas.height / 2);
    }

    const signTexture = new THREE.CanvasTexture(canvas);
    const signMaterial = new THREE.MeshBasicMaterial({
      map: signTexture,
      transparent: true,
      opacity: 0.9
    });

    const sign = new THREE.Mesh(signGeometry, signMaterial);
    sign.position.set(this.position.x, 3.5, this.position.z - 2);
    this.scene.add(sign);
    this.meshes.push(sign);
  }

  /**
   * Create regular room features
   */
  private createRegularRoom(): void {
    // Randomly decide if this room should have obstacles for cover
    const hasObstacles = this.random.between(0, 1) < 0.7; // 70% chance to have obstacles

    if (hasObstacles) {
      // Add some obstacles for cover
      const obstacleCount = this.random.intBetween(1, 3);
      for (let i = 0; i < obstacleCount; i++) {
        const angle = (i * Math.PI * 2) / obstacleCount + this.random.between(-0.5, 0.5);
        const distance = this.random.between(3, 6);

        const obstacleGeometry = new THREE.BoxGeometry(2, 1.5, 2);
        const obstacleMaterial = new THREE.MeshStandardMaterial({
          color: 0x666666,
          roughness: 0.7,
          metalness: 0.3
        });

        const obstacle = new THREE.Mesh(obstacleGeometry, obstacleMaterial);
        obstacle.position.set(
          this.position.x + Math.cos(angle) * distance,
          0.75,
          this.position.z + Math.sin(angle) * distance
        );

        this.scene.add(obstacle);
        this.meshes.push(obstacle);

        // Add physics for obstacle
        const obstacleShape = new CANNON.Box(new CANNON.Vec3(1, 0.75, 1));
        const obstacleBody = new CANNON.Body({
          mass: 0,
          position: new CANNON.Vec3(
            this.position.x + Math.cos(angle) * distance,
            0.75,
            this.position.z + Math.sin(angle) * distance
          ),
          shape: obstacleShape
        });

        this.world.addBody(obstacleBody);
        this.bodies.push(obstacleBody);
      }
    }

    // Occasionally add some decorative elements (25% chance)
    if (this.random.between(0, 1) < 0.25) {
      // Add a decorative element in the room
      const decorType = this.random.intBetween(0, 2);

      if (decorType === 0) {
        // Add a small pedestal with an object
        const pedestalGeometry = new THREE.CylinderGeometry(0.5, 0.7, 0.8, 16);
        const pedestalMaterial = new THREE.MeshStandardMaterial({
          color: 0x666666,
          roughness: 0.7,
          metalness: 0.3
        });

        const pedestal = new THREE.Mesh(pedestalGeometry, pedestalMaterial);
        pedestal.position.set(
          this.position.x + this.random.between(-5, 5),
          0.4,
          this.position.z + this.random.between(-5, 5)
        );
        this.scene.add(pedestal);
        this.meshes.push(pedestal);

        // Add object on pedestal
        const objectGeometry = new THREE.SphereGeometry(0.3, 16, 16);
        const objectMaterial = new THREE.MeshStandardMaterial({
          color: 0x3366cc,
          emissive: 0x3366cc,
          emissiveIntensity: 0.3,
          metalness: 0.8,
          roughness: 0.2
        });

        const object = new THREE.Mesh(objectGeometry, objectMaterial);
        object.position.set(pedestal.position.x, 1.0, pedestal.position.z);
        this.scene.add(object);
        this.meshes.push(object);
      } else if (decorType === 1) {
        // Add a small chest
        const chestBaseGeometry = new THREE.BoxGeometry(1, 0.5, 0.8);
        const chestLidGeometry = new THREE.BoxGeometry(1, 0.3, 0.8);
        const chestMaterial = new THREE.MeshStandardMaterial({
          color: 0x8B4513,
          roughness: 0.8,
          metalness: 0.2
        });

        // Position for the chest
        const chestX = this.position.x + this.random.between(-5, 5);
        const chestZ = this.position.z + this.random.between(-5, 5);

        // Chest base
        const chestBase = new THREE.Mesh(chestBaseGeometry, chestMaterial);
        chestBase.position.set(chestX, 0.25, chestZ);
        this.scene.add(chestBase);
        this.meshes.push(chestBase);

        // Chest lid
        const chestLid = new THREE.Mesh(chestLidGeometry, chestMaterial);
        chestLid.position.set(chestX, 0.65, chestZ - 0.3);
        chestLid.rotation.x = -Math.PI / 4; // Slightly open
        this.scene.add(chestLid);
        this.meshes.push(chestLid);
      } else {
        // Add a small light source
        const lightColor = this.random.intBetween(0, 2) === 0 ? 0x3366cc :
                          this.random.intBetween(0, 1) === 0 ? 0x33cc33 : 0xcc3333;

        const light = new THREE.PointLight(lightColor, 0.8, 8);
        light.position.set(
          this.position.x + this.random.between(-5, 5),
          2,
          this.position.z + this.random.between(-5, 5)
        );
        this.scene.add(light);
        this.meshes.push(light);
      }
    }
  }

  /**
   * Create boss room features
   */
  private createBossRoom(): void {
    // Add boss arena markings
    const arenaGeometry = new THREE.CircleGeometry(8, 32);
    const arenaMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    });

    const arena = new THREE.Mesh(arenaGeometry, arenaMaterial);
    arena.position.set(this.position.x, 0.1, this.position.z);
    arena.rotation.x = -Math.PI / 2;
    this.scene.add(arena);
    this.meshes.push(arena);

    // Add boss throne
    const throneBaseGeometry = new THREE.BoxGeometry(3, 1, 3);
    const throneBackGeometry = new THREE.BoxGeometry(3, 4, 1);
    const throneMaterial = new THREE.MeshStandardMaterial({
      color: 0x990000,
      roughness: 0.7,
      metalness: 0.5
    });

    // Throne base
    const throneBase = new THREE.Mesh(throneBaseGeometry, throneMaterial);
    throneBase.position.set(this.position.x, 0.5, this.position.z - 6);
    this.scene.add(throneBase);
    this.meshes.push(throneBase);

    // Throne back
    const throneBack = new THREE.Mesh(throneBackGeometry, throneMaterial);
    throneBack.position.set(this.position.x, 3, this.position.z - 7);
    this.scene.add(throneBack);
    this.meshes.push(throneBack);

    // Add boss room lighting
    const light = new THREE.PointLight(0xff0000, 1, 15);
    light.position.set(this.position.x, 5, this.position.z);
    this.scene.add(light);
    this.meshes.push(light);
  }

  /**
   * Create exit room features
   */
  private createExitRoom(): void {
    // Add exit portal (initially locked)
    const portalGeometry = new THREE.TorusGeometry(2, 0.3, 16, 32);
    const portalMaterial = new THREE.MeshStandardMaterial({
      color: 0x33cccc,
      emissive: 0x33cccc,
      emissiveIntensity: 0.5,
      metalness: 0.8,
      roughness: 0.2,
      transparent: true,
      opacity: 0.5 // Initially dim to show it's locked
    });

    const portal = new THREE.Mesh(portalGeometry, portalMaterial);
    portal.position.set(this.position.x, 2, this.position.z);
    portal.rotation.x = Math.PI / 2;
    portal.userData.isExitPortal = true; // Mark for later reference
    this.scene.add(portal);
    this.meshes.push(portal);

    // Add locked indicator
    const lockGeometry = new THREE.BoxGeometry(0.5, 0.7, 0.2);
    const lockMaterial = new THREE.MeshStandardMaterial({
      color: 0xcc3333,
      metalness: 0.8,
      roughness: 0.2
    });

    const lock = new THREE.Mesh(lockGeometry, lockMaterial);
    lock.position.set(this.position.x, 2, this.position.z + 2.3);
    lock.userData.isExitLock = true; // Mark for later reference
    this.scene.add(lock);
    this.meshes.push(lock);

    // Add exit sign
    const signGeometry = new THREE.PlaneGeometry(3, 1);
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;

    const context = canvas.getContext('2d');
    if (context) {
      context.fillStyle = '#000000';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.fillStyle = '#ffffff';
      context.font = 'bold 48px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText('DUNGEON EXIT', canvas.width / 2, canvas.height / 2);
      context.font = '24px Arial';
      context.fillText('(Defeat boss to unlock)', canvas.width / 2, canvas.height / 2 + 40);
    }

    const signTexture = new THREE.CanvasTexture(canvas);
    const signMaterial = new THREE.MeshBasicMaterial({
      map: signTexture,
      transparent: true,
      opacity: 0.9
    });

    const sign = new THREE.Mesh(signGeometry, signMaterial);
    sign.position.set(this.position.x, 3.5, this.position.z - 2);
    this.scene.add(sign);
    this.meshes.push(sign);
  }

  /**
   * Get the room type
   */
  public getRoomType(): DungeonRoomType {
    return this.type;
  }

  /**
   * Set the room type
   */
  public setRoomType(type: DungeonRoomType): void {
    this.type = type;
  }

  /**
   * Get the room position
   */
  public getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  /**
   * Get the room size
   */
  public getSize(): number {
    return this.size;
  }

  /**
   * Unlock the exit portal
   */
  public unlockExit(): void {
    this.exitUnlocked = true;

    // Find exit portal and lock
    this.meshes.forEach(mesh => {
      if (mesh.userData.isExitPortal) {
        // Brighten portal
        if (mesh instanceof THREE.Mesh && mesh.material instanceof THREE.MeshStandardMaterial) {
          mesh.material.opacity = 1.0;
          mesh.material.emissiveIntensity = 1.0;
        }
      }

      if (mesh.userData.isExitLock) {
        // Remove lock
        this.scene.remove(mesh);
      }
    });
  }

  /**
   * Check if a position is inside this room
   */
  public containsPosition(position: THREE.Vector3): boolean {
    return this.boundingBox.containsPoint(position);
  }

  // Methods already defined above

  /**
   * Check if exit is unlocked
   */
  public isExitUnlocked(): boolean {
    return this.exitUnlocked;
  }

  // We're now using existing SVG textures instead of procedural textures

  /**
   * Dispose of all room resources
   */
  public dispose(): void {
    // Remove all meshes from scene
    this.meshes.forEach(mesh => {
      this.scene.remove(mesh);

      // Dispose of geometries and materials
      if (mesh instanceof THREE.Mesh) {
        if (mesh.geometry) mesh.geometry.dispose();

        if (Array.isArray(mesh.material)) {
          mesh.material.forEach(material => material.dispose());
        } else if (mesh.material) {
          mesh.material.dispose();
        }
      }
    });

    // Remove all bodies from physics world
    this.bodies.forEach(body => {
      this.world.removeBody(body);
    });

    // Clear arrays
    this.meshes = [];
    this.bodies = [];
  }
}