import { PointerLockControls } from 'three/examples/jsm/controls/PointerLockControls';
import * as THREE from 'three';
import { useIsMobile } from '@/hooks/use-mobile';

class InputHandler {
  private keys: { [key: string]: boolean } = {};
  private moveDirection: THREE.Vector3 = new THREE.Vector3();
  private jumping: boolean = false;
  private jetpackActive: boolean = false;
  private controls: PointerLockControls;
  private ammoSwitch: number = 0; // For weapon switching, 0 means no switch
  private fireGrapplingHook: boolean = false; // For grappling hook activation
  private interactPressed: boolean = false; // For interaction with objects
  private mouseClicked: boolean = false; // For weapon firing
  private isMultiplayer: boolean = false; // Added for multiplayer mode
  private isMobile: boolean = false; // Flag for mobile device detection
  private mobileDirection: THREE.Vector3 = new THREE.Vector3(); // For mobile joystick input

  constructor(container: HTMLElement, controls: PointerLockControls, isMobile: boolean = false) {
    this.controls = controls;
    this.isMobile = isMobile;

    // Initialize key state with expanded keys
    this.keys = {
      'KeyW': false,
      'KeyA': false,
      'KeyS': false,
      'KeyD': false,
      'Space': false,
      'ShiftLeft': false, // For jetpack
      'KeyG': false, // For grappling hook
      'KeyE': false, // For interaction
      'Digit1': false, // For weapon 1
      'Digit2': false, // For weapon 2
      'Digit3': false  // For weapon 3
    };

    // Only set up keyboard/mouse controls if not on mobile
    if (!this.isMobile) {
      // Event listeners
      document.addEventListener('keydown', this.onKeyDown.bind(this));
      document.addEventListener('keyup', this.onKeyUp.bind(this));

      // On click, lock pointer if not already locked
      container.addEventListener('click', () => {
        if (!this.controls.isLocked) {
          this.controls.lock();
        } else {
          // If pointer is locked, register the click for weapon firing
          this.mouseClicked = true;
        }
      });

      // Handle pointer lock changes
      document.addEventListener('pointerlockchange', this.onPointerLockChange.bind(this));
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    // Only handle input when controls are locked, unless in multiplayer mode
    if (!this.controls.isLocked && !this.isMultiplayer) return;

    if (event.code in this.keys) {
      this.keys[event.code] = true;

      // For jump, only set to true on initial press
      if (event.code === 'Space' && !event.repeat) {
        this.jumping = true;
      }

      // For jetpack, just track the key state
      if (event.code === 'ShiftLeft') {
        this.jetpackActive = true;
      }

      // Grappling hook activation
      if (event.code === 'KeyG' && !event.repeat) {
        this.fireGrapplingHook = true;
      }

      // Interaction activation
      if (event.code === 'KeyE' && !event.repeat) {
        this.interactPressed = true;
      }

      // Check for weapon switching keys (one-shot events)
      if (!event.repeat) {
        if (event.code === 'Digit1') this.ammoSwitch = 1;
        if (event.code === 'Digit2') this.ammoSwitch = 2;
        if (event.code === 'Digit3') this.ammoSwitch = 3;
      }
    }
  }

  onKeyUp(event: KeyboardEvent): void {
    if (event.code in this.keys) {
      this.keys[event.code] = false;

      // For jump, reset jump state
      if (event.code === 'Space') {
        this.jumping = false;
      }

      // For jetpack, update state
      if (event.code === 'ShiftLeft') {
        this.jetpackActive = false;
      }
    }
  }

  onPointerLockChange(): void {
    // Reset all keys when pointer lock changes to avoid stuck keys
    if (!this.controls.isLocked) {
      Object.keys(this.keys).forEach(key => {
        this.keys[key] = false;
      });
      this.jumping = false;
      this.jetpackActive = false;
      this.ammoSwitch = 0;
    }
  }

  getMoveDirection(): THREE.Vector3 {
    // If on mobile, return the mobile direction vector
    if (this.isMobile) {
      return this.mobileDirection;
    }

    // Calculate move direction based on WASD keys for desktop
    this.moveDirection.set(0, 0, 0);

    if (this.keys['KeyW']) this.moveDirection.z -= 1;
    if (this.keys['KeyS']) this.moveDirection.z += 1;
    if (this.keys['KeyA']) this.moveDirection.x -= 1;
    if (this.keys['KeyD']) this.moveDirection.x += 1;

    // Normalize if moving diagonally
    if (this.moveDirection.length() > 1) {
      this.moveDirection.normalize();
    }

    return this.moveDirection;
  }

  isJumping(): boolean {
    // Return jump state and reset it
    const jumping = this.jumping;
    this.jumping = false;
    return jumping;
  }

  isJetpackActive(): boolean {
    // Return current jetpack state (don't reset)
    return this.jetpackActive;
  }

  getAmmoSwitch(): number {
    // Return the ammo switch and reset it
    const switchValue = this.ammoSwitch;
    this.ammoSwitch = 0;
    return switchValue;
  }

  isGrapplingHookFired(): boolean {
    // Return grappling hook state and reset it
    const fired = this.fireGrapplingHook;
    this.fireGrapplingHook = false;
    return fired;
  }

  isInteractButtonPressed(): boolean {
    // Return interaction state and reset it
    const interacted = this.interactPressed;
    this.interactPressed = false;
    return interacted;
  }

  isMouseClicked(): boolean {
    // Return mouse click state and reset it
    const clicked = this.mouseClicked;
    this.mouseClicked = false;
    return clicked;
  }

  // Methods to check current key state without consuming events
  isMoveForwardActive(): boolean {
      return !this.isMobile && this.keys['KeyW'];
  }

  isMoveBackwardActive(): boolean {
      return !this.isMobile && this.keys['KeyS'];
  }

  isMoveLeftActive(): boolean {
      return !this.isMobile && this.keys['KeyA'];
  }

  isMoveRightActive(): boolean {
      return !this.isMobile && this.keys['KeyD'];
  }

  isAscendActive(): boolean {
      return !this.isMobile && this.keys['Space'];
  }

  isDescendActive(): boolean {
      return !this.isMobile && this.keys['ShiftLeft'];
  }

  // Set multiplayer mode
  setMultiplayerMode(enabled: boolean): void {
    this.isMultiplayer = enabled;
    //console.log(`Input handler multiplayer mode: ${enabled}`);

    // If enabling multiplayer mode, ensure controls are locked - REMOVED AUTOMATIC LOCK
    /*
    if (enabled && this.controls) {
      this.controls.lock();
    }
    */
  }

  // Set mobile mode
  setMobileMode(enabled: boolean): void {
    this.isMobile = enabled;
  }

  // Mobile-specific methods
  setMobileDirection(direction: THREE.Vector3): void {
    this.mobileDirection.copy(direction);
  }

  setMobileJump(): void {
    this.jumping = true;
  }

  setMobileJetpack(active: boolean): void {
    this.jetpackActive = active;
  }

  setMobileGrapplingHook(): void {
    this.fireGrapplingHook = true;
  }

  setMobileInteract(): void {
    this.interactPressed = true;
  }

  setMobileShoot(): void {
    this.mouseClicked = true;
  }

  setMobileAmmoSwitch(ammoIndex: number): void {
    this.ammoSwitch = ammoIndex;
  }
}

export default InputHandler;