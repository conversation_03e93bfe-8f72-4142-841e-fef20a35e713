lockdown-install.js:1 Removing unpermitted intrinsics
(index):129 Vite HMR WebSocket connections disabled, but tournament connections allowed
(index):101 Blocked Vite HMR WebSocket connection: http://localhost:5001/__vite_hmr?token=d0AxCxTlcAM2
client:533 [vite] failed to connect to websocket (TypeError: socket2.addEventListener is not a function). 
(anonymous) @ client:533
chunk-RPCDYKBN.js?v=6ac7c29a:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ethers.js?v=6ac7c29a:13 Module "buffer" has been externalized for browser compatibility. Cannot access "buffer.Buffer" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.
get @ ethers.js?v=6ac7c29a:13
Web3Context.tsx:49 Web3Context initialized in development mode: true
disableHmr.ts:11 Development mode detected, disabling Vite HMR WebSocket connections
disableHmr.ts:74 Vite HMR WebSocket connections disabled, tournament connections allowed
App.tsx:56 Home page detected - preventing WebSocket connections
Web3Context.tsx:105 Wallet is already connected: ******************************************
:5001/api/pets/wallet/******************************************:1 
            
            
           Failed to load resource: net::ERR_CONNECTION_REFUSED
petService.ts:55 Error fetching pet specters: TypeError: Failed to fetch
    at PetService.getPetSpectersByWallet (petService.ts:49:30)
    at checkForNFTPets (PVPRequirementDialog.tsx:40:41)
    at PVPRequirementDialog.tsx:57:5
    at commitHookEffectListMount (chunk-RPCDYKBN.js?v=6ac7c29a:16915:34)
    at commitPassiveMountOnFiber (chunk-RPCDYKBN.js?v=6ac7c29a:18156:19)
    at commitPassiveMountEffects_complete (chunk-RPCDYKBN.js?v=6ac7c29a:18129:17)
    at commitPassiveMountEffects_begin (chunk-RPCDYKBN.js?v=6ac7c29a:18119:15)
    at commitPassiveMountEffects (chunk-RPCDYKBN.js?v=6ac7c29a:18109:11)
    at flushPassiveEffectsImpl (chunk-RPCDYKBN.js?v=6ac7c29a:19490:11)
    at flushPassiveEffects (chunk-RPCDYKBN.js?v=6ac7c29a:19447:22)
getPetSpectersByWallet @ petService.ts:55
PVPRequirementDialog.tsx:45 Error checking for NFT pets: TypeError: Failed to fetch
    at PetService.getPetSpectersByWallet (petService.ts:49:30)
    at checkForNFTPets (PVPRequirementDialog.tsx:40:41)
    at PVPRequirementDialog.tsx:57:5
    at commitHookEffectListMount (chunk-RPCDYKBN.js?v=6ac7c29a:16915:34)
    at commitPassiveMountOnFiber (chunk-RPCDYKBN.js?v=6ac7c29a:18156:19)
    at commitPassiveMountEffects_complete (chunk-RPCDYKBN.js?v=6ac7c29a:18129:17)
    at commitPassiveMountEffects_begin (chunk-RPCDYKBN.js?v=6ac7c29a:18119:15)
    at commitPassiveMountEffects (chunk-RPCDYKBN.js?v=6ac7c29a:18109:11)
    at flushPassiveEffectsImpl (chunk-RPCDYKBN.js?v=6ac7c29a:19490:11)
    at flushPassiveEffects (chunk-RPCDYKBN.js?v=6ac7c29a:19447:22)
checkForNFTPets @ PVPRequirementDialog.tsx:45
useMultiplayer.ts:915 Auto-connect disabled - not connecting automatically
Game.tsx:567 Initializing game engine
GameEngine.ts:4304 Created dungeon entrance at (-56.305179952440426, 7.447310621264527e-15, -33.53970533883926)
App.tsx:121 Not on home page - restoring WebSocket connections
GameEngine.ts:413 Attempting to lock pointer on game start
Player.ts:538 Player is now grounded. Contact with: unknown
llmService.ts:37 LLM enhanced prompt: Pixel art of a fantasy version of Bored Ape Einstein floating in front of a gray background, wears a laurel wreath crown and carries a glowing globe of energy in one hand and holds a scroll with intricate, swirling patterns in the other, sporting a mischievous grin with yellow glowing eyes and a wispy, flowing beard that resembles smoke.
aiGenerationService.ts:48 Sending enhanced prompt to server: Pixel art of a fantasy version of Bored Ape Einstein floating in front of a gray background, wears a laurel wreath crown and carries a glowing globe of energy in one hand and holds a scroll with intricate, swirling patterns in the other, sporting a mischievous grin with yellow glowing eyes and a wispy, flowing beard that resembles smoke.
AIPetGenerationDialog.tsx:168 Stored pet image URL in local storage with key: pet_image_pet-1745300199145
AIPetGenerationDialog.tsx:237 Created pet: {gameId: 'pet-1745300199145', name: 'Bob', walletAddress: '******************************************', specterType: 'WISP', level: 1, …}
Web3Context.tsx:274 mintNFT called with: {tokenURI: '{"name":"Bob","description":"A WISP Pet Specter fr…nd a wispy, flowing beard that resembles smoke."}', price: '0.001', petSpecterId: 'pet-1745300199145', isDevelopment: true, chainId: 80002}
Web3Context.tsx:292 Using mock NFT implementation? true {isDevelopment: true, isTestMode: true, chainId: 80002, contractAddress: '******************************************'}
Web3Context.tsx:294 Using mock NFT minting in development mode
Web3Context.tsx:326 Linking pet with gameId: pet-1745300199145 to NFT: 1745300199250
petService.ts:157 Linking pet specter pet-1745300199145 to NFT 1745300199250 for wallet ******************************************
petService.ts:245 Successfully linked pet specter to NFT: {gameId: 'pet-1745300199145', name: 'Bob', walletAddress: '******************************************', specterType: 'WISP', level: 1, …}
AIPetGenerationDialog.tsx:248 Stored NFT pet image URL in local storage with key: nft_pet_image_1745300199250
petService.ts:157 Linking pet specter pet-1745300199145 to NFT 1745300199250 for wallet ******************************************
petService.ts:245 Successfully linked pet specter to NFT: {gameId: 'pet-1745300199145', name: 'Bob', walletAddress: '******************************************', specterType: 'WISP', level: 1, …}
Game.tsx:539 AI Pet dialog closed, resuming game
App.tsx:56 Home page detected - preventing WebSocket connections
useMultiplayer.ts:915 Auto-connect disabled - not connecting automatically
Game.tsx:567 Initializing game engine
GameEngine.ts:4304 Created dungeon entrance at (7.937422558050295, -1.448568833794394e-14, 65.23774060096947)
App.tsx:121 Not on home page - restoring WebSocket connections
GameEngine.ts:413 Attempting to lock pointer on game start
GameEngine.ts:3215 Loading 1 pet specters for wallet ******************************************
PetSpecter.ts:176 Loading NFT pet texture from: /uploads/images/6fc6f421092a23fffda6b11770858c52.png
GameEngine.ts:1915 Created pet specter "Bob" of type WISP with custom image
PetSpecter.ts:183 NFT pet texture loaded successfully
Player.ts:538 Player is now grounded. Contact with: unknown
App.tsx:56 Home page detected - preventing WebSocket connections
@radix-ui_react-dialog.js?v=6ac7c29a:336 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anonymous) @ @radix-ui_react-dialog.js?v=6ac7c29a:336
commitHookEffectListMount @ chunk-RPCDYKBN.js?v=6ac7c29a:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js?v=6ac7c29a:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js?v=6ac7c29a:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js?v=6ac7c29a:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js?v=6ac7c29a:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js?v=6ac7c29a:19490
flushPassiveEffects @ chunk-RPCDYKBN.js?v=6ac7c29a:19447
commitRootImpl @ chunk-RPCDYKBN.js?v=6ac7c29a:19416
commitRoot @ chunk-RPCDYKBN.js?v=6ac7c29a:19277
performSyncWorkOnRoot @ chunk-RPCDYKBN.js?v=6ac7c29a:18895
flushSyncCallbacks @ chunk-RPCDYKBN.js?v=6ac7c29a:9119
(anonymous) @ chunk-RPCDYKBN.js?v=6ac7c29a:18627
PVPArenaDialog.tsx:454 Created test battle with ID: 2c60ec8c-0607-4fc4-b021-cefa09818254. Joining as spectator.
App.tsx:37 PVP page detected - allowing all WebSocket connections
App.tsx:121 Not on home page - restoring WebSocket connections
useTournamentBattleConnection.ts:619 Tournament Battle: Auto-connecting to WebSocket server...
webSocketUtils.ts:113 Using WebSocket host: 127.0.0.1 and port: 5001
webSocketUtils.ts:133 Generated WebSocket URL: ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254
useTournamentBattleConnection.ts:161 Tournament Battle: Connecting to WebSocket server at: ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254 (new connection)
useTournamentBattleConnection.ts:165 Tournament Battle: Creating WebSocket connection with consolidated WebSocket utility
webSocketUtils.ts:35 Creating tournament WebSocket connection to ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254
unifiedWebSocket.ts:31 Creating WebSocket connection to ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254 with ID tournament_1745300220721_xkcdvsz6n
(index):117 Tournament WebSocket connection detected in index.html script, using native WebSocket: ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254
useTournamentBattleConnection.ts:173 Tournament Battle: WebSocket connection stored in window.__tournamentWebSocket for debugging
useTournamentBattleConnection.ts:175 Tournament Battle: Created WebSocket connection with readyState: 0
useTournamentBattleConnection.ts:176 Tournament Battle: WebSocket URL: ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254
useTournamentBattleConnection.ts:177 Tournament Battle: WebSocket protocol: none
useTournamentBattleConnection.ts:178 Tournament Battle: WebSocket extensions: none
useTournamentBattleConnection.ts:179 Tournament Battle: WebSocket binaryType: blob
pvp.tsx:101 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
GameEngine.ts:3199 Skipping pet specters loading - spectator mode or disabled
pvp.tsx:112 Creating PVP arena
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
useTournamentBattleConnection.ts:633 Tournament Battle: On PVP page, keeping WebSocket connection active
pvp.tsx:163 Tournament Battle: Still on PVP page, keeping WebSocket connections active
useTournamentBattleConnection.ts:619 Tournament Battle: Auto-connecting to WebSocket server...
useTournamentBattleConnection.ts:127 Tournament Battle: WebSocket connection already in progress...
pvp.tsx:101 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
GameEngine.ts:3199 Skipping pet specters loading - spectator mode or disabled
pvp.tsx:112 Creating PVP arena
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
App.tsx:37 PVP page detected - allowing all WebSocket connections
unifiedWebSocket.ts:65 WebSocket connection tournament_1745300220721_xkcdvsz6n opened
useTournamentBattleConnection.ts:196 Tournament Battle: WebSocket connection established
useTournamentBattleConnection.ts:197 Tournament Battle: WebSocket readyState: 1
useTournamentBattleConnection.ts:198 Tournament Battle: WebSocket URL: ws://127.0.0.1:5001/tournament?token=******************************************&battleId=2c60ec8c-0607-4fc4-b021-cefa09818254
useTournamentBattleConnection.ts:199 Tournament Battle: WebSocket protocol: 
useTournamentBattleConnection.ts:200 Tournament Battle: WebSocket extensions: 
useTournamentBattleConnection.ts:201 Tournament Battle: WebSocket binaryType: blob
useTournamentBattleConnection.ts:223 Tournament Battle: Auto-joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 after connection established
pvp.tsx:22 Tournament Battle: Connected to game server with ID: tournament_1745300221515_m52icauct
pvp.tsx:32 Tournament Battle: Connection established, now joining battle as spectator
useTournamentBattleConnection.ts:417 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
useTournamentBattleConnection.ts:420 Tournament Battle: Cannot join tournament battle: WebSocket not connected or no connection ID
(anonymous) @ useTournamentBattleConnection.ts:420
onConnect @ pvp.tsx:35
ws.onopen @ useTournamentBattleConnection.ts:276
useTournamentBattleConnection.ts:424 Tournament Battle: Attempting to connect before joining tournament battle...
useTournamentBattleConnection.ts:127 Tournament Battle: WebSocket connection already in progress...
pvp.tsx:39 Tournament Battle: Telling game engine to connect to battle...
GameEngine.ts:4603 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
useTournamentBattleConnection.ts:633 Tournament Battle: On PVP page, keeping WebSocket connection active
pvp.tsx:163 Tournament Battle: Still on PVP page, keeping WebSocket connections active
useTournamentBattleConnection.ts:619 Tournament Battle: Auto-connecting to WebSocket server...
useTournamentBattleConnection.ts:127 Tournament Battle: WebSocket connection already in progress...
pvp.tsx:101 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
GameEngine.ts:3199 Skipping pet specters loading - spectator mode or disabled
pvp.tsx:112 Creating PVP arena
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300220751, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300220751,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300220751, sender: 'server'}
useTournamentBattleConnection.ts:85 Tournament Battle: Received battle update
pvp.tsx:54 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
App.tsx:37 PVP page detected - allowing all WebSocket connections
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_join', data: {…}, timestamp: 1745300220753, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_join","data":{"id":"1ae93a6e-d1d9-40a6-bb95-6b76adb4ee45","message":"Connected to tournament battle server for battle 2c60ec8c-0607-4fc4-b021-cefa09818254","isTournamentConnection":true,"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","success":true},"timestamp":1745300220753,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type tournament_battle_join: {type: 'tournament_battle_join', data: {…}, timestamp: 1745300220753, sender: 'server'}
useTournamentBattleConnection.ts:69 Tournament Battle: Join successful
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221397, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"spawn_entity","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","entityType":"pet","position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0},"name":"Test Pet 1","health":100,"maxHealth":100}},{"type":"spawn_entity","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","entityType":"pet","position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0},"name":"Test Pet 2","health":100,"maxHealth":100}}]},"timestamp":1745300221397,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221397, sender: 'server'}
useTournamentBattleConnection.ts:93 Tournament Battle: Received render instructions
pvp.tsx:61 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221397, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"spawn_entity","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","entityType":"pet","position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0},"name":"Test Pet 1","health":100,"maxHealth":100}},{"type":"spawn_entity","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","entityType":"pet","position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0},"name":"Test Pet 2","health":100,"maxHealth":100}}]},"timestamp":1745300221397,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221397, sender: 'server'}
useTournamentBattleConnection.ts:93 Tournament Battle: Received render instructions
pvp.tsx:61 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221398, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"spawn_entity","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","entityType":"pet","position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0},"name":"Test Pet 1","health":100,"maxHealth":100}},{"type":"spawn_entity","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","entityType":"pet","position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0},"name":"Test Pet 2","health":100,"maxHealth":100}}]},"timestamp":1745300221398,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300221398, sender: 'server'}
useTournamentBattleConnection.ts:93 Tournament Battle: Received render instructions
pvp.tsx:61 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
unifiedWebSocket.ts:80 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'pong', data: {…}, timestamp: 1745300221518, sender: 'server'}
useTournamentBattleConnection.ts:51 >>> Tournament Battle: Raw WebSocket message received: {"type":"pong","data":{"serverTime":1745300221518,"clientTime":1745300221514},"timestamp":1745300221518,"sender":"server"}
useTournamentBattleConnection.ts:63 Tournament Battle: Received message of type pong: {type: 'pong', data: {…}, timestamp: 1745300221518, sender: 'server'}
useTournamentBattleConnection.ts:101 Tournament Battle: Received pong response
useTournamentBattleConnection.ts:633 Tournament Battle: On PVP page, keeping WebSocket connection active
pvp.tsx:163 Tournament Battle: Still on PVP page, keeping WebSocket connections active
useTournamentBattleConnection.ts:619 Tournament Battle: Auto-connecting to WebSocket server...
useTournamentBattleConnection.ts:127 Tournament Battle: WebSocket connection already in progress...
pvp.tsx:101 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
GameEngine.ts:3199 Skipping pet specters loading - spectator mode or disabled
pvp.tsx:112 Creating PVP arena
GameEngine.ts:4485 ARENA: Creating dedicated PVP Arena environment
GameEngine.ts:4548 Disabling normal game systems for PVP Arena
GameEngine.ts:4556 Set generationStopped flag on level generator
GameEngine.ts:3189 Cleared all pet specters
DungeonManager.ts:909 Disabling dungeon manager
useTournamentBattleConnection.ts:247 Tournament Battle: Sending auto-join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300222343, sender: 'tournament_1745300221515_m52icauct'}
App.tsx:37 PVP page detected - allowing all WebSocket connections
GameEngine.ts:4516 ARENA: Creating Coliseum Arena instance
GameEngine.ts:4519 ARENA: Calling create() method on Coliseum Arena
ColiseumArena.ts:112 Creating arena floor with texture
ColiseumArena.ts:118 Loading floor texture from correct path
ColiseumArena.ts:153 Creating arena walls with texture
ColiseumArena.ts:158 Loading wall texture from correct path
ColiseumArena.ts:223 Creating arena ceiling with texture
ColiseumArena.ts:229 Loading ceiling texture from correct path
ColiseumArena.ts:62 Coliseum arena created
GameEngine.ts:4521 ARENA: Coliseum Arena created for PVP battles
pvp.tsx:120 PVP arena initialization completed successfully
pvp.tsx:134 Waiting for WebSocket connection before joining battle...
GameEngine.ts:4516 ARENA: Creating Coliseum Arena instance
GameEngine.ts:4519 ARENA: Calling create() method on Coliseum Arena
ColiseumArena.ts:112 Creating arena floor with texture
ColiseumArena.ts:118 Loading floor texture from correct path
ColiseumArena.ts:153 Creating arena walls with texture
ColiseumArena.ts:158 Loading wall texture from correct path
ColiseumArena.ts:223 Creating arena ceiling with texture
ColiseumArena.ts:229 Loading ceiling texture from correct path
ColiseumArena.ts:62 Coliseum arena created
GameEngine.ts:4521 ARENA: Coliseum Arena created for PVP battles
pvp.tsx:120 PVP arena initialization completed successfully
pvp.tsx:134 Waiting for WebSocket connection before joining battle...
GameEngine.ts:4516 ARENA: Creating Coliseum Arena instance
GameEngine.ts:4519 ARENA: Calling create() method on Coliseum Arena
ColiseumArena.ts:112 Creating arena floor with texture
ColiseumArena.ts:118 Loading floor texture from correct path
ColiseumArena.ts:153 Creating arena walls with texture
ColiseumArena.ts:158 Loading wall texture from correct path
ColiseumArena.ts:223 Creating arena ceiling with texture
ColiseumArena.ts:229 Loading ceiling texture from correct path
ColiseumArena.ts:62 Coliseum arena created
GameEngine.ts:4521 ARENA: Coliseum Arena created for PVP battles
GameEngine.ts:4516 ARENA: Creating Coliseum Arena instance
GameEngine.ts:4519 ARENA: Calling create() method on Coliseum Arena
ColiseumArena.ts:112 Creating arena floor with texture
ColiseumArena.ts:118 Loading floor texture from correct path
ColiseumArena.ts:153 Creating arena walls with texture
ColiseumArena.ts:158 Loading wall texture from correct path
ColiseumArena.ts:223 Creating arena ceiling with texture
ColiseumArena.ts:229 Loading ceiling texture from correct path
ColiseumArena.ts:62 Coliseum arena created
GameEngine.ts:4521 ARENA: Coliseum Arena created for PVP battles
pvp.tsx:120 PVP arena initialization completed successfully
pvp.tsx:128 Already connected, joining battle as spectator
GameEngine.ts:4603 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator with connection ID tournament_1745300221515_m52icauct
 Tournament Battle: Sending tournament battle join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300224171, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 1)
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222346, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300222346,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222346, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 PVP page detected - allowing all WebSocket connections
 Tournament Battle: Sending auto-join request (attempt 2): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300224956, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Connection established, now attempting to join tournament battle...
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Cannot join tournament battle: WebSocket not connected or no connection ID
(anonymous) @ useTournamentBattleConnection.ts:294
(anonymous) @ useTournamentBattleConnection.ts:301
setTimeout
(anonymous) @ useTournamentBattleConnection.ts:298
onConnect @ pvp.tsx:46
ws.onopen @ useTournamentBattleConnection.ts:189
 Tournament Battle: Attempting to connect before joining tournament battle...
 Tournament Battle: WebSocket connection already in progress...
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 THREE.WebGLProgram: Shader Error 0 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
await in initializePVPArena
(anonymous) @ pvp.tsx:132
commitHookEffectListMount @ chunk-RPCDYKBN.js:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js:19490
flushPassiveEffects @ chunk-RPCDYKBN.js:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js:9119
commitRootImpl @ chunk-RPCDYKBN.js:19432
commitRoot @ chunk-RPCDYKBN.js:19277
finishConcurrentRender @ chunk-RPCDYKBN.js:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js:18718
workLoop @ chunk-RPCDYKBN.js:197
flushWork @ chunk-RPCDYKBN.js:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js:384
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
WebGL: INVALID_OPERATION: useProgram: program not valid
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
await in initializePVPArena
(anonymous) @ pvp.tsx:132
commitHookEffectListMount @ chunk-RPCDYKBN.js:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js:19490
flushPassiveEffects @ chunk-RPCDYKBN.js:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js:9119
commitRootImpl @ chunk-RPCDYKBN.js:19432
commitRoot @ chunk-RPCDYKBN.js:19277
finishConcurrentRender @ chunk-RPCDYKBN.js:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js:18718
workLoop @ chunk-RPCDYKBN.js:197
flushWork @ chunk-RPCDYKBN.js:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js:384
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: Could not pack varying vViewPosition
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
await in initializePVPArena
(anonymous) @ pvp.tsx:132
commitHookEffectListMount @ chunk-RPCDYKBN.js:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js:19490
flushPassiveEffects @ chunk-RPCDYKBN.js:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js:9119
commitRootImpl @ chunk-RPCDYKBN.js:19432
commitRoot @ chunk-RPCDYKBN.js:19277
finishConcurrentRender @ chunk-RPCDYKBN.js:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js:18718
workLoop @ chunk-RPCDYKBN.js:197
flushWork @ chunk-RPCDYKBN.js:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js:384
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: Could not pack varying vViewPosition
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
await in initializePVPArena
(anonymous) @ pvp.tsx:132
commitHookEffectListMount @ chunk-RPCDYKBN.js:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js:19490
flushPassiveEffects @ chunk-RPCDYKBN.js:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js:9119
commitRootImpl @ chunk-RPCDYKBN.js:19432
commitRoot @ chunk-RPCDYKBN.js:19277
finishConcurrentRender @ chunk-RPCDYKBN.js:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js:18718
workLoop @ chunk-RPCDYKBN.js:197
flushWork @ chunk-RPCDYKBN.js:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js:384
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 PVP arena initialization completed successfully
 Already connected, joining battle as spectator
 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator with connection ID tournament_1745300221515_m52icauct
 Tournament Battle: Sending tournament battle join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300226222, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 1)
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Sending tournament battle join request (attempt 2): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300226991, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 2)
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222347, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300222347,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222347, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222399, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300222399,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300222399, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224173, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300224173,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224173, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224174, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300224174,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224174, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 PVP page detected - allowing all WebSocket connections
 Tournament Battle: Sending auto-join request (attempt 3): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300227025, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Sending tournament battle join request (attempt 2): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300227606, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 2)
 Tournament Battle: Connection established, now attempting to join tournament battle...
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Cannot join tournament battle: WebSocket not connected or no connection ID
(anonymous) @ useTournamentBattleConnection.ts:294
(anonymous) @ useTournamentBattleConnection.ts:301
setTimeout
(anonymous) @ useTournamentBattleConnection.ts:298
(anonymous) @ useTournamentBattleConnection.ts:301
setTimeout
(anonymous) @ useTournamentBattleConnection.ts:298
onConnect @ pvp.tsx:46
ws.onopen @ useTournamentBattleConnection.ts:189
 Tournament Battle: Attempting to connect before joining tournament battle...
 Tournament Battle: WebSocket connection already in progress...
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224957, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300224957,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224957, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224958, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"pending","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300224958,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300224958, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'pending', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226224, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300226224,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226224, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226225, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":100,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":100,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393}]},"timestamp":1745300226225,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226225, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226511, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"currentHealth":93,"maxHealth":100}}]},"timestamp":1745300226511,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226511, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"currentHealth":93,"maxHealth":100}}]},"timestamp":1745300226512,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":95,"maxHealth":100}}]},"timestamp":1745300226512,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":95,"maxHealth":100}}]},"timestamp":1745300226512,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300226512, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226513, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226513,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226513, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226514,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226514,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226514,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226514, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226993, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226993,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226993, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226994, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300226994,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300226994, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 PVP arena initialization completed successfully
 Already connected, joining battle as spectator
 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator with connection ID tournament_1745300221515_m52icauct
 Tournament Battle: Sending tournament battle join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300227959, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 1)
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Verifying tournament battle join request was processed...
 PVP page detected - allowing all WebSocket connections
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227027, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300227027,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227027, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227027, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":95,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":93,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300227027,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227027, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(5)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227513, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"currentHealth":83,"maxHealth":100}}]},"timestamp":1745300227513,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227513, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227514, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"currentHealth":83,"maxHealth":100}}]},"timestamp":1745300227514,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227514, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227514, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"currentHealth":92,"maxHealth":100}}]},"timestamp":1745300227514,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227514, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227515, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"currentHealth":92,"maxHealth":100}}]},"timestamp":1745300227515,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300227515, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227515, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227515,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227515, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227515, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227515,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227515, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227516, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227516,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227516, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227516, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227516,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227516, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227608, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227608,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227608, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227609, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227609,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227609, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 Tournament Battle: Sending tournament battle join request (attempt 3): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300229995, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 3)
 Tournament Battle: Sending tournament battle join request (attempt 2): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300229997, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 2)
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 PVP arena initialization completed successfully
 Already connected, joining battle as spectator
 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator with connection ID tournament_1745300221515_m52icauct
 Tournament Battle: Sending tournament battle join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300230472, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 1)
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Sending tournament battle join request (attempt 3): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300231400, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 3)
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227961, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227961,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227961, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227961, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":92,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":83,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"}]},"timestamp":1745300227961,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300227961, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(9)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228523, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"currentHealth":77,"maxHealth":100}}]},"timestamp":1745300228523,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228523, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228523, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"currentHealth":77,"maxHealth":100}}]},"timestamp":1745300228523,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228523, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228524, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":87,"maxHealth":100}}]},"timestamp":1745300228524,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228524, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228524, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":87,"maxHealth":100}}]},"timestamp":1745300228524,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300228524, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":87,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300228525,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(13)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":87,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300228525,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(13)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":87,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300228525,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228525, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(13)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228527, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":87,"maxHealth":100,"position":{"x":-15,"y":5,"z":0},"rotation":{"x":0,"y":0,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300228527,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300228527, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(13)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'pong', data: {…}, timestamp: 1745300229055, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"pong","data":{"serverTime":1745300229055,"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254"},"timestamp":1745300229055,"sender":"server"}
 Tournament Battle: Received message of type pong: {type: 'pong', data: {…}, timestamp: 1745300229055, sender: 'server'}
 Tournament Battle: Received pong response
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 WebGL: too many errors, no more errors will be reported to the console for this context.
useProgram @ chunk-QFYPTPS2.js:33432
setProgram @ chunk-QFYPTPS2.js:37167
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
 THREE.WebGLProgram: Shader Error 0 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: Could not pack varying vViewPosition
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: Could not pack varying vViewPosition
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 Tournament Battle: Verifying tournament battle join request was processed...
 PVP page detected - allowing all WebSocket connections
 Tournament Battle: Connection established, now attempting to join tournament battle...
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Cannot join tournament battle: WebSocket not connected or no connection ID
(anonymous) @ useTournamentBattleConnection.ts:294
(anonymous) @ useTournamentBattleConnection.ts:301
 Tournament Battle: Attempting to connect before joining tournament battle...
 Tournament Battle: WebSocket connection already in progress...
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 PVP arena initialization completed successfully
 Already connected, joining battle as spectator
 Connecting to battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Joining test battle 2c60ec8c-0607-4fc4-b021-cefa09818254
 Tournament Battle: Joining battle 2c60ec8c-0607-4fc4-b021-cefa09818254 as spectator with connection ID tournament_1745300221515_m52icauct
 Tournament Battle: Sending tournament battle join request (attempt 1): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300234839, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 1)
 Tournament Battle: On PVP page, keeping WebSocket connection active
 Tournament Battle: Still on PVP page, keeping WebSocket connections active
 Tournament Battle: Auto-connecting to WebSocket server...
 Tournament Battle: WebSocket connection already in progress...
 Initializing PVP Arena with test battle ID: 2c60ec8c-0607-4fc4-b021-cefa09818254
 Skipping pet specters loading - spectator mode or disabled
 Creating PVP arena
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 Tournament Battle: Verifying tournament battle join request was processed...
 Tournament Battle: Sending tournament battle join request (attempt 2): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300235943, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 2)
 Tournament Battle: Sending tournament battle join request (attempt 3): {type: 'tournament_battle_join', data: {…}, timestamp: 1745300235945, sender: 'tournament_1745300221515_m52icauct'}
 Tournament Battle: Sent tournament battle join request (attempt 3)
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229528, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"move_entity","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}}}]},"timestamp":1745300229528,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229528, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229528, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"move_entity","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}}}]},"timestamp":1745300229528,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229528, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(1)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229529, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"currentHealth":81,"maxHealth":100}}]},"timestamp":1745300229529,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229529, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229529, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"currentHealth":81,"maxHealth":100}}]},"timestamp":1745300229529,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300229529, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229529, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229529,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229529, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229530,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229530,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229530,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229530, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229997, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229997,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229997, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229998, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229998,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229998, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229999, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300229999,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300229999, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230000, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300230000,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230000, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230474, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300230474,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230474, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230474, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":81,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":77,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"}]},"timestamp":1745300230474,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230474, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(16)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230533, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"currentHealth":70,"maxHealth":100}}]},"timestamp":1745300230533,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230533, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230533, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","attackType":"fire"}},{"type":"damage","data":{"entityId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"currentHealth":70,"maxHealth":100}}]},"timestamp":1745300230533,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230533, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230534, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":76,"maxHealth":100}}]},"timestamp":1745300230534,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230534, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230534, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_render_instructions","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","instructions":[{"type":"attack","data":{"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","attackType":"ice"}},{"type":"damage","data":{"entityId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"currentHealth":76,"maxHealth":100}}]},"timestamp":1745300230534,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_render_instructions: {type: 'tournament_battle_render_instructions', data: {…}, timestamp: 1745300230534, sender: 'server'}
 Tournament Battle: Received render instructions
 Tournament Battle: Received render instructions: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', instructions: Array(2)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300230535,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300230535,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300230535,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230535, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230536, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300230536,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300230536, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300231402, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300231402,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300231402, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 WebSocket tournament_1745300220721_xkcdvsz6n received message: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300231403, sender: 'server'}
 >>> Tournament Battle: Raw WebSocket message received: {"type":"tournament_battle_update","data":{"battleId":"2c60ec8c-0607-4fc4-b021-cefa09818254","status":"in_progress","participants":[{"id":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","petName":"Test Pet 1","petLevel":5,"health":76,"maxHealth":100,"position":{"x":-14.987702352693843,"y":5,"z":-0.6072710984851837},"rotation":{"x":0,"y":-2.7925908053387163,"z":0}},{"id":"980544f3-ca8b-45f2-a270-08e342925e44","petName":"Test Pet 2","petLevel":5,"health":70,"maxHealth":100,"position":{"x":15,"y":5,"z":0},"rotation":{"x":0,"y":3.141592653589793,"z":0}}],"spectators":1,"battleLog":[{"type":"system","message":"Battle created. Pets are taking their positions...","timestamp":1745300220393},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300226511,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300226511,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300226512,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300226512,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300227512,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 10 damage with fire attack","timestamp":1745300227512,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":10,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300227514,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 3 damage with ice attack","timestamp":1745300227514,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":3,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300228522,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 6 damage with fire attack","timestamp":1745300228522,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":6,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300228524,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300228524,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"},{"type":"move","message":"Test Pet 1 moves to a new position","timestamp":1745300229528,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300229529,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 6 damage with ice attack","timestamp":1745300229529,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":6,"attackType":"ice"},{"type":"shield","message":"Test Pet 2's shield absorbs some of the damage","timestamp":1745300230533,"petId":"980544f3-ca8b-45f2-a270-08e342925e44"},{"type":"attack","message":"Test Pet 1 attacks Test Pet 2 for 7 damage with fire attack","timestamp":1745300230533,"attackerId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","targetId":"980544f3-ca8b-45f2-a270-08e342925e44","damage":7,"attackType":"fire"},{"type":"shield","message":"Test Pet 1's shield absorbs some of the damage","timestamp":1745300230534,"petId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3"},{"type":"attack","message":"Test Pet 2 attacks Test Pet 1 for 5 damage with ice attack","timestamp":1745300230534,"attackerId":"980544f3-ca8b-45f2-a270-08e342925e44","targetId":"d351ac21-1b05-4c17-b032-5c4deb4c2bd3","damage":5,"attackType":"ice"}]},"timestamp":1745300231403,"sender":"server"}
 Tournament Battle: Received message of type tournament_battle_update: {type: 'tournament_battle_update', data: {…}, timestamp: 1745300231403, sender: 'server'}
 Tournament Battle: Received battle update
 Tournament Battle: Received battle update: {battleId: '2c60ec8c-0607-4fc4-b021-cefa09818254', status: 'in_progress', participants: Array(2), spectators: 1, battleLog: Array(20)}
 ARENA: Creating dedicated PVP Arena environment
 Disabling normal game systems for PVP Arena
 Set generationStopped flag on level generator
 Cleared all pet specters
 Disabling dungeon manager
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 THREE.WebGLProgram: Shader Error 0 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 THREE.WebGLProgram: Shader Error 1282 - VALIDATE_STATUS false

Material Name: 
Material Type: MeshStandardMaterial

Program Info Log: FRAGMENT shader texture image units count exceeds MAX_TEXTURE_IMAGE_UNITS(32)
 


onFirstUse @ chunk-QFYPTPS2.js:31693
WebGLProgram.getUniforms @ chunk-QFYPTPS2.js:31725
setProgram @ chunk-QFYPTPS2.js:37166
WebGLRenderer.renderBufferDirect @ chunk-QFYPTPS2.js:36543
renderObject @ chunk-QFYPTPS2.js:36983
renderObjects @ chunk-QFYPTPS2.js:36965
renderScene @ chunk-QFYPTPS2.js:36884
WebGLRenderer.render @ chunk-QFYPTPS2.js:36795
animate @ GameEngine.ts:408
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
requestAnimationFrame
animate @ GameEngine.ts:353
start @ GameEngine.ts:292
initializePVPArena @ pvp.tsx:108
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
 ARENA: Creating Coliseum Arena instance
 ARENA: Calling create() method on Coliseum Arena
 Creating arena floor with texture
 Loading floor texture from correct path
 Creating arena walls with texture
 Loading wall texture from correct path
 Creating arena ceiling with texture
 Loading ceiling texture from correct path
 Coliseum arena created
 ARENA: Coliseum Arena created for PVP battles
