import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';

class TimeBubbleEffect {
  private scene: THREE.Scene;
  private effectMesh: THREE.Mesh;
  private duration: number;
  private timeLeft: number;
  private position: THREE.Vector3;
  private radius: number;

  constructor(scene: THREE.Scene, position: THREE.Vector3, radius: number, duration: number) {
    this.scene = scene;
    this.position = position.clone();
    this.radius = radius;
    this.duration = duration;
    this.timeLeft = duration;

    // Create visual effect with texture instead of wireframe
    const geometry = new THREE.SphereGeometry(radius, 32, 32);

    // Load texture from SVG
    const textureLoader = new THREE.TextureLoader();
    const texture = textureLoader.load('/assets/textures/level/wall4.svg', undefined, undefined, (error) => {
      console.warn('Error loading time bubble effect texture:', error);
    });

    // Apply texture to material
    const material = new THREE.MeshBasicMaterial({
      color: 0xff00ff,
      transparent: true,
      opacity: 0.3,
      map: texture,
      side: THREE.DoubleSide
    });

    this.effectMesh = new THREE.Mesh(geometry, material);
    this.effectMesh.position.copy(position);

    // Add distortion effect
    const distortionEffect = new THREE.Group();

    // Add inner ripple effect with textures
    for (let i = 0; i < 3; i++) {
      const rippleGeometry = new THREE.SphereGeometry(radius * (0.6 + i * 0.1), 32, 32);

      // Use a different texture for inner ripples
      const rippleTexture = textureLoader.load('/assets/textures/level/ceiling5.svg', undefined, undefined, (error) => {
        console.warn('Error loading ripple texture:', error);
      });

      const rippleMaterial = new THREE.MeshBasicMaterial({
        color: 0xff00ff,
        transparent: true,
        opacity: 0.1,
        map: rippleTexture,
        side: THREE.DoubleSide
      });

      const ripple = new THREE.Mesh(rippleGeometry, rippleMaterial);
      ripple.userData.offset = i * Math.PI * 0.5; // Phase offset
      distortionEffect.add(ripple);
    }

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.05, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xff00ff,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    this.effectMesh.add(glowMesh);

    this.effectMesh.add(distortionEffect);

    // Add to scene
    this.scene.add(this.effectMesh);
  }

  update(delta: number): boolean {
    // Update time left
    this.timeLeft -= delta;

    // Update visual effect

    // Ripple effect
    this.effectMesh.children.forEach((rippleGroup) => {
      rippleGroup.children.forEach((ripple) => {
        if (ripple instanceof THREE.Mesh) {
          const pulseScale = 0.8 + Math.sin(this.timeLeft * 3 + ripple.userData.offset) * 0.2;
          ripple.scale.set(pulseScale, pulseScale, pulseScale);
        }
      });
    });

    // Rotate the effect
    this.effectMesh.rotation.y += delta * 0.2;

    // Fade out as effect expires
    const opacity = Math.min(0.3, (this.timeLeft / this.duration) * 0.3);
    (this.effectMesh.material as THREE.MeshBasicMaterial).opacity = opacity;

    // Update ripple opacity
    this.effectMesh.children.forEach((rippleGroup) => {
      rippleGroup.children.forEach((ripple) => {
        if (ripple instanceof THREE.Mesh) {
          const rippleMaterial = ripple.material as THREE.MeshBasicMaterial;
          rippleMaterial.opacity = opacity * 0.3;
        }
      });
    });

    // Return true if effect is still active
    return this.timeLeft > 0;
  }

  isObjectAffected(position: THREE.Vector3): boolean {
    return position.distanceTo(this.position) < this.radius;
  }

  getTimeScale(): number {
    // Return time scale factor (0.2 = 5x slower)
    return 0.2;
  }

  dispose(): void {
    // Remove from scene
    this.scene.remove(this.effectMesh);

    // Dispose geometry and material
    this.effectMesh.geometry.dispose();
    (this.effectMesh.material as THREE.MeshBasicMaterial).dispose();

    // Dispose all child meshes (ripples and glow)
    this.effectMesh.children.forEach((child) => {
      if (child instanceof THREE.Mesh) {
        // Dispose direct child meshes (like glow)
        child.geometry.dispose();
        (child.material as THREE.MeshBasicMaterial).dispose();
      } else if (child instanceof THREE.Group) {
        // Dispose meshes in groups (like ripples)
        child.children.forEach((groupChild) => {
          if (groupChild instanceof THREE.Mesh) {
            groupChild.geometry.dispose();
            (groupChild.material as THREE.MeshBasicMaterial).dispose();
          }
        });
      }
    });
  }

  getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  getRadius(): number {
    return this.radius;
  }
}

export default TimeBubbleEffect;