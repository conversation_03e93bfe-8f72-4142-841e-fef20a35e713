import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';

class GravityEffect {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private effectMesh: THREE.Mesh;
  private duration: number;
  private timeLeft: number;
  private position: THREE.Vector3;
  private radius: number;

  constructor(scene: THREE.Scene, world: CANNON.World, position: THREE.Vector3, radius: number, duration: number) {
    this.scene = scene;
    this.world = world;
    this.position = position.clone();
    this.radius = radius;
    this.duration = duration;
    this.timeLeft = duration;

    // Create visual effect with texture instead of wireframe
    const geometry = new THREE.SphereGeometry(radius, 32, 32);

    // Load texture from SVG
    const textureLoader = new THREE.TextureLoader();
    const texture = textureLoader.load('/assets/textures/level/floor3.svg', undefined, undefined, (error) => {
      console.warn('Error loading gravity effect texture:', error);
    });

    // Apply texture to material
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.3,
      map: texture,
      side: THREE.DoubleSide
    });

    this.effectMesh = new THREE.Mesh(geometry, material);
    this.effectMesh.position.copy(position);

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.05, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    this.effectMesh.add(glowMesh);

    // Add to scene
    this.scene.add(this.effectMesh);
  }

  update(delta: number): boolean {
    // Update time left
    this.timeLeft -= delta;

    // Update visual effect
    const pulseScale = 1 + Math.sin(this.timeLeft * 5) * 0.1;
    this.effectMesh.scale.set(pulseScale, pulseScale, pulseScale);

    // Fade out as effect expires
    const opacity = Math.min(0.3, (this.timeLeft / this.duration) * 0.3);
    (this.effectMesh.material as THREE.MeshBasicMaterial).opacity = opacity;

    // Return true if effect is still active
    return this.timeLeft > 0;
  }

  applyToBody(body: CANNON.Body): void {
    // Calculate direction to effect center
    const bodyPos = new THREE.Vector3(
      body.position.x,
      body.position.y,
      body.position.z
    );

    const direction = new THREE.Vector3()
      .copy(this.position)
      .sub(bodyPos);

    const distance = direction.length();

    // console.log(`[GravityEffect] Checking body at distance ${distance.toFixed(2)} from effect center (radius: ${this.radius.toFixed(2)})`);

    // Only apply if within radius
    if (distance < this.radius) {
      direction.normalize();

      // Force is stronger when closer to center with a more dramatic falloff
      // Match the formula used in Specter.applyGravityEffect for consistency
      const forceMagnitude = 500 * (1 / Math.pow(distance + 0.1, 1.2));

      // console.log(`[GravityEffect] Applying force magnitude ${forceMagnitude.toFixed(2)} to body`);

      // Apply force toward gravity well
      body.applyForce(
        new CANNON.Vec3(
          direction.x * forceMagnitude,
          direction.y * forceMagnitude,
          direction.z * forceMagnitude
        ),
        new CANNON.Vec3(0, 0, 0)
      );

      // Prevent teleporting by reducing velocity slightly
      // This helps enemies stay in the gravity field
      const dampingFactor = 0.9; // Increased damping for stronger effect
      body.velocity.scale(dampingFactor, body.velocity);

      // Additionally, directly modify velocity for more immediate effect
      const pullFactor = 0.1; // How much to directly affect velocity
      body.velocity.x += direction.x * forceMagnitude * pullFactor;
      body.velocity.y += direction.y * forceMagnitude * pullFactor;
      body.velocity.z += direction.z * forceMagnitude * pullFactor;

      return true; // Indicate that the effect was applied
    }

    return false; // Indicate that the effect was not applied
  }

  dispose(): void {
    // Remove from scene
    this.scene.remove(this.effectMesh);

    // Dispose geometry and material
    this.effectMesh.geometry.dispose();
    (this.effectMesh.material as THREE.MeshBasicMaterial).dispose();

    // Dispose all child meshes (glow)
    this.effectMesh.children.forEach((child) => {
      if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        (child.material as THREE.MeshBasicMaterial).dispose();
      }
    });
  }

  getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  getRadius(): number {
    return this.radius;
  }
}

export default GravityEffect;
