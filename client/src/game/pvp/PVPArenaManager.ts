import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';
import GameEngine from '../engine/GameEngine';
import { ColiseumArena } from '../world/ColiseumArena';
import { MessageType, NetworkMessage } from '@shared/schema';

/**
 * PVP Arena Manager
 * Handles setting up and managing the PVP arena environment
 */
export class PVPArenaManager {
  private gameEngine: GameEngine;
  private coliseumArena: ColiseumArena | null = null;
  private battleId: string | null = null;
  private isSpectator: boolean = true;
  private connectionStatus: 'connecting' | 'connected' | 'disconnected' = 'disconnected';
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private lastConnectionAttempt: number = 0;
  private spectatorPositionUpdateInterval: number | null = null;

  /**
   * Constructor
   * @param gameEngine Reference to the game engine
   */
  constructor(gameEngine: GameEngine) {
    this.gameEngine = gameEngine;
  }

  /**
   * Set up the PVP arena environment
   * @param battleId The battle ID
   * @param isSpectator Whether the user is a spectator
   */
  public setupArena(battleId: string, isSpectator: boolean = true): void {
    console.log(`Setting up PVP arena for battle ${battleId} as ${isSpectator ? 'spectator' : 'participant'}`);

    this.battleId = battleId;
    this.isSpectator = isSpectator;
    this.connectionStatus = 'connecting';
    this.reconnectAttempts = 0;
    this.lastConnectionAttempt = Date.now();

    // Set spectator mode in game engine, GameEngine handles player state changes
    this.gameEngine.setSpectatorMode(true);

    // Position player at a good viewing spot (Handled by GameEngine)
    console.log('PVPArenaManager: Skipping initial spectator positioning (handled by GameEngine)');

    // Join the battle via WebSocket
    this.joinBattleAsSpectator(battleId);

    // Set up periodic position updates for spectator
    this.setupSpectatorPositionUpdates();
  }

  /**
   * Position the spectator at a good viewing spot (Handled by GameEngine)
   */
  private positionSpectator(): void {
    // This is now handled within GameEngine's setSpectatorMode or related logic
    console.log('PVPArenaManager: Skipping initial spectator positioning (handled by GameEngine)');
  }

  /**
   * Clean up the PVP arena
   */
  public dispose(): void {
    // Clear any update intervals
    if (this.spectatorPositionUpdateInterval !== null) {
      window.clearInterval(this.spectatorPositionUpdateInterval);
      this.spectatorPositionUpdateInterval = null;
    }

    // Leave the battle if we're connected
    if (this.battleId && this.connectionStatus === 'connected') {
      this.leaveBattle();
    }

    // Disable spectator mode in game engine (This means re-enable normal controls)
    this.gameEngine.setSpectatorMode(false);
  }

  /**
   * Get the battle ID
   */
  public getBattleId(): string | null {
    return this.battleId;
  }

  /**
   * Check if in spectator mode
   */
  public isInSpectatorMode(): boolean {
    return this.isSpectator;
  }

  /**
   * Join a battle as a spectator
   * @param battleId The battle ID to join
   */
  private joinBattleAsSpectator(battleId: string): void {
    console.log(`Joining battle ${battleId} as spectator`);

    // Get the user ID from localStorage or use a default
    const userId = localStorage.getItem('walletAddress') || 'anonymous_spectator';

    // Send join message to server via custom event
    this.sendWebSocketMessage({
      type: MessageType.TournamentBattleJoin,
      data: {
        battleId,
        isSpectator: true,
        userId
      },
      timestamp: Date.now(),
      sender: 'client'
    });

    // Update connection status
    this.connectionStatus = 'connecting';
    this.lastConnectionAttempt = Date.now();

    // Set up a timeout to check if we've connected successfully
    setTimeout(() => {
      if (this.connectionStatus === 'connecting') {
        console.warn(`No connection confirmation received for battle ${battleId} after 5 seconds`);

        // Try to reconnect if we haven't exceeded max attempts
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          console.log(`Attempting to reconnect (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
          this.joinBattleAsSpectator(battleId);
        } else {
          console.error(`Failed to connect to battle ${battleId} after ${this.maxReconnectAttempts} attempts`);
          this.connectionStatus = 'disconnected';
        }
      }
    }, 5000);
  }

  /**
   * Leave the current battle
   */
  private leaveBattle(): void {
    if (!this.battleId) return;

    console.log(`Leaving battle ${this.battleId}`);

    // Send leave message to server
    this.sendWebSocketMessage({
      type: MessageType.TournamentBattleLeave,
      data: {
        battleId: this.battleId
      },
      timestamp: Date.now(),
      sender: 'client'
    });

    // Update connection status
    this.connectionStatus = 'disconnected';
  }

  /**
   * Send a WebSocket message via custom event
   * @param message The message to send
   */
  private sendWebSocketMessage(message: NetworkMessage): void {
    // Dispatch a custom event to send the message via WebSocket
    const event = new CustomEvent('tournament-ws-message', {
      detail: {
        message
      }
    });
    
    // Dispatch the event on the document
    document.dispatchEvent(event);
  }

  /**
   * Set up periodic position updates for the spectator
   */
  private setupSpectatorPositionUpdates(): void {
    // Clear any existing interval
    if (this.spectatorPositionUpdateInterval !== null) {
      window.clearInterval(this.spectatorPositionUpdateInterval);
    }

    // Set up a new interval to update spectator position
    this.spectatorPositionUpdateInterval = window.setInterval(() => {
      // Only send updates if we're connected
      if (this.connectionStatus === 'connected' && this.battleId) {
        const player = this.gameEngine.getPlayer();
        if (player) {
          const position = player.getPosition();
          if (position) {
            // Send spectator position update
            this.sendWebSocketMessage({
              type: 'spectator_position_update' as any, // NOTE: Check if a proper MessageType exists for this
              data: {
                battleId: this.battleId,
                position: {
                  x: position.x,
                  y: position.y,
                  z: position.z
                }
              },
              timestamp: Date.now(),
              sender: 'client'
            });
          }
        }
      }
    }, 5000); // Update every 5 seconds
  }

  /**
   * Handle WebSocket connection status change
   * @param status The new connection status
   */
  public setConnectionStatus(status: 'connecting' | 'connected' | 'disconnected'): void {
    this.connectionStatus = status;

    if (status === 'connected') {
      // Reset reconnect attempts on successful connection
      this.reconnectAttempts = 0;
      console.log(`Successfully connected to battle ${this.battleId}`);
    } else if (status === 'disconnected') {
      console.log(`Disconnected from battle ${this.battleId}`);

      // Try to reconnect if we were previously connected
      if (this.battleId && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        // Wait a bit before reconnecting
        setTimeout(() => {
          if (this.battleId) {
            this.joinBattleAsSpectator(this.battleId);
          }
        }, 2000);
      }
    }
  }
}
