import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import {
  SPECTER_COLORS,
  SPECTER_MOVE_SPEED,
  SPECTER_MASS,
  SPECTER_ATTACK_DAMAGE,
  SPECTER_ATTACK_RANGE,
  SPECTER_DETECTION_RANGE,
  SPECTER_TYPES
} from '../constants';
import { SpecterType } from '../types';
import Player from './Player';

class Specter {
  // Add methods needed for dungeon compatibility
  private mesh: THREE.Group;
  private body: CANNON.Body;
  private scene: THREE.Scene;
  private world: CANNON.World;
  private speed: number;
  private timeEffect: boolean = false;
  private phased: boolean = false;
  private phasedTimer: number = 0;
  private type: SpecterType;
  private isCamouflaged: boolean = false;
  private lastTeleportTime: number = 0;
  private readonly TELEPORT_COOLDOWN: number = 10000; // 10 seconds
  private readonly CAMO_COOLDOWN: number = 10000; // 10 seconds
  private lastCamoTime: number = 0;
  private readonly TELEPORT_RANGE: number = 15;
  private readonly MIN_TELEPORT_DISTANCE: number = 5; // Minimum distance from player

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    material: CANNON.Material,
    providedType?: SpecterType
  ) {
    this.scene = scene;
    this.world = world;

    // If a type is provided, use it; otherwise randomly select one
    if (providedType) {
      this.type = { ...providedType };
    } else {
      // Randomly select a specter type from defined types
      this.type = { ...SPECTER_TYPES[Math.floor(Math.random() * SPECTER_TYPES.length)] };
      // Assign unique ID
      this.type.id = Math.floor(Math.random() * 1000);
    }

    // Create mesh group
    this.mesh = new THREE.Group();
    this.scene.add(this.mesh);

    // Load specter texture
    const textureLoader = new THREE.TextureLoader();
    const specterTexture = textureLoader.load(this.type.texture);
    specterTexture.minFilter = THREE.NearestFilter;
    specterTexture.magFilter = THREE.NearestFilter;

    // Create sprite-based enemy that always faces camera
    const size = 2;
    const spriteMaterial = new THREE.SpriteMaterial({
      map: specterTexture,
      color: new THREE.Color(this.type.color),
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending
    });

    // Create main sprite
    const specterSprite = new THREE.Sprite(spriteMaterial);
    specterSprite.scale.set(size, size, 1);
    this.mesh.add(specterSprite);

    // Add particles around the core (for ethereal effect)
    this.addParticles();

    // Create physics body
    const shape = new CANNON.Sphere(1.2);
    this.body = new CANNON.Body({
      mass: SPECTER_MASS,
      material: material,
      shape: shape,
      position: new CANNON.Vec3(position.x, position.y, position.z),
      linearDamping: 0.1,
      fixedRotation: false
    });

    // Add body to world
    this.world.addBody(this.body);

    // Set random movement speed
    this.speed = SPECTER_MOVE_SPEED * (Math.random() * 0.5 + 0.75);

    // Position mesh
    this.mesh.position.copy(position);
  }

  // Add a public method to set the speed
  public setSpeed(newSpeed: number): void {
    this.speed = newSpeed;
  }

  update(delta: number, playerPosition: THREE.Vector3, player?: Player): void {
    // Random chance to teleport if cooldown elapsed
    if (!this.phased && !this.timeEffect && Math.random() < 0.02) {
      this.tryTeleport(playerPosition);
    }

    // Random chance to toggle camouflage
    if (!this.phased && !this.timeEffect && Math.random() < 0.01) {
      this.toggleCamouflage();
    }

    // Update phased timer if phased
    if (this.phased) {
      this.phasedTimer -= delta;

      // Visual effect for phased state - make it pulse
      const pulseFreq = 2; // Hz
      const pulseScale = 0.2; // Amount of pulsation
      const pulse = 1 + Math.sin(this.phasedTimer * pulseFreq * Math.PI) * pulseScale;
      this.mesh.scale.set(pulse, pulse, pulse);

      // Check if phase effect is over
      if (this.phasedTimer <= 0) {
        this.phased = false;

        // Restore normal appearance
        this.mesh.scale.set(1.0, 1.0, 1.0);
        this.mesh.traverse((child) => {
          if (child instanceof THREE.Sprite) {
            const material = child.material as THREE.SpriteMaterial;
            material.opacity = 0.9;
            material.color.set(this.type.color);
          }
        });
      }

      // Do minimal movement when phased, just drift around a bit
      if (Math.random() < 0.05) {
        const randomDir = new THREE.Vector3(
          Math.random() * 2 - 1,
          Math.random() * 2 - 1,
          Math.random() * 2 - 1
        ).normalize();

        this.body.applyForce(
          new CANNON.Vec3(
            randomDir.x * 5,
            randomDir.y * 5,
            randomDir.z * 5
          ),
          new CANNON.Vec3(0, 0, 0)
        );
      }

      // Update position and don't do regular movement when phased
      this.mesh.position.set(
        this.body.position.x,
        this.body.position.y,
        this.body.position.z
      );

      return;
    }

    // If under time effect, move more slowly
    const speedFactor = this.timeEffect ? 0.2 : 1.0;

    // Visual effect for time slowdown - rotate more slowly and add blue tint
    if (this.timeEffect) {
      this.mesh.traverse((child) => {
        if (child instanceof THREE.Sprite) {
          const material = child.material as THREE.SpriteMaterial;
          // Add blue-purple slowdown tint - blend the color
          material.color.lerp(new THREE.Color(0x3300ff), 0.2);
        }
      });
    }

    // Calculate direction and distance to player
    const toPlayer = new THREE.Vector3()
      .copy(playerPosition)
      .sub(this.getPosition());

    const distanceToPlayer = toPlayer.length();
    const direction = toPlayer.clone().normalize();

    // Attack player if we're close enough and not phased
    if (player && distanceToPlayer < SPECTER_ATTACK_RANGE && !this.phased) {
      this.attackPlayer(player, delta);

      // Visual pulse effect for attack
      this.mesh.scale.set(1.2, 1.2, 1.2);
      setTimeout(() => {
        if (this.mesh) { // Check if still exists
          this.mesh.scale.set(1.0, 1.0, 1.0);
        }
      }, 100);

      // Add attack particle effect
      const attackParticleCount = 8;
      const attackParticleGeometry = new THREE.ConeGeometry(0.2, 0.8, 4);
      const attackParticleMaterial = new THREE.MeshBasicMaterial({
        color: 0xff0000,
        transparent: true,
        opacity: 0.7
      });

      // Create particles shooting from specter to player
      for (let i = 0; i < attackParticleCount; i++) {
        const particle = new THREE.Mesh(attackParticleGeometry, attackParticleMaterial);

        // Position particle at specter
        particle.position.copy(this.getPosition());

        // Orient toward player
        particle.lookAt(playerPosition);

        // Add small random offset
        particle.rotation.x += (Math.random() - 0.5) * 0.5;
        particle.rotation.y += (Math.random() - 0.5) * 0.5;
        particle.rotation.z += (Math.random() - 0.5) * 0.5;

        this.scene.add(particle);

        // Animate particle toward player and remove
        let progress = 0;
        const startPos = this.getPosition().clone();

        const animateAttackParticle = () => {
          progress += 0.05;

          if (progress >= 1) {
            this.scene.remove(particle);
            particle.geometry.dispose();
            (particle.material as THREE.Material).dispose();
          } else {
            particle.position.lerpVectors(startPos, playerPosition, progress);
            particle.scale.multiplyScalar(0.95);
            requestAnimationFrame(animateAttackParticle);
          }
        };

        animateAttackParticle();
      }
    }

    // Only chase player if within detection range
    if (distanceToPlayer < SPECTER_DETECTION_RANGE) {
      // Add small random movement
      direction.x += (Math.random() - 0.5) * 0.2;
      direction.z += (Math.random() - 0.5) * 0.2;
      direction.normalize();

      // Move toward player but maintain height
      const moveSpeed = this.speed * speedFactor;

      // Apply force to body
      this.body.applyForce(
        new CANNON.Vec3(
          direction.x * moveSpeed,
          0, // Don't apply vertical force for movement
          direction.z * moveSpeed
        ),
        new CANNON.Vec3(0, 0, 0)
      );
    }

    // Update mesh position with body position
    this.mesh.position.set(
      this.body.position.x,
      this.body.position.y,
      this.body.position.z
    );

    // Rotate mesh for visual effect
    this.mesh.rotation.y += delta * 0.5 * speedFactor;
    this.mesh.rotation.x += delta * 0.3 * speedFactor;

    // Reset time effect for next frame
    this.timeEffect = false;
  }

  attackPlayer(player: Player, delta: number): void {
    // Only attack once every second (approximately)
    if (Math.random() > delta) {
      return;
    }

    // Apply damage to player
    player.takeDamage(SPECTER_ATTACK_DAMAGE);

    // Flash specter color to indicate attack
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        // Save original color
        const originalColor = material.color.clone();

        // Flash bright
        material.color.set(0xffffff);

        // Restore original color after flash
        setTimeout(() => {
          if (material) { // Check if material still exists
            material.color.copy(originalColor);
          }
        }, 100);
      }
    });
  }

  // Get position from physics body
  getPosition(): THREE.Vector3 {
    return new THREE.Vector3(
      this.body.position.x,
      this.body.position.y,
      this.body.position.z
    );
  }

  applyGravityEffect(effectPosition: THREE.Vector3, delta: number): void {
    // console.log(`[Specter] Gravity effect applied at position ${effectPosition.x.toFixed(2)}, ${effectPosition.y.toFixed(2)}, ${effectPosition.z.toFixed(2)}`);

    // Calculate direction to effect center
    const direction = new THREE.Vector3()
      .copy(effectPosition)
      .sub(this.getPosition());

    const distance = direction.length();
    direction.normalize();

    // console.log(`[Specter] Distance to gravity center: ${distance.toFixed(2)}`);

    // Significantly increase force strength for better gameplay effect
    // Force is stronger when closer to center with a more dramatic falloff
    const forceMagnitude = 500 * (1 / Math.pow(distance + 0.1, 1.2)); // Increased force and reduced falloff
    // console.log(`[Specter] Applied force magnitude: ${forceMagnitude.toFixed(2)}`);

    // Apply force toward gravity well
    this.body.applyForce(
      new CANNON.Vec3(
        direction.x * forceMagnitude,
        direction.y * forceMagnitude,
        direction.z * forceMagnitude
      ),
      new CANNON.Vec3(0, 0, 0)
    );

    // Reset teleport cooldown to prevent teleporting away while under gravity effect
    this.lastTeleportTime = Date.now();

    // Additionally, directly modify velocity for more immediate effect
    const pullFactor = 0.1; // How much to directly affect velocity
    this.body.velocity.x += direction.x * forceMagnitude * pullFactor * delta;
    this.body.velocity.y += direction.y * forceMagnitude * pullFactor * delta;
    this.body.velocity.z += direction.z * forceMagnitude * pullFactor * delta;

    // Add more visual effects to show the gravity pull
    if (Math.random() < 0.5) { // Increased particle rate
      // Create more sophisticated particle trail
      const particleTypes = [
        { geometry: new THREE.SphereGeometry(0.1, 4, 4), scale: 1.0 },
        { geometry: new THREE.TetrahedronGeometry(0.15, 0), scale: 0.8 },
        { geometry: new THREE.OctahedronGeometry(0.12, 0), scale: 0.9 }
      ];

      // Randomly select a particle type
      const particleType = particleTypes[Math.floor(Math.random() * particleTypes.length)];

      const particleMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8
      });

      const particle = new THREE.Mesh(particleType.geometry, particleMaterial);
      particle.scale.multiplyScalar(particleType.scale);

      // Position particle between specter and gravity center
      const lerpFactor = Math.random() * 0.8;
      const particlePos = this.getPosition().clone().lerp(effectPosition, lerpFactor);
      particle.position.copy(particlePos);

      this.scene.add(particle);

      // More dynamic animation that moves toward the gravity center
      let age = 0;
      const lifespan = 0.5 + Math.random() * 0.5; // Between 0.5 and 1 second

      const animateParticle = () => {
        age += 0.016; // Approx. 60fps

        // Move particle toward gravity center
        particle.position.lerp(effectPosition, 0.05);

        // Pulsate slightly
        const scale = particleType.scale * (1 + Math.sin(age * 10) * 0.1);
        particle.scale.set(scale, scale, scale);

        // Fade out as it approaches the center
        if (particle.material instanceof THREE.MeshBasicMaterial) {
          particle.material.opacity = 0.8 * (1 - age / lifespan);
        }

        if (age >= lifespan) {
          this.scene.remove(particle);
          particle.geometry.dispose();
          (particle.material as THREE.Material).dispose();
        } else {
          requestAnimationFrame(animateParticle);
        }
      };

      animateParticle();
    }

    // Visual feedback on the specter itself
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        // Add cyan tint to show gravity effect
        material.color.lerp(new THREE.Color(0x00ffff), 0.2);
      }
    });
  }

  applyTimeEffect(delta: number): void {
    // Mark specter as affected by time effect (will slow movement in update method)
    this.timeEffect = true;

    // Visual indicator of time effect
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        // Add a stronger purple-blue tint to the specter when slowed
        material.color.setRGB(0.4, 0, 0.8);
      }
    });

    // Apply physics anomaly effects
    if (this.body.velocity.length() > 0.5) {
      // Time distortion - reverse velocity randomly
      if (Math.random() < 0.1) {
        this.body.velocity.scale(-0.5, this.body.velocity);
      } else {
        // Normal slowdown with added vertical displacement
        this.body.velocity.scale(0.4, this.body.velocity);
        this.body.velocity.y += (Math.random() - 0.5) * 5;
      }
    }

    // Apply random rotational forces for chaotic movement
    this.body.angularVelocity.set(
      (Math.random() - 0.5) * 2,
      (Math.random() - 0.5) * 2,
      (Math.random() - 0.5) * 2
    );

    // Apply strong angular damping for the slow-motion effect
    this.body.angularDamping = 0.9;

    // Add visual time-slow particle effects
    if (Math.random() < 0.3) {
      // Create time distortion particles
      this.createTimeDistortionEffect();
    }
  }

  private tryTeleport(playerPosition: THREE.Vector3): void {
    const now = Date.now();
    if (now - this.lastTeleportTime < this.TELEPORT_COOLDOWN) return;

    // Random position around player with minimum distance constraint
    const angle = Math.random() * Math.PI * 2;
    // Generate a distance between MIN_TELEPORT_DISTANCE and TELEPORT_RANGE
    const distance = this.MIN_TELEPORT_DISTANCE + Math.random() * (this.TELEPORT_RANGE - this.MIN_TELEPORT_DISTANCE);
    const teleportPos = new THREE.Vector3(
      playerPosition.x + Math.cos(angle) * distance,
      playerPosition.y,
      playerPosition.z + Math.sin(angle) * distance
    );

    // Teleport effect at old position
    this.createTeleportEffect(this.getPosition());

    // Update position
    this.body.position.set(teleportPos.x, teleportPos.y, teleportPos.z);
    this.mesh.position.copy(teleportPos);

    // Teleport effect at new position
    this.createTeleportEffect(teleportPos);

    this.lastTeleportTime = now;
  }

  private toggleCamouflage(): void {
    const now = Date.now();
    if (now - this.lastCamoTime < this.CAMO_COOLDOWN) return;

    this.isCamouflaged = !this.isCamouflaged;
    this.lastCamoTime = now;

    this.mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        material.opacity = this.isCamouflaged ? 0.2 : 0.8;
        // Change color intensity based on camouflage state
        const currentColor = material.color.clone();
        if (this.isCamouflaged) {
          material.color.lerp(new THREE.Color(0x333333), 0.8);
        } else {
          material.color.copy(new THREE.Color(this.type.color));
        }
      }
    });
  }

  private createTeleportEffect(position: THREE.Vector3): void {
    const particleCount = 20;

    // Use sprite for teleport effect
    const texturePath = '/assets/textures/phase_effect.png';
    const texture = new THREE.TextureLoader().load(texturePath);

    const particleMaterial = new THREE.SpriteMaterial({
      map: texture,
      color: 0x00ffff,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });

    for (let i = 0; i < particleCount; i++) {
      const size = Math.random() * 0.4 + 0.2;
      const particle = new THREE.Sprite(particleMaterial.clone());
      particle.scale.set(size, size, 1.0);
      particle.position.copy(position);
      this.scene.add(particle);

      // Animate particle
      const angle = (i / particleCount) * Math.PI * 2;
      const velocity = new THREE.Vector3(
        Math.cos(angle) * 2,
        Math.random() * 2,
        Math.sin(angle) * 2
      );

      let age = 0;
      const animate = () => {
        age += 0.016;
        particle.position.add(velocity.clone().multiplyScalar(0.016));
        particle.scale.multiplyScalar(0.95);

        if (age < 1.0 && particle.scale.x > 0.01) {
          requestAnimationFrame(animate);
        } else {
          this.scene.remove(particle);
          (particle.material as THREE.Material).dispose();
        }
      };
      animate();
    }
  }

  private createTimeDistortionEffect(): void {
    // Use sprite for time distortion effect
    const texturePath = '/assets/textures/time_particle.png';
    const texture = new THREE.TextureLoader().load(texturePath);

    const spriteMaterial = new THREE.SpriteMaterial({
      map: texture,
      color: 0x4400ff,
      transparent: true,
      opacity: 0.6,
      blending: THREE.AdditiveBlending
    });

    // Create time distortion particles that appear frozen in space
    const particle = new THREE.Sprite(spriteMaterial);
    const size = Math.random() * 0.4 + 0.3;
    particle.scale.set(size, size, 1.0);

    // Position particle around the specter, but not moving with it
    const angle = Math.random() * Math.PI * 2;
    const radius = 1 + Math.random() * 1.5;
    const height = Math.random() * 2 - 1;

    particle.position.set(
      this.mesh.position.x + Math.cos(angle) * radius,
      this.mesh.position.y + height,
      this.mesh.position.z + Math.sin(angle) * radius
    );

    this.scene.add(particle);

    // Slow fading animation to show "frozen time"
    let age = 0;
    const lifespan = 1.5 + Math.random(); // Longer lifespan for time particles

    const animateTimeParticle = () => {
      age += 0.016;

      // Very slow or no movement, to indicate time is nearly stopped
      // Just a gentle pulsation instead of movement
      const pulse = 1 + Math.sin(age * 2) * 0.1;
      particle.scale.set(size * pulse, size * pulse, 1.0);

      // Slowly fade out
      particle.material.opacity = 0.6 * (1 - age / lifespan);

      if (age >= lifespan) {
        this.scene.remove(particle);
        (particle.material as THREE.Material).dispose();
      } else {
        requestAnimationFrame(animateTimeParticle);
      }
    };

    animateTimeParticle();
  }

  applyPhaseEffect(): void {
    if (!this.phased) {
      this.phased = true;
      this.phasedTimer = 5; // Increased from 3 to 5 seconds for better usability

      // Make it more transparent when phased
      this.mesh.traverse((child) => {
        if (child instanceof THREE.Sprite) {
          const material = child.material as THREE.SpriteMaterial;
          material.opacity = 0.2; // More transparent than before

          // Add yellow tint to show it's phased
          material.color.setRGB(1.0, 1.0, 0.3);
        }
      });

      // Visual particle effect for phase transition
      const particleCount = 15;

      // Use sprite for phase transition effect
      const texturePath = '/assets/textures/phase_effect.png';
      const texture = new THREE.TextureLoader().load(texturePath);

      const particleMaterial = new THREE.SpriteMaterial({
        map: texture,
        color: 0xffff00,
        transparent: true,
        opacity: 0.5,
        blending: THREE.AdditiveBlending
      });

      for (let i = 0; i < particleCount; i++) {
        const size = Math.random() * 0.4 + 0.3;
        const particle = new THREE.Sprite(particleMaterial.clone());
        particle.scale.set(size, size, 1.0);

        // Position particle around the specter
        const angle = (i / particleCount) * Math.PI * 2;
        particle.position.set(
          this.mesh.position.x + Math.cos(angle) * 2,
          this.mesh.position.y + Math.random() * 2 - 1,
          this.mesh.position.z + Math.sin(angle) * 2
        );

        this.scene.add(particle);

        // Animate and remove particle
        const animateParticle = () => {
          particle.position.lerp(this.mesh.position, 0.1);
          particle.scale.multiplyScalar(0.9);

          if (particle.scale.x < 0.1) {
            this.scene.remove(particle);
            (particle.material as THREE.Material).dispose();
          } else {
            requestAnimationFrame(animateParticle);
          }
        };

        animateParticle();
      }
    }
  }

  isPhased(): boolean {
    return this.phased;
  }

  getSpecterData(): SpecterType {
    return this.type;
  }

  dispose(): void {
    // Remove from scene
    this.scene.remove(this.mesh);

    // Remove from physics world
    this.world.removeBody(this.body);

    // Dispose materials
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material.dispose();
        }
      } else if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material.dispose();
        }
      }
    });
  }

  private addParticles(): void {
    // Create sprite material for particles
    const textureLoader = new THREE.TextureLoader();
    const texture = textureLoader.load(this.type.texture);

    const particleMaterial = new THREE.SpriteMaterial({
      map: texture,
      color: new THREE.Color(this.type.color),
      transparent: true,
      opacity: 0.4,
      blending: THREE.AdditiveBlending
    });

    // Add small particles orbiting around core
    const particleCount = 10;
    for (let i = 0; i < particleCount; i++) {
      const size = Math.random() * 0.3 + 0.1;
      const distance = Math.random() * 1.5 + 1.2;

      const particle = new THREE.Sprite(particleMaterial.clone());
      particle.scale.set(size, size, 1.0);

      // Position particle on a random orbit
      const angle = (i / particleCount) * Math.PI * 2;
      const height = Math.random() * 2 - 1;

      particle.position.set(
        Math.cos(angle) * distance,
        height,
        Math.sin(angle) * distance
      );

      // Add to orbiting group
      const orbit = new THREE.Group();
      orbit.add(particle);

      // Random rotation for orbit
      orbit.rotation.x = Math.random() * Math.PI;
      orbit.rotation.y = Math.random() * Math.PI;

      // Add orbit to mesh
      this.mesh.add(orbit);
    }
  }

  /**
   * Sets the position of the specter, including both mesh and physics body
   */
  setPosition(position: THREE.Vector3): void {
    // Update physics body position
    this.body.position.set(position.x, position.y, position.z);

    // Update mesh position
    this.mesh.position.copy(position);

    // Reset velocity to prevent unwanted momentum
    this.body.velocity.set(0, 0, 0);
  }

  /**
   * Get the mesh for this specter
   */
  getMesh(): THREE.Group {
    return this.mesh;
  }

  /**
   * Get the physics body for this specter
   */
  getPhysicsBody(): CANNON.Body {
    return this.body;
  }

  /**
   * Get the mesh position of this specter (for rendering purposes)
   */
  getMeshPosition(): THREE.Vector3 {
    return this.mesh.position.clone();
  }

  /**
   * Check if enemy is defeated - needed for dungeon compatibility
   */
  isEnemyDefeated(): boolean {
    // For Specter, we consider it defeated if it's not in the scene
    return !this.mesh || !this.scene.getObjectById(this.mesh.id);
  }

  /**
   * Get attack power - needed for dungeon compatibility
   */
  getAttackPower(): number {
    return SPECTER_ATTACK_DAMAGE;
  }
}

export default Specter;