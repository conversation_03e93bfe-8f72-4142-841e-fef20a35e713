import * as THREE from "three";
import * as <PERSON><PERSON><PERSON><PERSON> from "cannon-es";
import { PointerLockControls } from "three/examples/jsm/controls/PointerLockControls";
import {
  GRAVITY,
  PLAYER_HEIGHT,
  PLAYER_MASS,
  PLAYER_MOVE_SPEED,
  PLAYER_JUMP_FORCE,
  PLAYER_MAX_HEALTH,
  PLAYER_JETPACK_FORCE,
  PLAYER_JETPACK_MAX_FUEL,
  PLAYER_JETPACK_REGEN_RATE,
  PLAYER_JETPACK_CONSUMPTION_RATE,
} from "../constants";
import { AmmoType } from '../types';
import ShattershiftRifle from '../weapons/ShattershiftRifle';

class Player {
  private camera: THREE.PerspectiveCamera;
  private controls: PointerLockControls;
  private body: CANNON.Body;
  public weapon: ShattershiftRifle | null = null;
  private scene: THREE.Scene;
  private world: CANNON.World;
  private canJump: boolean = false;
  private initialPosition: THREE.Vector3;

  // New properties for enhanced player mechanics
  private health: number = PLAYER_MAX_HEALTH;
  private jetpackFuel: number = PLAYER_JETPACK_MAX_FUEL;
  private isUsingJetpack: boolean = false;
  private isDead: boolean = false;

  // Score for accomplishments and pet purchases
  public score: number = 0;

  // Callbacks for UI updates
  public onHealthChange: (health: number) => void = () => {};
  public onJetpackFuelChange: (fuel: number) => void = () => {};
  public onDeath: () => void = () => {};

  // Player state
  public isSpectator: boolean = false; // Added public spectator flag
  public movementEnabled: boolean = true; // Control whether player can move
  private isGrounded: boolean = false;
  private jumpImpulseApplied: boolean = false; // Track if jump impulse was applied this frame
  private jetpackActive: boolean = false; // Track if jetpack is actively being used
  private inDungeonFlag: boolean = false; // Track if player is in dungeon
  private collisionsEnabled: boolean = true; // Track if collisions are enabled
  private _originalMoveSpeed: number = PLAYER_MOVE_SPEED; // Store original move speed for spectator mode
  private moveSpeed: number = PLAYER_MOVE_SPEED; // Current move speed

  // Camera properties
  private eyeHeight: number = PLAYER_HEIGHT * 0.85; // Camera height relative to body center
  private cameraTargetPosition: THREE.Vector3 = new THREE.Vector3();
  private readonly cameraLerpFactor: number = 0.1; // Smoothing factor for camera movement

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    camera: THREE.PerspectiveCamera,
    controls: PointerLockControls,
    playerMaterial: CANNON.Material,
  ) {
    this.scene = scene;
    this.world = world;
    this.camera = camera;
    this.controls = controls;

    // Set initial position
    this.initialPosition = new THREE.Vector3(0, 5, 0);
    this.camera.position.copy(this.initialPosition);

    // Create physics body - use a capsule shape instead of sphere for better collision
    const shape = new CANNON.Cylinder(0.5, 0.5, PLAYER_HEIGHT, 8);
    this.body = new CANNON.Body({
      mass: PLAYER_MASS,
      material: playerMaterial,
      shape: shape,
      position: new CANNON.Vec3(
        this.initialPosition.x,
        this.initialPosition.y,
        this.initialPosition.z,
      ),
      linearDamping: 0.0, // CRITICAL FIX: Completely removed damping for faster movement
      fixedRotation: true, // Don't rotate the player with physics
    });

    // Add body to world
    this.world.addBody(this.body);

    // Add contact event to detect if player can jump
    this.body.addEventListener("collide", this.onCollide.bind(this));

    // Initialize camera target position
    this.cameraTargetPosition.copy(this.camera.position);
  }

  update(delta: number): void {
    // IF NOT in flyMode or spectator mode, update camera based on body
    if (!this.isSpectator && !((this.body as any).userData?.flyMode)) {
        // Update camera target position based on player body
        // Set the target slightly above the body for a better view
        this.cameraTargetPosition.set(
          this.body.position.x,
          this.body.position.y + this.eyeHeight, // Use eye height offset
          this.body.position.z
        );
        // Directly copy position
        this.camera.position.copy(this.cameraTargetPosition);
    }
    // ELSE (in fly mode or spectator), the camera position remains where it was set

    // Log state before gravity check (only occasionally to avoid spam)
    if (Math.random() < 0.02) { // Log roughly every 50 frames
      console.log(`Gravity Check - isSpectator: ${this.isSpectator}, flyMode: ${!!(this.body as any)?.userData?.flyMode}, collisionsEnabled: ${this.collisionsEnabled}, body.type: ${this.body.type}, body.mass: ${this.body.mass}`);
    }

    // Apply gravity ONLY if not spectator/flying and collisions are enabled
    if (!this.isSpectator && !((this.body as any).userData?.flyMode) && this.collisionsEnabled) {
      // Make descent more gradual by reducing gravity effect by 20% when not grounded
      // and not using jetpack (when falling naturally)
      if (!this.isGrounded && !this.jetpackActive && this.body.velocity.y < 0) {
        // Apply a counterforce that's 20% of gravity when falling
        this.body.applyForce(
          new CANNON.Vec3(0, -GRAVITY * this.body.mass * 0.2, 0),
          this.body.position
        );
      }
    } else {
      // Log why gravity was skipped (occasionally)
      if (Math.random() < 0.02) {
        console.log("Gravity skipped.");
      }
    }

    // We don't need to log velocity every frame as it causes console spam

    // Jetpack fuel regeneration
    if (!this.jetpackActive && this.jetpackFuel < PLAYER_JETPACK_MAX_FUEL) {
      this.jetpackFuel = Math.min(
        PLAYER_JETPACK_MAX_FUEL,
        this.jetpackFuel + PLAYER_JETPACK_REGEN_RATE * delta,
      );
      this.onJetpackFuelChange(this.jetpackFuel);
    }

    // Reset jump impulse flag
    this.jumpImpulseApplied = false;

    // Reset jetpack flag
    this.jetpackActive = false;
  }

  move(x: number, z: number, delta: number): void {
    if (this.isDead || !this.movementEnabled) return;

    // --- Ground Check Raycast ---
    let isActuallyGrounded = false;
    const rayStart = this.body.position.clone();
    // Start ray slightly above the bottom of the capsule/cylinder base
    rayStart.y -= PLAYER_HEIGHT * 0.5 - 0.1;
    const rayEnd = rayStart.clone();
    rayEnd.y -= 0.3; // Check 0.3 units down for ground

    const ray = new CANNON.Ray(rayStart, rayEnd);
    const result = new CANNON.RaycastResult();
    // Use bitmasking if collision groups are set up, otherwise check all
    // Assuming ground is group 1 and player is group 2 (adjust if different)
    // const options = { collisionFilterMask: 1 }; // Check only against group 1 (ground)
    const options = {}; // Check against everything for now if groups aren't set

    // Perform the raycast
    const hit = this.world.raycastClosest(ray.from, ray.to, options, result);

    if (hit) {
      // Check distance to ensure it's close enough to be considered grounded
      const groundDistance = result.distance;
      if (groundDistance < 0.25) { // Allow slightly more distance than ray length
          isActuallyGrounded = true;
          if (Math.random() < 0.05) { // Log occasionally
            console.log(`Ground detected at distance: ${groundDistance.toFixed(3)}, body with name: ${result.body?.material?.name || "unknown"}`);
          }
      } else {
        if (Math.random() < 0.05) { // Log occasionally
          console.log(`Almost grounded but too far: ${groundDistance.toFixed(3)}`);
        }
      }
    } else {
      if (Math.random() < 0.05) { // Log occasionally
        console.log("No ground detected by raycast");
      }
    }
    // Update the internal isGrounded state for other logic (like jumping)
    this.isGrounded = isActuallyGrounded;
    // Also update canJump based on this immediate check
    this.canJump = isActuallyGrounded;
    // --- End Ground Check ---

    // Convert camera direction to world space
    const cameraDirection = new THREE.Vector3(0, 0, -1); // Forward is -Z in Three.js
    cameraDirection.applyQuaternion(this.camera.quaternion);
    cameraDirection.y = 0; // Flatten direction to xz plane
    cameraDirection.normalize();

    // Get perpendicular direction for strafing (right vector)
    const rightDirection = new THREE.Vector3(1, 0, 0); // Right is +X in Three.js
    rightDirection.applyQuaternion(this.camera.quaternion);
    rightDirection.y = 0; // Flatten direction to xz plane
    rightDirection.normalize();

    // Calculate movement direction relative to camera
    const moveDirection = new THREE.Vector3();

    // Forward/backward movement - multiply by -1 to fix inverted controls
    // Now when z is negative (W key), we move forward
    // When z is positive (S key), we move backward
    if (z !== 0) {
      moveDirection.addScaledVector(cameraDirection, -z);
    }

    // Left/right movement - now correctly mapped
    // When x is negative (A key), we move left
    // When x is positive (D key), we move right
    if (x !== 0) {
      moveDirection.addScaledVector(rightDirection, x);
    }

    // Only apply movement if there is input direction
    if (moveDirection.lengthSq() > 0.001) { // Use lengthSq for efficiency
      moveDirection.normalize();

      // CRITICAL FIX: Clear any existing forces that might be slowing the player down
      if (isActuallyGrounded) {
        // Clear horizontal forces when grounded to prevent sluggish movement
        this.body.force.x = 0;
        this.body.force.z = 0;
      }

      // CRITICAL FIX: Use separate movement mode when jetpack is active
      const isUsingJetpackMovement = this.jetpackActive || this.isUsingJetpack;

      // Determine target speed based on immediate ground check and jetpack state
      // MODIFIED: Increase ground speed multiplier to 3.6 for quadruple effective speed
      const groundSpeedMultiplier = 3.6;
      
      // For jetpack, use a different speed calculation
      const targetSpeed = isUsingJetpackMovement 
          ? this.moveSpeed * 0.5 // Reduced speed while jetpacking for better control
          : (isActuallyGrounded ? this.moveSpeed * groundSpeedMultiplier : this.moveSpeed * 0.3); // 40% speed in air for control
      
      if (Math.random() < 0.05) {
        // console.log(`Movement details - grounded: ${isActuallyGrounded}, jetpack: ${isUsingJetpackMovement}, targetSpeed: ${targetSpeed}, moveSpeed: ${this.moveSpeed}, multiplier: ${groundSpeedMultiplier}`);
      }

      // Calculate target velocity
      const currentVelocity = this.body.velocity;
      const targetVelocity = new CANNON.Vec3(
        moveDirection.x * targetSpeed,
        currentVelocity.y, // Maintain current vertical velocity (gravity/jump/jetpack handle this)
        moveDirection.z * targetSpeed,
      );
      
      // Debug velocity calculation occasionally
      if (Math.random() < 0.05) {
        // console.log(`Velocity calculation - current: (${currentVelocity.x.toFixed(2)}, ${currentVelocity.z.toFixed(2)}), target: (${targetVelocity.x.toFixed(2)}, ${targetVelocity.z.toFixed(2)})`);
      }

      // Original ground movement code restored
      if (isActuallyGrounded && !isUsingJetpackMovement) {
        // When grounded and NOT using jetpack, apply target velocity directly for responsive movement
        // CRITICAL FIX: Apply a multiplier to the velocity for even faster ground movement
        this.body.velocity.x = targetVelocity.x * 1.75;
        this.body.velocity.z = targetVelocity.z * 1.75;
        
        // MODIFIED: Add additional impulse when grounded to improve responsiveness
        // This gives an immediate boost to overcome any residual friction or damping
        if (Math.abs(x) > 0.1 || Math.abs(z) > 0.1) {
          this.body.applyImpulse(
            new CANNON.Vec3(moveDirection.x * 40, 0, moveDirection.z * 40),
            new CANNON.Vec3(0, 0, 0)
          );
        }
        
        if (Math.random() < 0.05) {
          // console.log("Using direct velocity setting for ground movement");
        }
      } else {
        // For air movement only, use a slower blend factor to prevent the speed boost issue
        const airBlendFactor = 0.06; // Slower response in air for more control
        
        this.body.velocity.x = currentVelocity.x * (1 - airBlendFactor) + targetVelocity.x * airBlendFactor;
        this.body.velocity.z = currentVelocity.z * (1 - airBlendFactor) + targetVelocity.z * airBlendFactor;
        
        if (Math.random() < 0.05) {
          // console.log(isUsingJetpackMovement ? "Using jetpack air movement" : "Using regular air movement");
        }
      }
    } else {
      // If no input, gradually reduce horizontal velocity (apply damping)
      // The body's linearDamping handles this naturally, but we can enhance it
       const blendFactor = 0.15; // How quickly to slow down when no input
       this.body.velocity.x *= (1 - blendFactor);
       this.body.velocity.z *= (1 - blendFactor);
    }
  }

  jump(): void {
    if (this.isDead) return;

    if (this.canJump) {
      // Apply jump force
      this.body.applyImpulse(
        new CANNON.Vec3(0, PLAYER_JUMP_FORCE, 0),
        new CANNON.Vec3(0, 0, 0),
      );
      this.canJump = false;
    }
  }

  useJetpack(delta: number): void {
    if (this.isDead || this.jetpackFuel <= 0) return;

    // Mark jetpack as being used
    this.isUsingJetpack = true;
    this.jetpackActive = true;
    
    // CRITICAL FIX: Immediately set player as not grounded when using jetpack
    this.isGrounded = false;
    this.canJump = false;

    // Calculate fuel consumption
    const fuelUsed = PLAYER_JETPACK_CONSUMPTION_RATE * delta;

    // If we have fuel, apply jetpack force
    if (this.jetpackFuel >= fuelUsed) {
      // Apply a stronger jetpack force to reliably lift the player
      // Use direct force to body.velocity for immediate effect
      const currentVelocity = this.body.velocity;

      // Boost upward velocity, but don't exceed a maximum velocity to maintain control
      const maxUpwardVelocity = 15; // Maximum upward velocity
      const newUpwardVelocity = Math.min(
        currentVelocity.y + PLAYER_JETPACK_FORCE * delta * 2,
        maxUpwardVelocity,
      );

      // Set the new velocity directly
      this.body.velocity.y = newUpwardVelocity;

      // Additionally apply a small impulse for immediate feedback
      this.body.applyImpulse(
        new CANNON.Vec3(0, PLAYER_JETPACK_FORCE * delta * 0.5, 0),
        new CANNON.Vec3(0, 0, 0),
      );

      // Create jetpack particle effect (visual feedback)
      this.createJetpackEffect();

      // Reduce fuel
      this.jetpackFuel -= fuelUsed;
      this.onJetpackFuelChange(this.jetpackFuel);
    }
  }

  private createJetpackEffect(): void {
    // Placeholder for jetpack effect
    // This would typically create particles or visual effects
    // to represent the jetpack thrust
  }

  takeDamage(amount: number): void {
    if (this.isDead) return;

    this.health = Math.max(0, this.health - amount);
    this.onHealthChange(this.health);

    // Check if player is dead
    if (this.health <= 0) {
      this.isDead = true;
      this.onDeath();
    }
  }

  heal(amount: number): void {
    if (this.isDead) return;

    this.health = Math.min(PLAYER_MAX_HEALTH, this.health + amount);
    this.onHealthChange(this.health);
  }

  getHealth(): number {
    return this.health;
  }

  getJetpackFuel(): number {
    return this.jetpackFuel;
  }

  getPosition(): THREE.Vector3 {
    return new THREE.Vector3(
      this.body.position.x,
      this.body.position.y,
      this.body.position.z,
    );
  }

  getVelocity(): THREE.Vector3 {
    return new THREE.Vector3(
      this.body.velocity.x,
      this.body.velocity.y,
      this.body.velocity.z,
    );
  }

  /**
   * Get the player's rotation (camera quaternion)
   * @returns The player's rotation as a quaternion
   */
  getRotation(): THREE.Quaternion {
    return this.camera.quaternion.clone();
  }

  /**
   * Set the player's position
   * @param position The new position for the player
   */
  setPosition(position: THREE.Vector3): void {
    // Update physics body position
    this.body.position.set(
      position.x,
      position.y,
      position.z
    );

    // Reset velocity to prevent unwanted momentum
    this.body.velocity.setZero();

    // Update camera position
    this.camera.position.set(
      position.x,
      position.y + this.eyeHeight, // Add eye height offset
      position.z
    );

    // Update camera target position
    this.cameraTargetPosition.copy(this.camera.position);
  }

  reset(): void {
    // Reset position
    this.body.position.set(
      this.initialPosition.x,
      this.initialPosition.y,
      this.initialPosition.z,
    );

    // Reset velocity
    this.body.velocity.setZero();
    this.body.angularVelocity.setZero();

    // Update camera
    this.camera.position.copy(this.initialPosition);

    // Reset player state
    this.health = PLAYER_MAX_HEALTH;
    this.jetpackFuel = PLAYER_JETPACK_MAX_FUEL;
    this.isUsingJetpack = false;
    this.isDead = false;

    // Update UI
    this.onHealthChange(this.health);
    this.onJetpackFuelChange(this.jetpackFuel);
  }

  /**
   * Get the player's physics body for use with grappling hook
   * @returns Player's physics body
   */
  getPhysicsBody(): CANNON.Body {
    return this.body;
  }

  /**
   * Get the player's camera
   * @returns Player's camera
   */
  getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  /**
   * Adjust physics properties for dungeon movement
   * This is called when entering the dungeon to ensure smooth movement
   */
  adjustForDungeonPhysics(): void {
    // Set dungeon flag
    this.inDungeonFlag = true;

    // CRITICAL FIX: Use ZERO damping for dungeon movement
    this.body.linearDamping = 0.0;
    console.log('DUNGEON PHYSICS: Set linearDamping to 0.0');

    // Reset velocity to prevent carrying momentum between worlds
    this.body.velocity.setZero();
    console.log('DUNGEON PHYSICS: Reset velocity to zero');

    // Reset angular velocity to prevent any rotation
    this.body.angularVelocity.setZero();

    // CRITICAL FIX: Force canJump to true to ensure player can move immediately
    this.canJump = true;
    console.log('DUNGEON PHYSICS: Forced canJump to true');

    console.log('Player physics adjusted for dungeon movement');
  }

  /**
   * Reset physics properties for surface world movement
   * This is called when exiting the dungeon
   */
  resetToSurfacePhysics(): void {
    // Clear dungeon flag
    this.inDungeonFlag = false;

    // Restore normal damping
    this.body.linearDamping = 0.3;

    console.log('Player physics reset for surface world');
  }

  /**
   * Check if the player is in a dungeon
   * @returns True if player is in a dungeon, false otherwise
   */
  isInDungeon(): boolean {
    // Use the flag that's set when entering/exiting the dungeon
    return this.inDungeonFlag;
  }

  /**
   * Enable or disable collisions for the player
   * @param enabled Whether collisions are enabled
   */
  setCollisionsEnabled(enabled: boolean): void {
    this.collisionsEnabled = enabled;

    if (enabled) {
      // Re-enable collisions by restoring the body's collision response
      this.body.collisionResponse = true;
    } else {
      // Disable collisions but keep physics body active
      this.body.collisionResponse = false;
    }
  }

  /**
   * Set whether the player is in fly mode (no gravity)
   * @param enabled Whether fly mode is enabled
   */
  setFlyMode(enabled: boolean): void {
    if (this.body) {
      if (enabled) {
        // Store current velocity to apply after type change
        const currentVelocity = this.body.velocity.clone();
        
        // Disable gravity for fly mode
        this.body.mass = 0;
        this.body.updateMassProperties();
        this.body.type = CANNON.Body.KINEMATIC;
        
        // Reset velocity to zero after type change
        this.body.velocity.set(0, 0, 0);
        this.body.angularVelocity.set(0, 0, 0);
        this.body.force.set(0, 0, 0);
        this.body.torque.set(0, 0, 0);
      } else {
        // Re-enable gravity
        this.body.mass = PLAYER_MASS;
        this.body.updateMassProperties();
        this.body.type = CANNON.Body.DYNAMIC;
        
        // Reset velocity when exiting fly mode
        this.body.velocity.set(0, 0, 0);
        this.body.angularVelocity.set(0, 0, 0);
      }
    }
  }

  /**
   * Set the player's movement speed for spectator mode
   * @param speed The movement speed
   */
  setSpectatorSpeed(speed: number): void {
    // Set the new move speed directly
    this.moveSpeed = speed;
  }

  /**
   * Check if collisions are enabled for the player
   * @returns Whether collisions are enabled
   */
  areCollisionsEnabled(): boolean {
    return this.collisionsEnabled;
  }

  private onCollide(event: { body: CANNON.Body }): void {
    // Check if contact is with ground

    // Get contact normal
    const contactEquation = (event as any).contact;

    if (contactEquation) {
      // Check if the contact normal is pointing upward (ground contact)
      if (contactEquation.ni.dot(new CANNON.Vec3(0, 1, 0)) > 0.5) {
        // Store previous state
        const wasGrounded = this.canJump;

        // Set grounded state
        this.canJump = true;

        // Log when player becomes grounded
        if (!wasGrounded) {
          const materialName = event.body.material ? event.body.material.name : 'unknown';
          console.log('Player is now grounded. Contact with:', materialName);

          // CRITICAL FIX: Reduce friction on ALL ground contacts for better movement
          contactEquation.friction = 0.1; // Very low friction for all ground surfaces
          
          // CRITICAL FIX: For dungeon floor specifically
          if (this.isInDungeon() && materialName === 'dungeonFloor') {
            // Force the contact equation properties to allow sliding
            contactEquation.friction = 1; // Almost no friction

            // Apply an immediate impulse to maintain momentum
            const currentVel = this.body.velocity.clone();

            // Only apply if we have significant horizontal velocity
            if (Math.abs(currentVel.x) > 1 || Math.abs(currentVel.z) > 1) {
              // Apply impulse in the direction of movement
              this.body.applyImpulse(
                new CANNON.Vec3(currentVel.x * 10, 0, currentVel.z * 10),
                new CANNON.Vec3(0, 0, 0)
              );
              console.log(`Applied impulse to maintain momentum: (${currentVel.x.toFixed(2)}, ${currentVel.z.toFixed(2)})`);
            }
          }
        }
      }
    }
  }

  /**
   * Resets the player's physics body's forces, velocities, and potentially position.
   * Useful for teleporting, respawning, or entering spectator mode.
   */
  public resetPhysicsState(): void {
    console.log("Player: Resetting physics state.");
    // Reset velocities
    this.body.velocity.set(0, 0, 0);
    this.body.angularVelocity.set(0, 0, 0);

    // Reset forces
    this.body.force.set(0, 0, 0);
    this.body.torque.set(0, 0, 0);

     // It might be desirable to also reset position here, but often
     // the caller wants to set a *new* position immediately after resetting.
     // Let setPosition handle position updates.
  }

  /**
   * Configures the player's physics body for spectator mode.
   * Typically involves disabling gravity and potentially changing collision groups.
   */
  public setSpectatorPhysics(): void {
    console.log("Player: Setting spectator physics.");
    this.isSpectator = true;
    // Disable standard gravity application for the body
    // Setting mass to 0 might be an option, but could have side effects.
    // Instead, we will rely on the check in the update loop to not apply gravity.
    this.body.type = CANNON.Body.KINEMATIC; // Make body kinematic so it's not affected by forces like gravity
                                          // It can still be moved programmatically via setPosition.
    this.body.collisionResponse = false; // Disable collision response
    // Optionally, change collision group/mask if needed
    // this.body.collisionFilterGroup = SPECTATOR_GROUP;
    // this.body.collisionFilterMask = WORLD_MASK; // Only collide with world, not other objects?
  }

  /**
   * Reverts physics changes made for spectator mode.
   */
  public unsetSpectatorPhysics(): void {
    console.log("Player: Unsetting spectator physics.");
    this.isSpectator = false;
    this.body.type = CANNON.Body.DYNAMIC; // Restore dynamic body type
    this.body.collisionResponse = true; // Re-enable collision response
    // Restore original linear damping (assuming 0.3 was the default from constructor)
    this.body.linearDamping = 0.3;
    // Restore player mass if it was changed
    if (this.body.mass === 0) {
      this.body.mass = PLAYER_MASS;
      this.body.updateMassProperties();
    }
    // Restore original collision group/mask if changed
    // this.body.collisionFilterGroup = PLAYER_GROUP;
    // this.body.collisionFilterMask = DEFAULT_MASK;
  }

  public setIsUsingJetpack(active: boolean): void {
    this.isUsingJetpack = active;
  }
}

export default Player;
