import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';
import { DungeonDifficulty } from '../world/DungeonGenerator';
import { SeededRandom } from '../utils/SeededRandom';
import { audioManager as AudioManager } from '../audio/AudioManager';

// Enemy types for dungeon
export enum DungeonEnemyType {
  WISP = 'wisp',
  PHANTOM = 'phantom',
  WRAITH = 'wraith',
  POLTERGEIST = 'poltergeist',
  BANSHEE = 'banshee'
}

/**
 * DungeonEnemy class for enemies in the dungeon
 */
export class DungeonEnemy {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private position: THREE.Vector3;
  private mesh: THREE.Group;
  private body: CANNON.Body;
  private type: DungeonEnemyType;
  private difficulty: DungeonDifficulty;
  private dungeonLevel: number;
  private random: SeededRandom;
  private health: number;
  private maxHealth: number;
  private attackPower: number;
  private speed: number;
  private attackRange: number = 3;
  private attackCooldown: number = 0;
  private isDefeated: boolean = false;
  private isAttacking: boolean = false;
  private detectionRange: number = 15;
  private pointValue: number;

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    difficulty: DungeonDifficulty,
    dungeonLevel: number,
    seed: number
  ) {
    this.scene = scene;
    this.world = world;
    this.position = position;
    this.difficulty = difficulty;
    this.dungeonLevel = dungeonLevel;
    this.random = new SeededRandom(seed);

    // Determine enemy type based on dungeon level and randomness
    this.type = this.determineEnemyType();

    // Set base stats based on type and difficulty
    this.setBaseStats();

    // Create visual representation
    this.mesh = this.createVisual();

    // Create physics body
    this.body = this.createPhysicsBody();

    // Add to scene and world
    this.scene.add(this.mesh);
    this.world.addBody(this.body);
  }

  /**
   * Determine enemy type based on dungeon level and randomness
   */
  private determineEnemyType(): DungeonEnemyType {
    // Higher level dungeons have more advanced enemy types
    const availableTypes: DungeonEnemyType[] = [];

    // WISP is available at all levels
    availableTypes.push(DungeonEnemyType.WISP);

    // PHANTOM is available at level 2+
    if (this.dungeonLevel >= 2) {
      availableTypes.push(DungeonEnemyType.PHANTOM);
    }

    // WRAITH is available at level 4+
    if (this.dungeonLevel >= 4) {
      availableTypes.push(DungeonEnemyType.WRAITH);
    }

    // POLTERGEIST is available at level 6+
    if (this.dungeonLevel >= 6) {
      availableTypes.push(DungeonEnemyType.POLTERGEIST);
    }

    // BANSHEE is available at level 8+
    if (this.dungeonLevel >= 8) {
      availableTypes.push(DungeonEnemyType.BANSHEE);
    }

    // Choose a random type from available types
    return this.random.choose(availableTypes);
  }

  /**
   * Set base stats based on type and difficulty
   */
  private setBaseStats(): void {
    // Base stats by type
    switch (this.type) {
      case DungeonEnemyType.WISP:
        this.maxHealth = 30;
        this.attackPower = 5;
        this.speed = 3;
        this.pointValue = 50;
        break;
      case DungeonEnemyType.PHANTOM:
        this.maxHealth = 50;
        this.attackPower = 8;
        this.speed = 4;
        this.pointValue = 100;
        break;
      case DungeonEnemyType.WRAITH:
        this.maxHealth = 80;
        this.attackPower = 12;
        this.speed = 5;
        this.pointValue = 200;
        break;
      case DungeonEnemyType.POLTERGEIST:
        this.maxHealth = 120;
        this.attackPower = 15;
        this.speed = 6;
        this.pointValue = 300;
        break;
      case DungeonEnemyType.BANSHEE:
        this.maxHealth = 150;
        this.attackPower = 20;
        this.speed = 7;
        this.pointValue = 500;
        break;
      default:
        this.maxHealth = 30;
        this.attackPower = 5;
        this.speed = 3;
        this.pointValue = 50;
    }

    // Apply difficulty multiplier
    const difficultyMultiplier = {
      [DungeonDifficulty.EASY]: 0.8,
      [DungeonDifficulty.MEDIUM]: 1.0,
      [DungeonDifficulty.HARD]: 1.3,
      [DungeonDifficulty.NIGHTMARE]: 1.8
    };

    const multiplier = difficultyMultiplier[this.difficulty];

    this.maxHealth = Math.ceil(this.maxHealth * multiplier);
    this.attackPower = Math.ceil(this.attackPower * multiplier);
    this.speed = this.speed * multiplier;
    this.pointValue = Math.ceil(this.pointValue * multiplier);

    // Apply dungeon level scaling (10% increase per level)
    const levelMultiplier = 1 + (this.dungeonLevel - 1) * 0.1;

    this.maxHealth = Math.ceil(this.maxHealth * levelMultiplier);
    this.attackPower = Math.ceil(this.attackPower * levelMultiplier);
    this.speed = this.speed * Math.sqrt(levelMultiplier); // Square root for more balanced speed scaling
    this.pointValue = Math.ceil(this.pointValue * levelMultiplier);

    // Set current health to max
    this.health = this.maxHealth;
  }

  /**
   * Create visual representation based on enemy type
   */
  private createVisual(): THREE.Group {
    const group = new THREE.Group();

    // Get color and texture based on enemy type from existing specter textures
    let color: number;
    let texturePath: string;

    // Map dungeon enemy types to existing specter textures
    // Make sure to use the correct paths that exist in the game
    switch (this.type) {
      case DungeonEnemyType.WISP:
        color = 0x3399ff; // Blue
        texturePath = 'assets/textures/specter1.svg'; // Use existing specter texture
        break;
      case DungeonEnemyType.PHANTOM:
        color = 0xe1a95f; // Gold
        texturePath = 'assets/textures/specter2.svg'; // Use existing specter texture
        break;
      case DungeonEnemyType.WRAITH:
        color = 0x8844ff; // Purple
        texturePath = 'assets/textures/specter3.svg'; // Use existing specter texture
        break;
      case DungeonEnemyType.POLTERGEIST:
        color = 0x00ddff; // Cyan
        texturePath = 'assets/textures/specter4.svg'; // Use existing specter texture
        break;
      case DungeonEnemyType.BANSHEE:
        color = 0xffdd00; // Yellow
        texturePath = 'assets/textures/specter5.svg'; // Use existing specter texture
        break;
      default:
        color = 0x3399ff; // Blue
        texturePath = 'assets/textures/specter1.svg'; // Use existing specter texture
    }

    console.log(`Creating enemy of type ${this.type} with texture: ${texturePath}`);

    // Load specter texture
    const textureLoader = new THREE.TextureLoader();
    const specterTexture = textureLoader.load(
      texturePath,
      undefined,
      undefined,
      (error) => {
        console.warn('Error loading enemy texture:', texturePath, error);
        // Fallback to a simple sprite if texture fails to load
        const fallbackMaterial = new THREE.SpriteMaterial({
          color: color,
          transparent: true,
          opacity: 0.8
        });
        const fallbackSprite = new THREE.Sprite(fallbackMaterial);
        fallbackSprite.scale.set(2, 2, 1);
        group.add(fallbackSprite);
      }
    );

    // Create sprite-based enemy that always faces camera
    const size = 2;
    const spriteMaterial = new THREE.SpriteMaterial({
      map: specterTexture,
      color: new THREE.Color(color),
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending
    });

    // Create main sprite
    const specterSprite = new THREE.Sprite(spriteMaterial);
    specterSprite.scale.set(size, size, 1);
    group.add(specterSprite);

    // Add particle effects
    const particleCount = 20;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const radius = 0.6 + Math.random() * 0.4;

      particlePositions[i * 3] = Math.cos(angle) * radius;
      particlePositions[i * 3 + 1] = Math.random() * 1.5 - 0.5;
      particlePositions[i * 3 + 2] = Math.sin(angle) * radius;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.1,
      transparent: true,
      opacity: 0.5
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    // Position the group
    group.position.copy(this.position);

    return group;
  }

  /**
   * Create physics body for the enemy
   */
  private createPhysicsBody(): CANNON.Body {
    // Create a sphere shape for physics
    const radius = 0.7; // Slightly larger than visual for better collision
    const shape = new CANNON.Sphere(radius);

    // Create body
    const body = new CANNON.Body({
      mass: 10, // Light but not weightless
      position: new CANNON.Vec3(this.position.x, this.position.y, this.position.z),
      shape: shape,
      material: new CANNON.Material({
        friction: 0.3,
        restitution: 0.4
      }),
      linearDamping: 0.9, // Add damping to prevent excessive sliding
      angularDamping: 0.9
    });

    return body;
  }

  /**
   * Update enemy state
   */
  public update(delta: number, playerPosition: THREE.Vector3): void {
    if (this.isDefeated) return;

    // Update attack cooldown
    if (this.attackCooldown > 0) {
      this.attackCooldown -= delta;
    }

    // Update position from physics
    this.mesh.position.copy(this.body.position);

    // Calculate distance to player
    const distanceToPlayer = this.mesh.position.distanceTo(playerPosition);

    // If player is within detection range, move towards them
    if (distanceToPlayer < this.detectionRange) {
      // Calculate direction to player
      const direction = new THREE.Vector3()
        .subVectors(playerPosition, this.mesh.position)
        .normalize();

      // Look at player
      this.mesh.lookAt(playerPosition);

      // If within attack range and cooldown is complete, attack
      if (distanceToPlayer < this.attackRange && this.attackCooldown <= 0) {
        this.attack();
      } else {
        // Otherwise move towards player
        const force = direction.multiplyScalar(this.speed * 10 * delta);

        this.body.applyForce(
          new CANNON.Vec3(force.x, force.y, force.z),
          new CANNON.Vec3(0, 0, 0)
        );
      }
    } else {
      // Random movement when player is not detected
      if (Math.random() < 0.02) {
        const randomDirection = new THREE.Vector3(
          Math.random() * 2 - 1,
          0,
          Math.random() * 2 - 1
        ).normalize();

        const force = randomDirection.multiplyScalar(this.speed * 5 * delta);

        this.body.applyForce(
          new CANNON.Vec3(force.x, force.y, force.z),
          new CANNON.Vec3(0, 0, 0)
        );
      }
    }

    // Animate particles
    this.animateParticles(delta);
  }

  /**
   * Animate particles for visual effect
   */
  private animateParticles(delta: number): void {
    // Find particles in the mesh group
    this.mesh.traverse(child => {
      if (child instanceof THREE.Points) {
        // Get positions
        const positions = child.geometry.attributes.position.array;

        // Animate each particle
        for (let i = 0; i < positions.length; i += 3) {
          // Oscillate particles
          positions[i] += Math.sin(Date.now() * 0.001 + i) * 0.01;
          positions[i + 1] += Math.cos(Date.now() * 0.002 + i) * 0.01;
          positions[i + 2] += Math.sin(Date.now() * 0.001 + i) * 0.01;
        }

        // Update geometry
        child.geometry.attributes.position.needsUpdate = true;
      }
    });
  }

  /**
   * Perform attack
   */
  private attack(): void {
    // Set cooldown
    this.attackCooldown = 1.0;

    // Set attacking flag
    this.isAttacking = true;

    // Play attack sound
    AudioManager.playSoundEffect('specterAttack');

    // Visual attack effect
    this.createAttackEffect();

    // Reset attacking flag after a short delay
    setTimeout(() => {
      this.isAttacking = false;
    }, 500);
  }

  /**
   * Create visual attack effect
   */
  private createAttackEffect(): void {
    // Create attack particles
    const particleCount = 10;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Get enemy position
    const position = this.mesh.position.clone();

    // Create particles in a cone shape in front of enemy
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.random() - 0.5) * Math.PI / 2;
      const distance = 0.5 + Math.random() * 2;

      // Calculate direction based on enemy rotation
      const direction = new THREE.Vector3(0, 0, 1);
      direction.applyQuaternion(this.mesh.quaternion);

      // Add randomness to direction
      direction.x += Math.sin(angle) * 0.5;
      direction.y += (Math.random() - 0.5) * 0.2;

      // Set particle position
      particlePositions[i * 3] = position.x + direction.x * distance;
      particlePositions[i * 3 + 1] = position.y + direction.y * distance;
      particlePositions[i * 3 + 2] = position.z + direction.z * distance;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Get color based on enemy type
    let color: number;
    switch (this.type) {
      case DungeonEnemyType.WISP: color = 0x66ccff; break;
      case DungeonEnemyType.PHANTOM: color = 0xccccff; break;
      case DungeonEnemyType.WRAITH: color = 0x9966cc; break;
      case DungeonEnemyType.POLTERGEIST: color = 0xff6666; break;
      case DungeonEnemyType.BANSHEE: color = 0x33cc33; break;
      default: color = 0x66ccff;
    }

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.2,
      transparent: true,
      opacity: 0.8
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Animate and remove particles after a short time
    let time = 0;
    const duration = 0.5;

    const animateParticles = () => {
      time += 0.016; // Approximately 60fps

      if (time < duration) {
        // Move particles outward
        const positions = particleGeometry.attributes.position.array;

        for (let i = 0; i < particleCount; i++) {
          const index = i * 3;
          const dirX = positions[index] - position.x;
          const dirY = positions[index + 1] - position.y;
          const dirZ = positions[index + 2] - position.z;

          positions[index] += dirX * 0.1;
          positions[index + 1] += dirY * 0.1;
          positions[index + 2] += dirZ * 0.1;
        }

        particleGeometry.attributes.position.needsUpdate = true;

        // Fade out
        particleMaterial.opacity = 0.8 * (1 - time / duration);

        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animateParticles();
  }

  /**
   * Take damage from player or pet
   */
  public takeDamage(amount: number): void {
    if (this.isDefeated) return;

    // Reduce health
    this.health -= amount;

    // Check if defeated
    if (this.health <= 0) {
      this.defeat();
    } else {
      // Visual feedback for taking damage
      this.showDamageEffect();
    }
  }

  /**
   * Show visual effect when taking damage
   */
  private showDamageEffect(): void {
    // Flash enemy red
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
        // Store original color
        const originalColor = child.material.color.clone();
        const originalEmissive = child.material.emissive.clone();

        // Flash red
        child.material.color.set(0xff0000);
        child.material.emissive.set(0xff0000);

        // Restore original color after flash
        setTimeout(() => {
          if (child.material instanceof THREE.MeshStandardMaterial) {
            child.material.color.copy(originalColor);
            child.material.emissive.copy(originalEmissive);
          }
        }, 100);
      }
    });

    // Play damage sound
    AudioManager.playSoundEffect('enemyHit');
  }

  /**
   * Defeat the enemy
   */
  private defeat(): void {
    this.isDefeated = true;

    // Play defeat sound
    AudioManager.playSoundEffect('enemyDefeat');

    // Create defeat effect
    this.createDefeatEffect();
  }

  /**
   * Create visual effect for enemy defeat
   */
  private createDefeatEffect(): void {
    // Create explosion particles
    const particleCount = 30;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Get enemy position
    const position = this.mesh.position.clone();

    // Create particles in a sphere around enemy
    for (let i = 0; i < particleCount; i++) {
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const radius = Math.random() * 1.5;

      particlePositions[i * 3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = position.z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Get color based on enemy type
    let color: number;
    switch (this.type) {
      case DungeonEnemyType.WISP: color = 0x66ccff; break;
      case DungeonEnemyType.PHANTOM: color = 0xccccff; break;
      case DungeonEnemyType.WRAITH: color = 0x9966cc; break;
      case DungeonEnemyType.POLTERGEIST: color = 0xff6666; break;
      case DungeonEnemyType.BANSHEE: color = 0x33cc33; break;
      default: color = 0x66ccff;
    }

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.3,
      transparent: true,
      opacity: 1.0
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Animate and remove particles
    let time = 0;
    const duration = 1.0;

    const animateParticles = () => {
      time += 0.016; // Approximately 60fps

      if (time < duration) {
        // Move particles outward
        const positions = particleGeometry.attributes.position.array;

        for (let i = 0; i < particleCount; i++) {
          const index = i * 3;
          const dirX = positions[index] - position.x;
          const dirY = positions[index + 1] - position.y;
          const dirZ = positions[index + 2] - position.z;

          positions[index] += dirX * 0.05;
          positions[index + 1] += dirY * 0.05;
          positions[index + 2] += dirZ * 0.05;
        }

        particleGeometry.attributes.position.needsUpdate = true;

        // Fade out
        particleMaterial.opacity = 1.0 * (1 - time / duration);

        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animateParticles();

    // Hide enemy mesh
    this.mesh.visible = false;
  }

  /**
   * Get enemy position
   */
  public getPosition(): THREE.Vector3 {
    return this.mesh.position.clone();
  }

  /**
   * Check if enemy is defeated
   */
  public isEnemyDefeated(): boolean {
    return this.isDefeated;
  }

  /**
   * Get enemy data for scoring/rewards
   */
  public getEnemyData(): any {
    return {
      type: this.type,
      points: this.pointValue,
      position: this.getPosition(),
      difficulty: this.difficulty,
      dungeonLevel: this.dungeonLevel
    };
  }

  /**
   * Get attack power
   */
  public getAttackPower(): number {
    return this.attackPower;
  }

  /**
   * Dispose of all resources
   */
  public dispose(): void {
    // Remove from scene
    this.scene.remove(this.mesh);

    // Remove from physics world
    this.world.removeBody(this.body);

    // Dispose of geometries and materials
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();

        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else if (child.material) {
          child.material.dispose();
        }
      }
    });
  }
}