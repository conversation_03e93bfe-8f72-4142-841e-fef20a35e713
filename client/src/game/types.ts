import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';

// Ammo types
export enum AmmoType {
  GravityWell = 'Gravity Well',
  TimeBubble = 'Time Bubble',
  PhaseNet = 'Phase Net'
}

// Effect types
export type EffectType = 'gravity' | 'time' | 'phase';

// Specter type enum
export enum SpecterTypeEnum {
  WISP = 'WISP',
  PHANTOM = 'PHANTOM',
  POLTERGEIST = 'POLTERGEIST',
  WRAITH = 'WRAITH',
  BANSHEE = 'BANSHEE'
}

// Specter tier enum for pricing
export enum SpecterTier {
  COMMON = 'COMMON',    // $1 (WISP)
  UNCOMMON = 'UNCOMMON', // $5 (PHANTOM)
  RARE = 'RARE',         // $10 (POLTERGEIST)
  EPIC = 'EPIC',         // $20 (WRAITH)
  LEGENDARY = 'LEGENDARY' // $50 (BANSHEE)
}

// Specter data type
export interface SpecterType {
  id: number;
  name: string;
  color: string;
  points: number;
  texture: string; // Path to the specter texture
  tier?: SpecterTier; // Tier for NFT pricing
  maticPrice?: string; // Price in MATIC for NFT minting
}

// UI types for minimap and HUD components
export interface PlayerInfo {
  id: string;
  name: string;
  score: number;
  team?: number;
  position?: {
    x: number;
    y: number;
    z: number;
  };
}

export interface TeamInfo {
  id: number;
  name: string;
  color: string;
  score: number;
}

// Weapon effect data for networking
export interface WeaponEffectData {
  id: string;
  type: 'gravity' | 'time' | 'phase';
  position: {
    x: number;
    y: number;
    z: number;
  };
  radius: number;
  timeLeft: number;
}

// ShattershiftRifle interface definition for TypeScript type checking
export interface ShattershiftRifle {
  update(delta: number): void;
  fireAmmo(): boolean;
  setAmmoType(ammoType: AmmoType): void;
  getCurrentAmmoType(): AmmoType;
  getAmmo(): number[];
  refillAmmo(amount: number): void;
  refillAmmoByType(ammoType: AmmoType, amount: number): void;
  fireGrapplingHook(): void;
  updateGrapplingHook(playerBody: CANNON.Body): void;
  enableHoming(duration?: number): void;
  updateHoming(delta: number): void;
  isHomingEnabled(): boolean;
  reset(): void;
}

// Player interface extension for TypeScript type checking
export interface Player {
  score: number;
  update(delta: number): void;
  move(x: number, z: number, delta: number): void;
  jump(): void;
  useJetpack(delta: number): void;
  getPosition(): THREE.Vector3;
  setPosition(position: THREE.Vector3): void;
  getPhysicsBody(): CANNON.Body;
  takeDamage(amount: number): void;
  heal(amount: number): void;
}
