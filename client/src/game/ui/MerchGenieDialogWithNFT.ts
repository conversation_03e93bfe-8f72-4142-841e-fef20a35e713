import Player from '../entities/Player';
import { PetSpecter, SpecterEquipment } from '../entities/PetSpecter';
import Specter from '../entities/Specter';
import { SpecterType, SpecterTypeEnum, SpecterTier } from '../types';
import { audioManager as AudioManager } from '../audio/AudioManager';
import { playerInventory } from '../inventory/PlayerInventory';
import { AVAILABLE_EQUIPMENT, getEquipmentPrice, getEquipmentColor } from '../data/EquipmentData';

/**
 * UI dialog for MerchGenie kiosk with NFT support
 * Allows players to purchase pet specters with points or mint them as NFTs
 */
export class MerchGenieDialogWithNFT {
  private player: Player;
  private container: HTMLElement;
  public isOpen: boolean = false;
  private selectedType: SpecterType = {
    id: 0,
    name: 'WISP',
    color: '#3399ff',
    points: 100,
    texture: 'assets/textures/wisp.png'
  };
  private customName: string = '';
  private cost: number = 100;

  // Equipment selection
  private selectedEquipment: SpecterEquipment | null = null;
  private equipmentCost: number = 0;
  private activeTab: 'pets' | 'equipment' = 'pets';

  constructor(player: Player) {
    this.player = player;

    // Create container
    this.container = document.createElement('div');
    this.container.className = 'merch-genie-dialog';
    this.container.style.position = 'fixed';
    this.container.style.top = '50%';
    this.container.style.left = '50%';
    this.container.style.transform = 'translate(-50%, -50%)';
    this.container.style.width = '500px';
    this.container.style.maxWidth = '90%';
    this.container.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    this.container.style.border = '2px solid #00ffff';
    this.container.style.borderRadius = '10px';
    this.container.style.padding = '20px';
    this.container.style.color = 'white';
    this.container.style.fontFamily = 'Arial, sans-serif';
    this.container.style.zIndex = '1000';
    this.container.style.display = 'none';
    this.container.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.5)';

    // Create content
    this.createDialogContent();

    // Add to document
    document.body.appendChild(this.container);

    // Create tabs
    this.createTabs();

    // Add event listeners
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });

    // Listen for name input changes
    document.addEventListener('input', (e) => {
      if (e.target && (e.target as HTMLElement).id === 'pet-name') {
        this.customName = (e.target as HTMLInputElement).value;
      }
    });
  }

  /**
   * Create dialog content
   */
  private createDialogContent(): void {
    // Header
    const header = document.createElement('div');
    header.className = 'dialog-header';
    header.innerHTML = `
      <h2 style="color: #00ffff; text-align: center; margin-top: 0; text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);">MerchGenie</h2>
      <p style="text-align: center; margin-bottom: 20px;">Your one-stop shop for spectral companions and equipment!</p>
    `;
    this.container.appendChild(header);

    // Create main container for tabs content
    const contentContainer = document.createElement('div');
    contentContainer.id = 'merch-genie-content';
    contentContainer.style.width = '100%';
    this.container.appendChild(contentContainer);

    // Create pet content container
    const petContent = document.createElement('div');
    petContent.id = 'pet-content';
    petContent.style.display = 'block'; // Show by default
    contentContainer.appendChild(petContent);

    // Create equipment content container
    const equipmentContent = document.createElement('div');
    equipmentContent.id = 'equipment-content';
    equipmentContent.style.display = 'none'; // Hidden by default
    contentContainer.appendChild(equipmentContent);

    // Build pet content
    this.createPetContent(petContent);

    // Build equipment content - we'll create it when the tab is clicked
    // to ensure it's properly displayed
  }

  /**
   * Create tabs for switching between pets and equipment
   */
  private createTabs(): void {
    const tabsContainer = document.createElement('div');
    tabsContainer.style.display = 'flex';
    tabsContainer.style.marginBottom = '15px';
    tabsContainer.style.borderBottom = '1px solid rgba(0, 255, 255, 0.3)';

    // Pets tab
    const petsTab = document.createElement('div');
    petsTab.textContent = 'Pet Specters';
    petsTab.style.padding = '8px 15px';
    petsTab.style.cursor = 'pointer';
    petsTab.style.borderBottom = '2px solid #00ffff';
    petsTab.style.color = '#00ffff';
    petsTab.style.marginRight = '10px';

    // Equipment tab
    const equipmentTab = document.createElement('div');
    equipmentTab.textContent = 'Equipment';
    equipmentTab.style.padding = '8px 15px';
    equipmentTab.style.cursor = 'pointer';
    equipmentTab.style.borderBottom = '2px solid transparent';
    equipmentTab.style.color = '#aaaaaa';

    // Add click handlers
    petsTab.addEventListener('click', () => {
      this.switchTab('pets');
      petsTab.style.borderBottom = '2px solid #00ffff';
      petsTab.style.color = '#00ffff';
      equipmentTab.style.borderBottom = '2px solid transparent';
      equipmentTab.style.color = '#aaaaaa';
      AudioManager.playSoundEffect('uiClick');
    });

    equipmentTab.addEventListener('click', () => {
      this.switchTab('equipment');
      equipmentTab.style.borderBottom = '2px solid #00ffff';
      equipmentTab.style.color = '#00ffff';
      petsTab.style.borderBottom = '2px solid transparent';
      petsTab.style.color = '#aaaaaa';
      AudioManager.playSoundEffect('uiClick');
    });

    tabsContainer.appendChild(petsTab);
    tabsContainer.appendChild(equipmentTab);

    // Insert tabs after header
    const header = this.container.querySelector('.dialog-header');
    if (header && header.nextSibling) {
      this.container.insertBefore(tabsContainer, header.nextSibling);
    } else {
      this.container.appendChild(tabsContainer);
    }
  }

  /**
   * Switch between tabs
   */
  private switchTab(tab: 'pets' | 'equipment'): void {
    this.activeTab = tab;

    const petContent = document.getElementById('pet-content');
    const equipmentContent = document.getElementById('equipment-content');

    if (petContent && equipmentContent) {
      if (tab === 'pets') {
        petContent.style.display = 'block';
        equipmentContent.style.display = 'none';
      } else {
        // Clear equipment content first
        equipmentContent.innerHTML = '';

        // Create equipment content
        this.createEquipmentContent(equipmentContent);

        // Show equipment content
        petContent.style.display = 'none';
        equipmentContent.style.display = 'block';
      }
    }
  }

  /**
   * Create pet content
   */
  private createPetContent(container: HTMLElement): void {
    // Specter selection
    const selectionContainer = document.createElement('div');
    selectionContainer.className = 'selection-container';
    selectionContainer.style.display = 'flex';
    selectionContainer.style.justifyContent = 'space-between';
    selectionContainer.style.marginBottom = '20px';
    selectionContainer.style.overflowX = 'auto';
    selectionContainer.style.padding = '10px 0';

    // Define available specters with tiers - using Demo NFT Pets
    const specterTypes = [
      {
        type: {
          id: 0,
          name: 'SANTA',
          color: '#3399ff',
          points: 100,
          texture: 'uploads/images/a4780f582a4cc18809e9be5a5b4e52f2.png',
          tier: SpecterTier.COMMON,
          maticPrice: '0.001'
        },
        cost: 100,
        nftPrice: '0.001'
      },
      {
        type: {
          id: 1,
          name: 'MAGICIAN',
          color: '#ff3399',
          points: 250,
          texture: 'uploads/images/de1a7654b03d749e341062669b7974a5.png',
          tier: SpecterTier.UNCOMMON,
          maticPrice: '0.002'
        },
        cost: 250,
        nftPrice: '0.002'
      },
      {
        type: {
          id: 2,
          name: 'CARBON FIBER',
          color: '#33ff99',
          points: 500,
          texture: '/uploads/images/0f19922a7112c8920ea334f67db9f1ed.png',
          tier: SpecterTier.EPIC,
          maticPrice: '0.003'
        },
        cost: 500,
        nftPrice: '0.003'
      },
      {
        type: {
          id: 3,
          name: 'Devil Kong',
          color: '#9933ff',
          points: 750,
          texture: 'uploads/images/e2b59d4bda83ccb4de14f349996dd67c.png',
          tier: SpecterTier.RARE,
          maticPrice: '0.005'
        },
        cost: 1000,
        nftPrice: '0.005'
      },
      {
        type: {
          id: 3,
          name: 'SuperSaiyan BOKU',
          color: '#9933ff',
          points: 1000,
          texture: 'uploads/images/c8936786d1264edcc57e5a7dd94015a3.png',
          tier: SpecterTier.RARE,
          maticPrice: '0.005'
        },
        cost: 1000,
        nftPrice: '0.005'
      },
      {
        type: {
          id: 4,
          name: 'BORED SUPER APE',
          color: '#ff9933',
          points: 2000,
          texture: 'uploads/images/0ae71911fbbb2a6d09b0cc004e332759.png',
          tier: SpecterTier.LEGENDARY,
          maticPrice: '0.01'
        },
        cost: 2000,
        nftPrice: '0.01'
      }


    ];

    specterTypes.forEach(specter => {
      const option = document.createElement('div');
      option.className = 'specter-option';
      option.setAttribute('data-type', specter.type.name);
      option.style.width = '60px';
      option.style.textAlign = 'center';
      option.style.cursor = 'pointer';
      option.style.transition = 'all 0.2s ease';
      option.style.opacity = '0.7';
      option.style.padding = '10px';
      option.style.borderRadius = '5px';

      // Use image for Demo NFT pets instead of colored circle
      option.innerHTML = `
        <div style="width: 40px; height: 40px; margin: 0 auto; overflow: hidden; border-radius: 50%; box-shadow: 0 0 10px ${specter.type.color};">
          <img src="${specter.type.texture}" style="width: 100%; height: 100%; object-fit: cover;" alt="${specter.type.name}" />
        </div>
        <div style="margin-top: 5px; font-size: 12px;">${specter.type.name}</div>
        <div style="font-size: 10px; color: #aaaaaa;">${specter.type.tier}</div>
        <div style="font-size: 10px;">${specter.cost} pts</div>
      `;

      option.addEventListener('click', () => {
        // Deselect all
        document.querySelectorAll('.specter-option').forEach(el => {
          (el as HTMLElement).style.backgroundColor = 'transparent';
          (el as HTMLElement).style.opacity = '0.7';
        });

        // Select this one
        option.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
        option.style.opacity = '1';

        // Update selected type and cost
        this.selectedType = specter.type;
        this.cost = specter.cost;

        // Update cost display
        const costDisplay = document.getElementById('cost-display');
        if (costDisplay) {
          costDisplay.textContent = `${specter.cost}`;
        }

        // Update tier display
        const tierDisplay = document.getElementById('tier-display');
        if (tierDisplay) {
          tierDisplay.textContent = `${specter.type.tier}`;
        }

        // Update NFT price display
        const nftPriceDisplay = document.getElementById('nft-price-display');
        if (nftPriceDisplay) {
          nftPriceDisplay.textContent = `${specter.nftPrice}`;
        }

        // Update preview
        this.updatePreview(specter.type.color);

        // Play selection sound
        AudioManager.playSoundEffect('uiClick');
      });

      selectionContainer.appendChild(option);
    });

    container.appendChild(selectionContainer);

    // Preview container
    const previewContainer = document.createElement('div');
    previewContainer.className = 'preview-container';
    previewContainer.style.display = 'flex';
    previewContainer.style.marginBottom = '20px';

    // Visual preview
    const previewVisual = document.createElement('div');
    previewVisual.id = 'preview-visual';
    previewVisual.style.width = '100px';
    previewVisual.style.height = '100px';
    previewVisual.style.display = 'flex';
    previewVisual.style.alignItems = 'center';
    previewVisual.style.justifyContent = 'center';
    previewVisual.style.marginRight = '20px';

    // Create preview container for the image
    const previewSphere = document.createElement('div');
    previewSphere.style.width = '60px';
    previewSphere.style.height = '60px';
    previewSphere.style.borderRadius = '50%';
    previewSphere.style.overflow = 'hidden';
    previewSphere.style.boxShadow = '0 0 15px #3399ff';

    // Add image element
    const previewImage = document.createElement('img');
    previewImage.src = 'uploads/images/0f9d515248ee4a1cebf01f930f0d1e21.png'; // Default image
    previewImage.style.width = '100%';
    previewImage.style.height = '100%';
    previewImage.style.objectFit = 'cover';
    previewSphere.appendChild(previewImage);

    previewVisual.appendChild(previewSphere);
    previewContainer.appendChild(previewVisual);

    // Preview info
    const previewInfo = document.createElement('div');
    previewInfo.innerHTML = `
      <div style="margin-bottom: 10px;">
        <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #00ffff;">Name your companion:</label>
        <input type="text" id="pet-name" placeholder="Enter name..." style="width: 100%; padding: 5px; background: rgba(0,0,0,0.5); border: 1px solid #00ffff; color: white; border-radius: 3px;">
      </div>
      <div style="font-size: 12px;">Cost: <span id="cost-display">100</span> points</div>
      <div style="font-size: 12px;">Tier: <span id="tier-display">COMMON</span></div>
      <div style="font-size: 12px;">NFT Price: <span id="nft-price-display">0.001</span> MATIC</div>
    `;
    previewContainer.appendChild(previewInfo);

    container.appendChild(previewContainer);

    // Features and traits section
    const featuresContainer = document.createElement('div');
    featuresContainer.style.marginBottom = '20px';
    featuresContainer.style.fontSize = '14px';
    featuresContainer.innerHTML = `
      <h3 style="color: #00ffff; margin-top: 0; font-size: 16px;">Features:</h3>
      <ul style="padding-left: 20px; margin-top: 5px;">
        <li>Loyal companion that follows and assists you</li>
        <li>Attacks nearby enemy specters automatically</li>
        <li>Five trainable traits: Attack, Defense, Speed, Intelligence, and Loyalty</li>
        <li>Mint as NFT to own permanently on the blockchain</li>
      </ul>
    `;
    featuresContainer.appendChild(document.createTextNode('\n      ')); // Add a little space
    container.appendChild(featuresContainer);

    // Buttons
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'buttons-container';
    buttonsContainer.style.display = 'flex';
    buttonsContainer.style.flexWrap = 'wrap';
    buttonsContainer.style.justifyContent = 'space-between';
    buttonsContainer.style.gap = '10px';

    // Purchase with points button
    const purchaseButton = document.createElement('button');
    purchaseButton.textContent = 'Purchase with Points';
    purchaseButton.style.padding = '10px 20px';
    purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
    purchaseButton.style.border = '1px solid #00ffff';
    purchaseButton.style.borderRadius = '5px';
    purchaseButton.style.color = 'white';
    purchaseButton.style.cursor = 'pointer';
    purchaseButton.style.transition = 'all 0.2s ease';
    purchaseButton.style.flexGrow = '1';

    purchaseButton.addEventListener('mouseover', () => {
      purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.4)';
    });

    purchaseButton.addEventListener('mouseout', () => {
      purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
    });

    purchaseButton.addEventListener('click', () => {
      this.purchasePetSpecter();
    });

    // Mint as NFT button removed - standard specters should not be mintable as NFTs

    // Generate from NFT button
    const generateFromNFTButton = document.createElement('button');
    generateFromNFTButton.textContent = 'Generate from Your NFT';
    generateFromNFTButton.style.padding = '10px 20px';
    generateFromNFTButton.style.backgroundColor = 'rgba(255, 165, 0, 0.2)';
    generateFromNFTButton.style.border = '1px solid #ffa500';
    generateFromNFTButton.style.borderRadius = '5px';
    generateFromNFTButton.style.color = 'white';
    generateFromNFTButton.style.cursor = 'pointer';
    generateFromNFTButton.style.transition = 'all 0.2s ease';
    generateFromNFTButton.style.flexGrow = '1';
    generateFromNFTButton.style.marginTop = '10px';
    generateFromNFTButton.style.width = '100%';

    generateFromNFTButton.addEventListener('mouseover', () => {
      generateFromNFTButton.style.backgroundColor = 'rgba(255, 165, 0, 0.4)';
    });

    generateFromNFTButton.addEventListener('mouseout', () => {
      generateFromNFTButton.style.backgroundColor = 'rgba(255, 165, 0, 0.2)';
    });

    generateFromNFTButton.addEventListener('click', () => {
      this.generatePetFromNFT();
    });

    // AI-Generated Pet button
    const generateAIPetButton = document.createElement('button');
    generateAIPetButton.textContent = 'Create AI-Generated Pet Specter';
    generateAIPetButton.style.padding = '10px 20px';
    generateAIPetButton.style.backgroundColor = 'rgba(138, 43, 226, 0.2)';
    generateAIPetButton.style.border = '1px solid #8a2be2';
    generateAIPetButton.style.borderRadius = '5px';
    generateAIPetButton.style.color = 'white';
    generateAIPetButton.style.cursor = 'pointer';
    generateAIPetButton.style.transition = 'all 0.2s ease';
    generateAIPetButton.style.flexGrow = '1';
    generateAIPetButton.style.marginTop = '10px';
    generateAIPetButton.style.width = '100%';

    generateAIPetButton.addEventListener('mouseover', () => {
      generateAIPetButton.style.backgroundColor = 'rgba(138, 43, 226, 0.4)';
    });

    generateAIPetButton.addEventListener('mouseout', () => {
      generateAIPetButton.style.backgroundColor = 'rgba(138, 43, 226, 0.2)';
    });

    generateAIPetButton.addEventListener('click', () => {
      this.generateAIPet();
    });

    // Cancel button
    const cancelButton = document.createElement('button');
    cancelButton.textContent = 'Cancel';
    cancelButton.style.padding = '10px 20px';
    cancelButton.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
    cancelButton.style.border = '1px solid #ff0000';
    cancelButton.style.borderRadius = '5px';
    cancelButton.style.color = 'white';
    cancelButton.style.cursor = 'pointer';
    cancelButton.style.transition = 'all 0.2s ease';
    cancelButton.style.flexGrow = '1';

    cancelButton.addEventListener('mouseover', () => {
      cancelButton.style.backgroundColor = 'rgba(255, 0, 0, 0.4)';
    });

    cancelButton.addEventListener('mouseout', () => {
      cancelButton.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
    });

    cancelButton.addEventListener('click', () => {
      this.close();
    });

    // First row of buttons
    const buttonRow1 = document.createElement('div');
    buttonRow1.style.display = 'flex';
    buttonRow1.style.gap = '10px';
    buttonRow1.style.width = '100%';

    buttonRow1.appendChild(purchaseButton);
    // Mint button removed
    buttonRow1.appendChild(cancelButton);

    buttonsContainer.appendChild(buttonRow1);
    buttonsContainer.appendChild(generateFromNFTButton);
    buttonsContainer.appendChild(generateAIPetButton);

    container.appendChild(buttonsContainer);
  }

  /**
   * Create equipment content
   */
  private createEquipmentContent(container: HTMLElement): void {
    // Make sure container is empty
    container.innerHTML = '';

    // Header
    const header = document.createElement('div');
    header.innerHTML = `
      <h3 style="color: #00ffff; margin-top: 0; font-size: 16px;">Pet Specter Equipment</h3>
      <p style="margin-bottom: 15px; font-size: 14px;">Enhance your pet's abilities with powerful equipment!</p>
    `;
    container.appendChild(header);

    // Create a scrollable container for equipment categories
    const scrollableContainer = document.createElement('div');
    scrollableContainer.style.maxHeight = 'calc(60vh - 200px)'; // Dynamic height based on viewport
    scrollableContainer.style.overflowY = 'auto'; // Enable vertical scrolling
    scrollableContainer.style.paddingRight = '5px'; // Add some padding for the scrollbar
    scrollableContainer.style.marginBottom = '20px'; // Add margin at the bottom

    // Custom scrollbar styling
    scrollableContainer.style.scrollbarWidth = 'thin'; // For Firefox
    scrollableContainer.style.scrollbarColor = '#00ffff #000000'; // For Firefox

    // Add custom scrollbar styles for webkit browsers (Chrome, Safari, Edge)
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      #equipment-content > div:nth-child(2)::-webkit-scrollbar {
        width: 8px;
      }
      #equipment-content > div:nth-child(2)::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
      }
      #equipment-content > div:nth-child(2)::-webkit-scrollbar-thumb {
        background: rgba(0, 255, 255, 0.5);
        border-radius: 4px;
      }
      #equipment-content > div:nth-child(2)::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 255, 255, 0.8);
      }
    `;
    document.head.appendChild(styleElement);

    container.appendChild(scrollableContainer);

    // Equipment categories
    const categories = ['weapon', 'armor', 'utility'];
    const categoryNames = {
      'weapon': 'Weapons',
      'armor': 'Armor',
      'utility': 'Utility Items'
    };

    // Create equipment grid
    categories.forEach(category => {
      const categoryContainer = document.createElement('div');
      categoryContainer.style.marginBottom = '20px';

      // Category header
      const categoryHeader = document.createElement('h4');
      categoryHeader.textContent = categoryNames[category as keyof typeof categoryNames];
      categoryHeader.style.color = '#00ffff';
      categoryHeader.style.fontSize = '14px';
      categoryHeader.style.marginBottom = '10px';
      categoryHeader.style.borderBottom = '1px solid rgba(0, 255, 255, 0.3)';
      categoryHeader.style.paddingBottom = '5px';
      categoryContainer.appendChild(categoryHeader);

      // Equipment grid
      const equipmentGrid = document.createElement('div');
      equipmentGrid.style.display = 'grid';
      equipmentGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
      equipmentGrid.style.gap = '10px';

      // Filter equipment by category
      const categoryEquipment = AVAILABLE_EQUIPMENT.filter(item => item.type === category);

      // Log for debugging
      console.log(`Creating equipment items for ${category}:`, categoryEquipment);

      // Create equipment items
      categoryEquipment.forEach(equipment => {
        const equipmentItem = document.createElement('div');
        equipmentItem.className = 'equipment-item';
        equipmentItem.style.background = 'rgba(0,0,0,0.3)';
        equipmentItem.style.padding = '10px';
        equipmentItem.style.borderRadius = '5px';
        equipmentItem.style.border = '1px solid #3399ff';
        equipmentItem.style.cursor = 'pointer';
        equipmentItem.style.transition = 'all 0.2s ease';

        // Check if already purchased
        const isPurchased = playerInventory.hasEquipment(equipment.id);

        // Equipment details
        const price = getEquipmentPrice(equipment);
        const rarityColor = equipment.rarity === 'common' ? '#aaaaaa' :
                           equipment.rarity === 'uncommon' ? '#00cc00' :
                           equipment.rarity === 'rare' ? '#0088ff' :
                           equipment.rarity === 'epic' ? '#aa00ff' : '#ff8800';

        // Create a unique ID for this equipment item
        const equipmentItemId = `equipment-item-${equipment.id}`;
        equipmentItem.id = equipmentItemId;

        equipmentItem.innerHTML = `
          <div style="color: ${rarityColor}; font-weight: bold; font-size: 14px;">${equipment.name}</div>
          <div style="color: #ccc; font-size: 12px;">${category.charAt(0).toUpperCase() + category.slice(1)}</div>
          ${equipment.stats.attackBonus ? `<div style="color: #aaa; font-size: 11px;">+${equipment.stats.attackBonus} Attack</div>` : ''}
          ${equipment.stats.defenseBonus ? `<div style="color: #aaa; font-size: 11px;">+${equipment.stats.defenseBonus} Defense</div>` : ''}
          ${equipment.stats.speedBonus ? `<div style="color: #aaa; font-size: 11px;">+${equipment.stats.speedBonus} Speed</div>` : ''}
          <div style="color: ${isPurchased ? '#00cc00' : '#00ffff'}; font-size: 11px; margin-top: 5px;">
            ${isPurchased ? 'Purchased' : `${price} points`}
          </div>
        `;

        // Add hover effect
        equipmentItem.addEventListener('mouseover', () => {
          equipmentItem.style.backgroundColor = 'rgba(0,80,120,0.4)';
        });

        equipmentItem.addEventListener('mouseout', () => {
          equipmentItem.style.backgroundColor = 'rgba(0,0,0,0.3)';
        });

        // Add click handler
        equipmentItem.addEventListener('click', () => {
          if (isPurchased) {
            this.showNotification('You already own this item!', 'success');
            AudioManager.playSoundEffect('uiClick');
            return;
          }

          // Select this equipment
          this.selectEquipment(equipment, price);
          AudioManager.playSoundEffect('uiClick');

          // Highlight selected item
          document.querySelectorAll('.equipment-item').forEach(item => {
            (item as HTMLElement).style.border = '1px solid #3399ff';
          });
          equipmentItem.style.border = '1px solid #00ffff';
        });

        equipmentGrid.appendChild(equipmentItem);
      });

      categoryContainer.appendChild(equipmentGrid);
      scrollableContainer.appendChild(categoryContainer); // Add to scrollable container instead of main container
    });

    // Equipment preview and purchase
    const purchaseContainer = document.createElement('div');
    purchaseContainer.style.marginTop = '20px';
    purchaseContainer.style.padding = '15px';
    purchaseContainer.style.backgroundColor = 'rgba(0,0,0,0.3)';
    purchaseContainer.style.borderRadius = '5px';

    // Preview content
    const previewContent = document.createElement('div');
    previewContent.id = 'equipment-preview';
    previewContent.innerHTML = `
      <div style="text-align: center; color: #aaaaaa; font-style: italic;">Select an item to purchase</div>
    `;
    purchaseContainer.appendChild(previewContent);

    // Purchase button
    const purchaseButton = document.createElement('button');
    purchaseButton.id = 'purchase-equipment-button';
    purchaseButton.textContent = 'Purchase Equipment';
    purchaseButton.style.padding = '10px 20px';
    purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
    purchaseButton.style.border = '1px solid #00ffff';
    purchaseButton.style.borderRadius = '5px';
    purchaseButton.style.color = 'white';
    purchaseButton.style.cursor = 'pointer';
    purchaseButton.style.transition = 'all 0.2s ease';
    purchaseButton.style.width = '100%';
    purchaseButton.style.marginTop = '15px';
    purchaseButton.style.display = 'none'; // Hide initially

    purchaseButton.addEventListener('mouseover', () => {
      purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.4)';
    });

    purchaseButton.addEventListener('mouseout', () => {
      purchaseButton.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
    });

    purchaseButton.addEventListener('click', () => {
      this.purchaseEquipment();
    });

    purchaseContainer.appendChild(purchaseButton);
    container.appendChild(purchaseContainer);
  }

  /**
   * Select equipment for purchase
   */
  private selectEquipment(equipment: SpecterEquipment, price: number): void {
    this.selectedEquipment = equipment;
    this.equipmentCost = price;

    // Update preview
    const previewElement = document.getElementById('equipment-preview');
    if (previewElement) {
      const rarityColor = equipment.rarity === 'common' ? '#aaaaaa' :
                         equipment.rarity === 'uncommon' ? '#00cc00' :
                         equipment.rarity === 'rare' ? '#0088ff' :
                         equipment.rarity === 'epic' ? '#aa00ff' : '#ff8800';

      previewElement.innerHTML = `
        <div style="margin-bottom: 10px;">
          <div style="font-size: 16px; color: ${rarityColor}; font-weight: bold;">${equipment.name}</div>
          <div style="font-size: 12px; color: #aaaaaa;">${equipment.rarity.charAt(0).toUpperCase() + equipment.rarity.slice(1)} ${equipment.type.charAt(0).toUpperCase() + equipment.type.slice(1)}</div>
        </div>
        <div style="margin-bottom: 15px;">
          ${equipment.stats.attackBonus ? `<div style="font-size: 13px;">+${equipment.stats.attackBonus} Attack</div>` : ''}
          ${equipment.stats.defenseBonus ? `<div style="font-size: 13px;">+${equipment.stats.defenseBonus} Defense</div>` : ''}
          ${equipment.stats.speedBonus ? `<div style="font-size: 13px;">+${equipment.stats.speedBonus} Speed</div>` : ''}
        </div>
        <div style="font-size: 14px; margin-top: 10px;">
          Price: <span style="color: #00ffff;">${price} points</span>
        </div>
        <div style="font-size: 12px; color: ${this.player.score >= price ? '#00cc00' : '#ff0000'}; margin-top: 5px;">
          Your points: ${this.player.score}
        </div>
      `;
    }

    // Show purchase button
    const purchaseButton = document.getElementById('purchase-equipment-button');
    if (purchaseButton) {
      purchaseButton.style.display = 'block';

      // Disable if not enough points
      if (this.player.score < price) {
        purchaseButton.style.opacity = '0.5';
        purchaseButton.style.cursor = 'not-allowed';
      } else {
        purchaseButton.style.opacity = '1';
        purchaseButton.style.cursor = 'pointer';
      }
    }
  }

  /**
   * Purchase selected equipment
   */
  private purchaseEquipment(): void {
    if (!this.selectedEquipment) {
      this.showNotification('Please select equipment to purchase', 'error');
      return;
    }

    // Check if player has enough points
    if (this.player.score < this.equipmentCost) {
      this.showNotification('Not enough points!', 'error');
      AudioManager.playSoundEffect('uiError');
      return;
    }

    // Add to inventory
    playerInventory.addEquipment(this.selectedEquipment);

    // Deduct points
    this.player.score -= this.equipmentCost;

    // Dispatch score update event
    const scoreEvent = new CustomEvent('scoreUpdate', {
      detail: {
        score: this.player.score
      }
    });
    document.dispatchEvent(scoreEvent);

    // Show success notification
    this.showNotification(`Purchased ${this.selectedEquipment.name}!`, 'success');

    // Play purchase sound
    AudioManager.playSoundEffect('powerup');

    // Reset selection
    this.selectedEquipment = null;
    this.equipmentCost = 0;

    // Refresh equipment display
    this.refreshEquipmentDisplay();
  }

  /**
   * Refresh equipment display after purchase
   */
  private refreshEquipmentDisplay(): void {
    // Reset preview
    const previewElement = document.getElementById('equipment-preview');
    if (previewElement) {
      previewElement.innerHTML = `
        <div style="text-align: center; color: #aaaaaa; font-style: italic;">Select an item to purchase</div>
      `;
    }

    // Hide purchase button
    const purchaseButton = document.getElementById('purchase-equipment-button');
    if (purchaseButton) {
      purchaseButton.style.display = 'none';
    }

    // Update equipment items to show purchased status
    document.querySelectorAll('.equipment-item').forEach(item => {
      const equipmentId = item.querySelector('div')?.textContent;
      if (equipmentId) {
        const equipment = AVAILABLE_EQUIPMENT.find(e => e.name === equipmentId);
        if (equipment && playerInventory.hasEquipment(equipment.id)) {
          const priceElement = item.querySelector('div:last-child');
          if (priceElement) {
            priceElement.textContent = 'Purchased';
            priceElement.setAttribute('style', 'color: #00cc00; font-size: 11px; margin-top: 5px;');
          }
        }
      }
    });
  }

  /**
   * Update the visual preview
   */
  private updatePreview(color: string): void {
    const previewSphere = document.querySelector('#preview-visual div') as HTMLElement;
    if (previewSphere) {
      // Update the box shadow color
      previewSphere.style.boxShadow = `0 0 15px ${color}`;

      // Update the image source to match the selected type
      const previewImage = previewSphere.querySelector('img') as HTMLImageElement;
      if (previewImage && this.selectedType && this.selectedType.texture) {
        previewImage.src = this.selectedType.texture;
      }
    }
  }

  /**
   * Show the dialog
   */
  open(): void {
    if (this.isOpen) return;

    this.container.style.display = 'block';
    this.isOpen = true;

    // Hide game UI while dialog is open
    const gameUI = document.querySelector('.game-ui');
    if (gameUI) {
      (gameUI as HTMLElement).style.display = 'none';
    }

    // Reset to pets tab by default
    this.switchTab('pets');

    // Play open sound
    AudioManager.playSoundEffect('uiOpen');
  }

  /**
   * Close the dialog
   */
  close(): void {
    if (!this.isOpen) return;

    this.container.style.display = 'none';
    this.isOpen = false;

    // Show game UI again
    const gameUI = document.querySelector('.game-ui');
    if (gameUI) {
      (gameUI as HTMLElement).style.display = 'block';
    }

    // Play close sound
    AudioManager.playSoundEffect('uiClose');
  }

  /**
   * Purchase a pet specter with points
   */
  private purchasePetSpecter(): void {
    // Check if player has enough points
    if (!this.player || typeof this.player.score === 'undefined' || this.player.score < this.cost) {
      // Debug info to help diagnose the issue
      console.log("Purchase attempt:", {
        playerExists: !!this.player,
        playerScore: this.player ? this.player.score : 'no player',
        cost: this.cost,
        enoughPoints: this.player ? this.player.score >= this.cost : false
      });

      this.showNotification('Not enough points!', 'error');
      AudioManager.playSoundEffect('uiError');
      return;
    }

    // Get pet name, use default if empty
    const petName = this.customName.trim() || `${this.selectedType.name} Companion`;

    // Dispatch event to create pet specter
    // Include the texture URL for Demo NFT pets
    const event = new CustomEvent('purchasePetSpecter', {
      detail: {
        type: this.selectedType,
        name: petName,
        cost: this.cost,
        customImageUrl: this.selectedType.texture // Pass the texture URL as customImageUrl
      }
    });

    document.dispatchEvent(event);

    // Deduct points
    this.player.score -= this.cost;

    // Dispatch score update event
    const scoreEvent = new CustomEvent('scoreUpdate', {
      detail: {
        score: this.player.score
      }
    });
    document.dispatchEvent(scoreEvent);

    // Show success notification
    this.showNotification(`${petName} has joined your team!`, 'success');

    // Play purchase sound
    AudioManager.playSoundEffect('powerup');

    // Close dialog
    this.close();
  }

  // mintPetSpecterAsNFT method removed - standard specters should not be mintable as NFTs

  /**
   * Generate pet specter from player's existing NFT
   */
  private generatePetFromNFT(): void {
    // Dispatch event to open NFT-based pet generation dialog
    const event = new CustomEvent('generatePetFromNFT', {});
    document.dispatchEvent(event);

    // Simply hide this dialog without closing it or changing its state
    // This ensures the game stays paused
    this.container.style.display = 'none';

    // We're keeping isOpen as true to prevent any game resumption
    // The NFT-based pet generation dialog will handle its own state
  }

  /**
   * Generate AI pet specter
   */
  private generateAIPet(): void {
    // Dispatch event to open AI pet generation dialog
    const event = new CustomEvent('generateAIPet', {});
    document.dispatchEvent(event);

    // Simply hide this dialog without closing it or changing its state
    // This ensures the game stays paused
    this.container.style.display = 'none';

    // We're keeping isOpen as true to prevent any game resumption
    // The AI pet generation dialog will handle its own state
  }

  /**
   * Show notification
   */
  private showNotification(message: string, type: 'success' | 'error'): void {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.padding = '10px 20px';
    notification.style.borderRadius = '5px';
    notification.style.color = 'white';
    notification.style.fontFamily = 'Arial, sans-serif';
    notification.style.zIndex = '1001';
    notification.style.opacity = '0';
    notification.style.transition = 'opacity 0.3s ease';

    if (type === 'success') {
      notification.style.backgroundColor = 'rgba(0, 255, 0, 0.7)';
    } else {
      notification.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
    }

    notification.textContent = message;

    document.body.appendChild(notification);

    // Fade in
    setTimeout(() => {
      notification.style.opacity = '1';
    }, 10);

    // Remove after delay
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}
