import Player from '../entities/Player';
import { PetSpecter } from '../entities/PetSpecter';
import Specter from '../entities/Specter';
import { SpecterType, SpecterTypeEnum } from '../types';
import { audioManager as AudioManager } from '../audio/AudioManager';

/**
 * UI dialog for MerchGenie kiosk
 * Allows players to purchase pet specters with points
 */
export class MerchGenieDialog {
  private player: Player;
  private container: HTMLElement;
  public isOpen: boolean = false;
  private selectedType: SpecterType = {
    id: 0,
    name: 'WISP',
    color: '#3399ff',
    points: 100,
    texture: 'assets/textures/wisp.png'
  };
  private customName: string = '';
  private cost: number = 100;

  constructor(player: Player) {
    this.player = player;

    // Create container element
    this.container = document.createElement('div');
    this.container.className = 'merch-genie-dialog';
    this.container.style.display = 'none';
    this.container.style.position = 'absolute';
    this.container.style.top = '50%';
    this.container.style.left = '50%';
    this.container.style.transform = 'translate(-50%, -50%)';
    this.container.style.backgroundColor = 'rgba(0, 40, 80, 0.9)';
    this.container.style.border = '2px solid #00ffff';
    this.container.style.borderRadius = '8px';
    this.container.style.padding = '20px';
    this.container.style.width = '400px';
    this.container.style.color = 'white';
    this.container.style.fontFamily = 'Arial, sans-serif';
    this.container.style.zIndex = '1000';
    this.container.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.5)';

    // Add content
    this.createDialogContent();

    // Add to document
    document.body.appendChild(this.container);
  }

  /**
   * Create dialog content
   */
  private createDialogContent(): void {
    // Header
    const header = document.createElement('div');
    header.className = 'dialog-header';
    header.innerHTML = `
      <h2 style="color: #00ffff; text-align: center; margin-top: 0; text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);">MerchGenie</h2>
      <p style="text-align: center; margin-bottom: 20px;">Summon your own spectral companion!</p>
    `;
    this.container.appendChild(header);

    // Specter selection
    const selectionContainer = document.createElement('div');
    selectionContainer.className = 'selection-container';
    selectionContainer.style.display = 'flex';
    selectionContainer.style.justifyContent = 'space-between';
    selectionContainer.style.marginBottom = '20px';

    const specterTypes = [
      {
        type: { id: 0, name: 'WISP', color: '#3399ff', points: 100, texture: 'assets/textures/wisp.png' },
        name: 'Wisp',
        color: '#3399ff',
        cost: 100
      },
      {
        type: { id: 1, name: 'PHANTOM', color: '#33ff66', points: 150, texture: 'assets/textures/phantom.png' },
        name: 'Phantom',
        color: '#33ff66',
        cost: 150
      },
      {
        type: { id: 2, name: 'POLTERGEIST', color: '#ff9933', points: 200, texture: 'assets/textures/poltergeist.png' },
        name: 'Poltergeist',
        color: '#ff9933',
        cost: 200
      },
      {
        type: { id: 3, name: 'WRAITH', color: '#9933ff', points: 250, texture: 'assets/textures/wraith.png' },
        name: 'Wraith',
        color: '#9933ff',
        cost: 250
      },
      {
        type: { id: 4, name: 'BANSHEE', color: '#ff3366', points: 300, texture: 'assets/textures/banshee.png' },
        name: 'Banshee',
        color: '#ff3366',
        cost: 300
      }
    ];

    specterTypes.forEach(specter => {
      const option = document.createElement('div');
      option.className = 'specter-option';
      option.setAttribute('data-type', specter.type.name);
      option.style.width = '60px';
      option.style.textAlign = 'center';
      option.style.cursor = 'pointer';
      option.style.transition = 'all 0.2s ease';
      option.style.opacity = '0.7';
      option.style.padding = '10px';
      option.style.borderRadius = '5px';

      option.innerHTML = `
        <div style="width: 40px; height: 40px; margin: 0 auto; background-color: ${specter.color}; border-radius: 50%; box-shadow: 0 0 10px ${specter.color};"></div>
        <div style="margin-top: 5px; font-size: 12px;">${specter.name}</div>
        <div style="font-size: 10px;">${specter.cost} pts</div>
      `;

      option.addEventListener('click', () => {
        // Deselect all
        document.querySelectorAll('.specter-option').forEach(el => {
          (el as HTMLElement).style.backgroundColor = 'transparent';
          (el as HTMLElement).style.opacity = '0.7';
        });

        // Select this one
        option.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
        option.style.opacity = '1';

        // Update selected type and cost
        this.selectedType = specter.type;
        this.cost = specter.cost;

        // Update cost display
        const costDisplay = document.getElementById('cost-display');
        if (costDisplay) {
          costDisplay.textContent = `${specter.cost}`;
        }

        // Update preview
        this.updatePreview(specter.color);

        // Play selection sound
        AudioManager.playSoundEffect('uiClick');
      });

      selectionContainer.appendChild(option);

      // Select first one by default
      if (specter.type.name === 'WISP') {
        option.style.backgroundColor = 'rgba(0, 255, 255, 0.2)';
        option.style.opacity = '1';
        this.updatePreview(specter.color);
      }
    });

    this.container.appendChild(selectionContainer);

    // Preview section
    const previewContainer = document.createElement('div');
    previewContainer.className = 'preview-container';
    previewContainer.style.display = 'flex';
    previewContainer.style.alignItems = 'center';
    previewContainer.style.marginBottom = '20px';
    previewContainer.style.padding = '10px';
    previewContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    previewContainer.style.borderRadius = '5px';

    // Preview visual
    const previewVisual = document.createElement('div');
    previewVisual.id = 'preview-visual';
    previewVisual.style.width = '80px';
    previewVisual.style.height = '80px';
    previewVisual.style.marginRight = '20px';
    previewVisual.style.display = 'flex';
    previewVisual.style.alignItems = 'center';
    previewVisual.style.justifyContent = 'center';

    // Add animated preview content
    const previewSphere = document.createElement('div');
    previewSphere.style.width = '40px';
    previewSphere.style.height = '40px';
    previewSphere.style.borderRadius = '50%';
    previewSphere.style.backgroundColor = '#3399ff';
    previewSphere.style.boxShadow = '0 0 15px #3399ff';
    previewSphere.style.animation = 'pulse 2s infinite';

    // Add animation keyframes
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes pulse {
        0% { transform: scale(1); opacity: 0.8; }
        50% { transform: scale(1.1); opacity: 1; }
        100% { transform: scale(1); opacity: 0.8; }
      }

      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
      }

      .merch-genie-dialog button:hover {
        background-color: #00cccc;
        transform: translateY(-2px);
      }
    `;
    document.head.appendChild(style);

    previewVisual.appendChild(previewSphere);
    previewContainer.appendChild(previewVisual);

    // Preview info
    const previewInfo = document.createElement('div');
    previewInfo.innerHTML = `
      <div style="margin-bottom: 10px;">
        <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #00ffff;">Name your companion:</label>
        <input type="text" id="pet-name" placeholder="Enter name..." style="width: 100%; padding: 5px; background: rgba(0,0,0,0.5); border: 1px solid #00ffff; color: white; border-radius: 3px;">
      </div>
      <div style="font-size: 12px;">Cost: <span id="cost-display">100</span> points</div>
    `;
    previewContainer.appendChild(previewInfo);

    this.container.appendChild(previewContainer);

    // Add name input handler
    setTimeout(() => {
      const nameInput = document.getElementById('pet-name') as HTMLInputElement;
      if (nameInput) {
        nameInput.addEventListener('input', (e) => {
          this.customName = (e.target as HTMLInputElement).value;
        });
      }
    }, 0);

    // Features and traits section
    const featuresContainer = document.createElement('div');
    featuresContainer.style.marginBottom = '20px';
    featuresContainer.style.fontSize = '14px';
    featuresContainer.innerHTML = `
      <h3 style="color: #00ffff; margin-top: 0; font-size: 16px;">Features:</h3>
      <ul style="padding-left: 20px; margin-top: 5px;">
        <li>Loyal companion that follows and assists you</li>
        <li>Attacks nearby enemy specters automatically</li>
        <li>Five trainable traits: Attack, Defense, Speed, Intelligence, and Loyalty</li>
      </ul>
    `;
    featuresContainer.appendChild(document.createTextNode('\n      ')); // Add a little space
    this.container.appendChild(featuresContainer);

    // Equipment section
    const equipmentContainer = document.createElement('div');
    equipmentContainer.style.marginBottom = '20px';
    equipmentContainer.innerHTML = `
      <h3 style="color: #00ffff; margin-top: 0; font-size: 16px;">Available Equipment:</h3>
      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-top: 10px;">
        <div class="equipment-item" style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 5px; border: 1px solid #3399ff;">
          <div style="color: #3399ff; font-weight: bold; font-size: 14px;">Ecto Blaster</div>
          <div style="color: #ccc; font-size: 12px;">Weapon</div>
          <div style="color: #aaa; font-size: 11px;">+5 Attack</div>
          <div style="color: #00ffff; font-size: 11px; margin-top: 5px;">250 points</div>
        </div>
        <div class="equipment-item" style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 5px; border: 1px solid #33ff66;">
          <div style="color: #33ff66; font-weight: bold; font-size: 14px;">Phantom Shield</div>
          <div style="color: #ccc; font-size: 12px;">Armor</div>
          <div style="color: #aaa; font-size: 11px;">+3 Defense</div>
          <div style="color: #00ffff; font-size: 11px; margin-top: 5px;">200 points</div>
        </div>
        <div class="equipment-item" style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 5px; border: 1px solid #ff9933;">
          <div style="color: #ff9933; font-weight: bold; font-size: 14px;">Speed Wisp</div>
          <div style="color: #ccc; font-size: 12px;">Utility</div>
          <div style="color: #aaa; font-size: 11px;">+2 Speed</div>
          <div style="color: #00ffff; font-size: 11px; margin-top: 5px;">150 points</div>
        </div>
      </div>
    `;

    // Add click handler to equipment items
    setTimeout(() => {
      const equipmentItems = equipmentContainer.querySelectorAll('.equipment-item');
      equipmentItems.forEach(item => {
        item.addEventListener('click', () => {
          AudioManager.playSoundEffect('uiClick');
          this.showNotification('Purchase equipment from Pet Specter menu', 'success');
        });
      });
    }, 0);

    this.container.appendChild(equipmentContainer);

    // Action buttons
    const actionContainer = document.createElement('div');
    actionContainer.style.display = 'flex';
    actionContainer.style.justifyContent = 'space-between';

    // Purchase button
    const purchaseButton = document.createElement('button');
    purchaseButton.textContent = 'Summon Companion';
    purchaseButton.style.padding = '10px 20px';
    purchaseButton.style.backgroundColor = '#00aaaa';
    purchaseButton.style.color = 'white';
    purchaseButton.style.border = 'none';
    purchaseButton.style.borderRadius = '5px';
    purchaseButton.style.cursor = 'pointer';
    purchaseButton.style.transition = 'all 0.2s ease';
    purchaseButton.style.fontWeight = 'bold';

    purchaseButton.addEventListener('click', () => {
      this.purchasePetSpecter();
    });

    // Cancel button
    const cancelButton = document.createElement('button');
    cancelButton.textContent = 'Cancel';
    cancelButton.style.padding = '10px 20px';
    cancelButton.style.backgroundColor = 'rgba(150, 150, 150, 0.3)';
    cancelButton.style.color = 'white';
    cancelButton.style.border = 'none';
    cancelButton.style.borderRadius = '5px';
    cancelButton.style.cursor = 'pointer';
    cancelButton.style.transition = 'all 0.2s ease';

    cancelButton.addEventListener('click', () => {
      this.close();
      AudioManager.playSoundEffect('uiClick');
    });

    actionContainer.appendChild(cancelButton);
    actionContainer.appendChild(purchaseButton);

    this.container.appendChild(actionContainer);
  }

  /**
   * Update the visual preview
   */
  private updatePreview(color: string): void {
    const previewSphere = document.querySelector('#preview-visual div') as HTMLElement;
    if (previewSphere) {
      previewSphere.style.backgroundColor = color;
      previewSphere.style.boxShadow = `0 0 15px ${color}`;
    }
  }

  /**
   * Show the dialog
   */
  open(): void {
    if (this.isOpen) return;

    this.container.style.display = 'block';
    this.isOpen = true;

    // Hide game UI while dialog is open
    const gameUI = document.querySelector('.game-ui');
    if (gameUI) {
      (gameUI as HTMLElement).style.display = 'none';
    }

    // Play open sound
    AudioManager.playSoundEffect('uiOpen');
  }

  /**
   * Close the dialog
   */
  close(): void {
    if (!this.isOpen) return;

    this.container.style.display = 'none';
    this.isOpen = false;

    // Show game UI again
    const gameUI = document.querySelector('.game-ui');
    if (gameUI) {
      (gameUI as HTMLElement).style.display = 'block';
    }

    // Play close sound
    AudioManager.playSoundEffect('uiClose');
  }

  /**
   * Toggle dialog visibility
   */
  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  /**
   * Purchase a pet specter
   */
  private purchasePetSpecter(): void {
    // Check if player has enough points
    if (!this.player || typeof this.player.score === 'undefined' || this.player.score < this.cost) {
      // Debug info to help diagnose the issue
      console.log("Purchase attempt:", {
        playerExists: !!this.player,
        playerScore: this.player ? this.player.score : 'no player',
        cost: this.cost,
        enoughPoints: this.player ? this.player.score >= this.cost : false
      });

      this.showNotification('Not enough points!', 'error');
      AudioManager.playSoundEffect('uiError');
      return;
    }

    // Get pet name, use default if empty
    const petName = this.customName.trim() || `${this.selectedType.name} Companion`;

    // Dispatch event to create pet specter
    // Include the texture URL for the pet
    const event = new CustomEvent('purchasePetSpecter', {
      detail: {
        type: this.selectedType,
        name: petName,
        cost: this.cost,
        customImageUrl: this.selectedType.texture // Pass the texture URL as customImageUrl
      }
    });

    document.dispatchEvent(event);

    // Deduct points
    this.player.score -= this.cost;

    // Dispatch score update event
    const scoreEvent = new CustomEvent('scoreUpdate', {
      detail: {
        score: this.player.score
      }
    });
    document.dispatchEvent(scoreEvent);

    // Show success notification
    this.showNotification(`${petName} has joined your team!`, 'success');

    // Play purchase sound
    AudioManager.playSoundEffect('powerup');

    // Close dialog
    this.close();
  }

  destroy(): void {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }

  /**
   * Show notification
   */
  private showNotification(message: string, type: 'success' | 'error'): void {
    const notification = document.createElement('div');
    notification.className = 'game-notification';
    notification.textContent = message;
    notification.style.position = 'absolute';
    notification.style.top = '100px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.padding = '10px 20px';
    notification.style.borderRadius = '5px';
    notification.style.color = 'white';
    notification.style.fontWeight = 'bold';
    notification.style.zIndex = '1001';
    notification.style.opacity = '0';
    notification.style.transition = 'opacity 0.3s ease';

    if (type === 'success') {
      notification.style.backgroundColor = 'rgba(0, 180, 0, 0.8)';
    } else {
      notification.style.backgroundColor = 'rgba(180, 0, 0, 0.8)';
    }

    document.body.appendChild(notification);

    // Fade in
    setTimeout(() => {
      notification.style.opacity = '1';
    }, 10);

    // Remove after a few seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}
