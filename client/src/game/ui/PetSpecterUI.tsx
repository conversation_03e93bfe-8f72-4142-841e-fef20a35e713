import React, { useState, useEffect } from 'react';
import { PetSpecter } from '../entities/PetSpecter';
import { SpecterTrait, SpecterTraitType, SpecterEquipment } from '../entities/PetSpecter';
import { nanoid } from 'nanoid';
import { playerInventory } from '../inventory/PlayerInventory';
import { AVAILABLE_EQUIPMENT, getEquipmentColor as getEquipmentColorUtil } from '../data/EquipmentData';
import TrainingFeeDialog from '@/components/TrainingFeeDialog';
import { useWeb3 } from '@/contexts/Web3Context';

interface PetSpecterUIProps {
  pets: PetSpecter[];
  isOpen: boolean;
  onClose: () => void;
}

const PetSpecterUI: React.FC<PetSpecterUIProps> = ({ pets, isOpen, onClose }) => {
  const { isConnected } = useWeb3();
  const [selectedPetIndex, setSelectedPetIndex] = useState(0);
  const [trainingMenuOpen, setTrainingMenuOpen] = useState(false);
  const [selectedTrait, setSelectedTrait] = useState<SpecterTraitType | null>(null);
  const [equipMenuOpen, setEquipMenuOpen] = useState(false);
  const [selectedEquipmentType, setSelectedEquipmentType] = useState<'weapon' | 'armor' | 'utility' | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<SpecterEquipment | null>(null);
  const [availableEquipment, setAvailableEquipment] = useState<SpecterEquipment[]>([]);
  const [trainingFeeDialogOpen, setTrainingFeeDialogOpen] = useState(false);

  // State to track deployed vs. recalled pets
  const [deployedPets, setDeployedPets] = useState<string[]>([]);
  const [recalledPets, setRecalledPets] = useState<string[]>([]);

  // Initialize the deployed/recalled states when component mounts
  useEffect(() => {
    if (pets.length > 0) {
      // Assume all pets are initially deployed
      setDeployedPets(pets.map(pet => pet.id));
      setRecalledPets([]);
    }
  }, [pets]);

  // Listen for inventory changes
  useEffect(() => {
    const handleInventoryChange = () => {
      if (selectedEquipmentType) {
        updateAvailableEquipment(selectedEquipmentType);
      }
    };

    playerInventory.addInventoryChangeListener(handleInventoryChange);

    return () => {
      playerInventory.removeInventoryChangeListener(handleInventoryChange);
    };
  }, [selectedEquipmentType]);

  if (!isOpen || pets.length === 0) return null;

  const selectedPet = pets[selectedPetIndex];

  // Helper to get trait name with proper capitalization
  const formatTraitName = (traitType: SpecterTraitType): string => {
    return traitType.charAt(0).toUpperCase() + traitType.slice(1);
  };

  // Helper to format equipment name with color based on rarity
  const getEquipmentColor = (rarity: string): string => {
    switch (rarity) {
      case 'common': return 'text-gray-400';
      case 'uncommon': return 'text-green-400';
      case 'rare': return 'text-blue-400';
      case 'epic': return 'text-purple-400';
      case 'legendary': return 'text-yellow-400';
      default: return 'text-white';
    }
  };

  // Update available equipment when type changes
  const updateAvailableEquipment = (type: 'weapon' | 'armor' | 'utility') => {
    const ownedEquipment = playerInventory.getEquipmentByType(type);
    setAvailableEquipment(ownedEquipment);
  };

  // Helper to get behavior state color
  const getBehaviorStateColor = (state: string): string => {
    switch (state) {
      case 'follow': return 'bg-blue-600';
      case 'attack': return 'bg-red-600';
      case 'idle': return 'bg-gray-600';
      default: return 'bg-blue-600';
    }
  };

  // Change pet behavior state
  const changeBehaviorState = (state: 'follow' | 'attack' | 'idle') => {
    selectedPet.behaviorState = state;
    setSelectedPetIndex(selectedPetIndex); // Force re-render

    // Create and dispatch custom event to notify the engine about behavior change
    const event = new CustomEvent('petBehaviorChange', {
      detail: {
        petId: selectedPet.id,
        newState: state
      }
    });
    document.dispatchEvent(event);

    //console.log(`Changed ${selectedPet.name}'s behavior to ${state}`);
  };

  // Training handler
  const handleTraining = (traitType: SpecterTraitType) => {
    // Give pet XP in the selected trait
    const xpAmount = 20; // Base XP amount per training session
    selectedPet.gainTraitXP(traitType, xpAmount);

    // Close training menu
    setTrainingMenuOpen(false);
    setSelectedTrait(null);

    // Force re-render
    setSelectedPetIndex(selectedPetIndex);

    //console.log(`Trained ${selectedPet.name}'s ${traitType} trait (+${xpAmount} XP)`);
  };

  // Premium training handler (with MATIC fee)
  const handlePremiumTraining = (traitType: SpecterTraitType) => {
    // Open the training fee dialog
    setSelectedTrait(traitType);
    setTrainingFeeDialogOpen(true);

    // Close the training menu
    setTrainingMenuOpen(false);
  };

  // Handle successful premium training payment
  const handleTrainingPaymentSuccess = () => {
    if (selectedTrait) {
      // Give pet 3x XP for premium training
      const xpAmount = 60; // 3x the base XP amount
      selectedPet.gainTraitXP(selectedTrait, xpAmount);

      // Reset selected trait
      setSelectedTrait(null);

      // Force re-render
      setSelectedPetIndex(selectedPetIndex);
    }
  };

  // Equipment handler
  const handleEquip = (equipment: SpecterEquipment) => {
    if (!selectedEquipmentType) return;

    // Equip the item to the pet
    selectedPet.equipItem(equipment);

    // Close equipment menu
    setEquipMenuOpen(false);
    setSelectedEquipmentType(null);
    setSelectedEquipment(null);

    // Force re-render
    setSelectedPetIndex(selectedPetIndex);

    //console.log(`Equipped ${equipment.name} to ${selectedPet.name}`);
  };

  // Handle equipment type selection
  const handleEquipmentTypeSelect = (type: 'weapon' | 'armor' | 'utility') => {
    setSelectedEquipmentType(type);
    updateAvailableEquipment(type);
  };

  // Handle pet recall
  const handleRecallPet = (petId: string) => {
    // Move pet from deployed to recalled
    setDeployedPets(prev => prev.filter(id => id !== petId));
    setRecalledPets(prev => [...prev, petId]);

    // Create and dispatch custom event to notify the engine
    const event = new CustomEvent('petRecall', {
      detail: {
        petId
      }
    });
    document.dispatchEvent(event);
  };

  // Handle pet deploy
  const handleDeployPet = (petId: string) => {
    // Check if we already have 2 deployed pets
    if (deployedPets.length >= 2) {
      // Show error message - cannot deploy more than 2 pets
      alert('You can only have 2 pet specters deployed at a time.');
      return;
    }

    // Move pet from recalled to deployed
    setRecalledPets(prev => prev.filter(id => id !== petId));
    setDeployedPets(prev => [...prev, petId]);

    // Create and dispatch custom event to notify the engine
    const event = new CustomEvent('petDeploy', {
      detail: {
        petId
      }
    });
    document.dispatchEvent(event);
  };

  // Check if pet is deployed
  const isPetDeployed = (petId: string) => {
    return deployedPets.includes(petId);
  };

  // Render training menu
  const renderTrainingMenu = () => {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div className="bg-gray-900 border-2 border-purple-500 rounded-lg w-[400px] p-5">
          <h3 className="text-lg text-white font-bold mb-4">Train {selectedPet.name}</h3>

          <div className="space-y-4">
            <p className="text-gray-300 text-sm">Select a trait to train:</p>

            {Object.values(SpecterTraitType).map((trait) => {
              const currentTrait = selectedPet.traits.find(t => t.type === trait);
              return (
                <div
                  key={trait}
                  className={`p-3 rounded cursor-pointer transition-colors ${
                    selectedTrait === trait
                      ? 'bg-purple-900 border border-purple-500'
                      : 'bg-gray-800 hover:bg-gray-700'
                  }`}
                  onClick={() => setSelectedTrait(trait)}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-white">{formatTraitName(trait)}</span>
                    <span className="text-purple-300">Level {currentTrait?.level || 1}</span>
                  </div>
                  {currentTrait && (
                    <div className="mt-1 h-2 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-purple-500"
                        style={{ width: `${(currentTrait.xp / currentTrait.xpToNextLevel) * 100}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex justify-between mt-6">
            <button
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded"
              onClick={() => {
                setTrainingMenuOpen(false);
                setSelectedTrait(null);
              }}
            >
              Cancel
            </button>

            <div className="flex space-x-2">
              <button
                className={`px-4 py-2 bg-purple-600 text-white rounded ${
                  selectedTrait ? 'hover:bg-purple-700' : 'opacity-50 cursor-not-allowed'
                }`}
                onClick={() => selectedTrait && handleTraining(selectedTrait)}
                disabled={!selectedTrait}
              >
                Train
              </button>

              <button
                className={`px-4 py-2 bg-blue-600 text-white rounded flex items-center ${
                  selectedTrait && isConnected ? 'hover:bg-blue-700' : 'opacity-50 cursor-not-allowed'
                }`}
                onClick={() => selectedTrait && handlePremiumTraining(selectedTrait)}
                disabled={!selectedTrait || !isConnected}
                title={!isConnected ? "Connect wallet to use premium training" : ""}
              >
                <span className="mr-1">💎</span> Premium
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render equipment menu
  const renderEquipmentMenu = () => {

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div className="bg-gray-900 border-2 border-green-500 rounded-lg w-[400px] p-5">
          <h3 className="text-lg text-white font-bold mb-4">Equip {selectedPet.name}</h3>

          {!selectedEquipmentType ? (
            // Equipment type selection
            <div className="space-y-4">
              <p className="text-gray-300 text-sm">Select equipment type:</p>

              <div
                className="p-3 rounded cursor-pointer transition-colors bg-gray-800 hover:bg-gray-700"
                onClick={() => handleEquipmentTypeSelect('weapon')}
              >
                <div className="flex justify-between items-center">
                  <span className="text-white">Weapon</span>
                  <span className="text-xs text-gray-400">
                    {selectedPet.equipment.weapon ? 'Equipped' : 'Empty'}
                  </span>
                </div>
              </div>

              <div
                className="p-3 rounded cursor-pointer transition-colors bg-gray-800 hover:bg-gray-700"
                onClick={() => handleEquipmentTypeSelect('armor')}
              >
                <div className="flex justify-between items-center">
                  <span className="text-white">Armor</span>
                  <span className="text-xs text-gray-400">
                    {selectedPet.equipment.armor ? 'Equipped' : 'Empty'}
                  </span>
                </div>
              </div>

              <div
                className="p-3 rounded cursor-pointer transition-colors bg-gray-800 hover:bg-gray-700"
                onClick={() => handleEquipmentTypeSelect('utility')}
              >
                <div className="flex justify-between items-center">
                  <span className="text-white">Utility</span>
                  <span className="text-xs text-gray-400">
                    {selectedPet.equipment.utility ? 'Equipped' : 'Empty'}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            // Equipment selection
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <p className="text-gray-300 text-sm">Select {selectedEquipmentType}:</p>
                <button
                  className="text-xs text-gray-400 hover:text-gray-300"
                  onClick={() => setSelectedEquipmentType(null)}
                >
                  ← Back
                </button>
              </div>

              {availableEquipment.length > 0 ? (
                <>
                  {availableEquipment.map((equipment) => (
                    <div
                      key={equipment.id}
                      className={`p-3 rounded cursor-pointer transition-colors ${
                        selectedEquipment?.id === equipment.id
                          ? 'bg-green-900 border border-green-500'
                          : 'bg-gray-800 hover:bg-gray-700'
                      }`}
                      onClick={() => setSelectedEquipment(equipment)}
                    >
                      <div className="flex justify-between items-center">
                        <span className={`text-base ${getEquipmentColor(equipment.rarity)}`}>
                          {equipment.name}
                        </span>
                        <span className="text-xs text-gray-400">
                          Level {equipment.level}
                        </span>
                      </div>

                      <div className="mt-1 space-y-1">
                        {equipment.stats.attackBonus && (
                          <div className="text-xs text-green-400">
                            +{equipment.stats.attackBonus} Attack
                          </div>
                        )}
                        {equipment.stats.defenseBonus && (
                          <div className="text-xs text-green-400">
                            +{equipment.stats.defenseBonus} Defense
                          </div>
                        )}
                        {equipment.stats.speedBonus && (
                          <div className="text-xs text-green-400">
                            +{equipment.stats.speedBonus} Speed
                          </div>
                        )}
                        {equipment.stats.specialEffect && (
                          <div className="text-xs text-blue-400">
                            {equipment.stats.specialEffect}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 bg-gray-800 rounded">
                  {selectedEquipmentType ? (
                    <div>
                      <p className="text-gray-400 text-center mb-2">No {selectedEquipmentType}s available</p>
                      <p className="text-xs text-blue-400 text-center">Purchase equipment from the MerchGenie kiosk</p>
                    </div>
                  ) : (
                    <p className="text-gray-400 text-center">Select an equipment type</p>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="flex justify-between mt-6">
            <button
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded"
              onClick={() => {
                setEquipMenuOpen(false);
                setSelectedEquipmentType(null);
                setSelectedEquipment(null);
              }}
            >
              Cancel
            </button>

            {selectedEquipmentType && (
              <button
                className={`px-4 py-2 bg-green-600 text-white rounded ${
                  selectedEquipment ? 'hover:bg-green-700' : 'opacity-50 cursor-not-allowed'
                }`}
                onClick={() => selectedEquipment && handleEquip(selectedEquipment)}
                disabled={!selectedEquipment}
              >
                Equip
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div className="bg-gray-900 border-2 border-blue-500 rounded-lg w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b border-blue-800">
            <h2 className="text-blue-400 text-xl font-bold">Pet Specters</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="flex flex-1 overflow-hidden">
            {/* Pet selection sidebar */}
            <div className="w-1/3 border-r border-blue-800 overflow-y-auto p-3 bg-gray-800">
              <h3 className="text-blue-300 text-sm mb-3">Your Companions</h3>

              <div className="space-y-2">
                {pets.map((pet, index) => (
                  <div
                    key={pet.id}
                    onClick={() => setSelectedPetIndex(index)}
                    className={`p-2 rounded cursor-pointer transition-colors ${
                      index === selectedPetIndex
                        ? 'bg-blue-900 border border-blue-500'
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <div className="flex justify-between">
                      <span className="text-white font-medium">{pet.name}</span>
                      <span className="text-blue-300 text-xs">Lvl {pet.level}</span>
                    </div>
                    <div className="text-xs text-gray-300">{pet.specterType.name} Specter</div>

                    {/* Health bar */}
                    <div className="mt-2 h-1.5 bg-gray-600 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-green-500"
                        style={{ width: `${(pet.health / pet.maxHealth) * 100}%` }}
                      ></div>
                    </div>

                    {/* Behavior indicator */}
                    <div className="mt-1 flex justify-between items-center">
                      <div className={`h-2 w-2 rounded-full ${getBehaviorStateColor(pet.behaviorState)}`}></div>
                      <span className="text-xs text-gray-400 capitalize">{pet.behaviorState}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pet details */}
            <div className="w-2/3 overflow-y-auto p-4">
              {/* Pet name and type */}
              <div className="mb-4 flex items-center justify-between">
                <div>
                  <h3 className="text-xl text-white font-bold">{selectedPet.name}</h3>
                  <div className="text-blue-400">Level {selectedPet.level} {selectedPet.specterType.name} Specter</div>
                </div>

                {/* Current state indicator */}
                <div className={`px-3 py-1 rounded-full ${getBehaviorStateColor(selectedPet.behaviorState)} text-white capitalize`}>
                  {selectedPet.behaviorState}
                </div>
              </div>

              <div className="text-sm text-gray-400 mb-4">XP: {selectedPet.xp}/{selectedPet.xpToNextLevel}</div>

              {/* Behavior Controls */}
              <div className="mb-6">
                <h4 className="text-blue-300 border-b border-blue-800 pb-1 mb-3">Commands</h4>

                <div className="grid grid-cols-4 gap-2">
                  <button
                    className={`px-3 py-2 rounded font-medium text-white focus:outline-none
                      ${selectedPet.behaviorState === 'follow'
                        ? 'bg-blue-600 ring-2 ring-blue-300'
                        : 'bg-blue-800 hover:bg-blue-700'}`}
                    onClick={() => changeBehaviorState('follow')}
                  >
                    Follow
                  </button>

                  <button
                    className={`px-3 py-2 rounded font-medium text-white focus:outline-none
                      ${selectedPet.behaviorState === 'attack'
                        ? 'bg-red-600 ring-2 ring-red-300'
                        : 'bg-red-800 hover:bg-red-700'}`}
                    onClick={() => changeBehaviorState('attack')}
                  >
                    Attack
                  </button>

                  <button
                    className={`px-3 py-2 rounded font-medium text-white focus:outline-none
                      ${selectedPet.behaviorState === 'idle'
                        ? 'bg-gray-600 ring-2 ring-gray-300'
                        : 'bg-gray-700 hover:bg-gray-600'}`}
                    onClick={() => changeBehaviorState('idle')}
                  >
                    Stay
                  </button>
                </div>
              </div>

              {/* Stats */}
              <div className="mb-6">
                <h4 className="text-blue-300 border-b border-blue-800 pb-1 mb-3">Stats</h4>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <div className="text-gray-400 text-sm">Health</div>
                    <div className="text-white">{Math.floor(selectedPet.health)}/{selectedPet.maxHealth}</div>
                    <div className="mt-1 h-2 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-green-500"
                        style={{ width: `${(selectedPet.health / selectedPet.maxHealth) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="text-gray-400 text-sm">Attack Power</div>
                    <div className="text-white">{selectedPet.attackPower}</div>
                  </div>

                  <div>
                    <div className="text-gray-400 text-sm">Defense</div>
                    <div className="text-white">{selectedPet.defenseValue}</div>
                  </div>

                  <div>
                    <div className="text-gray-400 text-sm">Speed</div>
                    <div className="text-white">{selectedPet.speed}</div>
                  </div>
                </div>
              </div>

              {/* Traits */}
              <div className="mb-6">
                <h4 className="text-blue-300 border-b border-blue-800 pb-1 mb-3">Traits</h4>

                <div className="space-y-3">
                  {selectedPet.traits.map((trait, index) => (
                    <div key={index}>
                      <div className="flex justify-between">
                        <span className="text-gray-300">{formatTraitName(trait.type)}</span>
                        <span className="text-blue-300">Level {trait.level}</span>
                      </div>
                      <div className="mt-1 h-2 bg-gray-800 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-blue-500"
                          style={{ width: `${(trait.xp / trait.xpToNextLevel) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Equipment */}
              <div>
                <h4 className="text-blue-300 border-b border-blue-800 pb-1 mb-3">Equipment</h4>

                <div className="grid grid-cols-3 gap-4">
                  {/* Weapon slot */}
                  <div className="bg-gray-800 rounded p-3 border border-gray-700">
                    <div className="text-gray-400 text-sm mb-2">Weapon</div>
                    {selectedPet.equipment.weapon ? (
                      <div>
                        <div className={`text-sm ${getEquipmentColor(selectedPet.equipment.weapon.rarity)}`}>
                          {selectedPet.equipment.weapon.name}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Level {selectedPet.equipment.weapon.level}
                        </div>
                        {selectedPet.equipment.weapon.stats.attackBonus && (
                          <div className="text-xs text-green-400 mt-1">
                            +{selectedPet.equipment.weapon.stats.attackBonus} Attack
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 italic">No weapon equipped</div>
                    )}
                  </div>

                  {/* Armor slot */}
                  <div className="bg-gray-800 rounded p-3 border border-gray-700">
                    <div className="text-gray-400 text-sm mb-2">Armor</div>
                    {selectedPet.equipment.armor ? (
                      <div>
                        <div className={`text-sm ${getEquipmentColor(selectedPet.equipment.armor.rarity)}`}>
                          {selectedPet.equipment.armor.name}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Level {selectedPet.equipment.armor.level}
                        </div>
                        {selectedPet.equipment.armor.stats.defenseBonus && (
                          <div className="text-xs text-green-400 mt-1">
                            +{selectedPet.equipment.armor.stats.defenseBonus} Defense
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 italic">No armor equipped</div>
                    )}
                  </div>

                  {/* Utility slot */}
                  <div className="bg-gray-800 rounded p-3 border border-gray-700">
                    <div className="text-gray-400 text-sm mb-2">Utility</div>
                    {selectedPet.equipment.utility ? (
                      <div>
                        <div className={`text-sm ${getEquipmentColor(selectedPet.equipment.utility.rarity)}`}>
                          {selectedPet.equipment.utility.name}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Level {selectedPet.equipment.utility.level}
                        </div>
                        {selectedPet.equipment.utility.stats.specialEffect && (
                          <div className="text-xs text-blue-400 mt-1">
                            {selectedPet.equipment.utility.stats.specialEffect}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 italic">No utility equipped</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer with controls */}
          <div className="p-3 border-t border-blue-800 bg-gray-800 flex justify-between">
            <div className="flex space-x-2">
              <button
                className={`px-4 py-2 text-white rounded ${
                  !isPetDeployed(selectedPet.id) || selectedPet.behaviorState === 'attack'
                    ? 'bg-gray-700 cursor-not-allowed'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
                onClick={() => {
                  // Toggle scan for enemies and attack mode
                  if (selectedPet.behaviorState !== 'attack') {
                    changeBehaviorState('attack');
                  } else {
                    changeBehaviorState('follow');
                  }
                }}
                disabled={!isPetDeployed(selectedPet.id) || selectedPet.behaviorState === 'attack'}
              >
                {selectedPet.behaviorState === 'attack' ? 'Stop Hunting' : 'Hunt Specters'}
              </button>
            </div>

            <div className="flex space-x-2">
              <button
                className="px-3 py-1.5 border border-purple-500 text-purple-400 hover:bg-purple-900 rounded text-sm"
                onClick={() => setTrainingMenuOpen(true)}
              >
                Train
              </button>
              <button
                className="px-3 py-1.5 border border-green-500 text-green-400 hover:bg-green-900 rounded text-sm"
                onClick={() => setEquipMenuOpen(true)}
              >
                Equip
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Training menu overlay */}
      {trainingMenuOpen && renderTrainingMenu()}

      {/* Equipment menu overlay */}
      {equipMenuOpen && renderEquipmentMenu()}

      {/* Training fee dialog */}
      {selectedTrait && (
        <TrainingFeeDialog
          isOpen={trainingFeeDialogOpen}
          onClose={() => setTrainingFeeDialogOpen(false)}
          petName={selectedPet.name}
          petId={selectedPet.id}
          traitType={selectedTrait}
          onTrainingSuccess={handleTrainingPaymentSuccess}
        />
      )}
    </>
  );
};

export default PetSpecterUI;