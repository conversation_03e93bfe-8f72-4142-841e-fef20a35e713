import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface DungeonRewardsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rewards: {
    experience: number;
    gold: number;
    items: any[];
  };
  dungeonLevel: number;
}

const DungeonRewardsDialog: React.FC<DungeonRewardsDialogProps> = ({
  isOpen,
  onClose,
  rewards,
  dungeonLevel
}) => {
  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'text-gray-300';
      case 'uncommon':
        return 'text-green-400';
      case 'rare':
        return 'text-blue-400';
      case 'epic':
        return 'text-purple-400';
      case 'legendary':
        return 'text-orange-400';
      default:
        return 'text-white';
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="bg-gray-900 text-white border-gray-700 max-w-md" aria-describedby="dungeon-rewards-description">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl text-yellow-400">
            Dungeon Completed!
          </DialogTitle>
          <DialogDescription id="dungeon-rewards-description" className="text-center text-gray-300">
            Level {dungeonLevel} dungeon cleared
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <h3 className="text-xl mb-4 text-center">Rewards</h3>
          
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-sm text-gray-400">Experience</div>
              <div className="text-xl text-green-400">+{rewards.experience}</div>
            </div>
            
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-sm text-gray-400">Gold</div>
              <div className="text-xl text-yellow-400">+{rewards.gold}</div>
            </div>
          </div>
          
          {rewards.items.length > 0 && (
            <div className="mt-4">
              <h4 className="text-lg mb-2">Items Found</h4>
              <div className="bg-gray-800 rounded-lg p-2">
                {rewards.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-2 border-b border-gray-700 last:border-0">
                    <div className={`${getRarityColor(item.rarity)}`}>
                      {item.type.replace('_', ' ')}
                    </div>
                    <div className="text-sm text-gray-400">
                      {item.rarity}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button 
            className="w-full bg-yellow-500 hover:bg-yellow-600 text-black" 
            onClick={onClose}
          >
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DungeonRewardsDialog;
