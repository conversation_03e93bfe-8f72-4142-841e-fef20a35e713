import { <PERSON><PERSON>pecter } from '../entities/PetSpecter';
import { BattleEvent, BattleState } from '../battle/BattleManager';

/**
 * UI for displaying battle information in the Coliseum Arena
 */
export class BattleUI {
  private container: HTMLElement;
  private battleInfoElement: HTMLElement;
  private pet1InfoElement: HTMLElement;
  private pet2InfoElement: HTMLElement;
  private battleLogElement: HTMLElement;
  private timerElement: HTMLElement;
  private isVisible: boolean = false;
  private battleStartTime: number = 0;
  private timerInterval: number | null = null;

  constructor() {
    // Create container
    this.container = document.createElement('div');
    this.container.className = 'fixed top-0 left-0 w-full h-full pointer-events-none z-50 flex flex-col items-center';
    this.container.style.display = 'none';

    // Create battle info element
    this.battleInfoElement = document.createElement('div');
    this.battleInfoElement.className = 'bg-black/70 text-white px-4 py-2 rounded-b-lg text-center font-bold';
    this.battleInfoElement.textContent = 'Battle';
    this.container.appendChild(this.battleInfoElement);

    // Create pet info container
    const petInfoContainer = document.createElement('div');
    petInfoContainer.className = 'flex justify-between w-full px-4 mt-2';
    this.container.appendChild(petInfoContainer);

    // Create pet 1 info element
    this.pet1InfoElement = document.createElement('div');
    this.pet1InfoElement.className = 'bg-blue-900/70 text-white px-4 py-2 rounded-lg w-48';
    petInfoContainer.appendChild(this.pet1InfoElement);

    // Create timer element
    this.timerElement = document.createElement('div');
    this.timerElement.className = 'bg-black/70 text-white px-4 py-2 rounded-lg mx-2 font-mono';
    this.timerElement.textContent = '00:00';
    petInfoContainer.appendChild(this.timerElement);

    // Create pet 2 info element
    this.pet2InfoElement = document.createElement('div');
    this.pet2InfoElement.className = 'bg-red-900/70 text-white px-4 py-2 rounded-lg w-48 text-right';
    petInfoContainer.appendChild(this.pet2InfoElement);

    // Create battle log element
    this.battleLogElement = document.createElement('div');
    this.battleLogElement.className = 'bg-black/70 text-white px-4 py-2 rounded-lg mt-auto mb-4 max-h-40 overflow-y-auto w-3/4';
    this.container.appendChild(this.battleLogElement);

    // Add to document
    document.body.appendChild(this.container);
  }

  /**
   * Show the battle UI
   */
  public show(): void {
    this.container.style.display = 'flex';
    this.isVisible = true;
  }

  /**
   * Hide the battle UI
   */
  public hide(): void {
    this.container.style.display = 'none';
    this.isVisible = false;
    
    if (this.timerInterval !== null) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update the battle UI with pet information
   */
  public updatePetInfo(pet1: PetSpecter, pet2: PetSpecter): void {
    // Update pet 1 info
    this.pet1InfoElement.innerHTML = `
      <div class="font-bold">${pet1.name}</div>
      <div class="flex items-center mt-1">
        <div class="h-2 bg-gray-700 rounded-full w-full">
          <div class="h-2 bg-green-500 rounded-full" style="width: ${(pet1.health / pet1.maxHealth) * 100}%"></div>
        </div>
        <div class="ml-2 text-xs">${Math.ceil(pet1.health)}/${pet1.maxHealth}</div>
      </div>
    `;

    // Update pet 2 info
    this.pet2InfoElement.innerHTML = `
      <div class="font-bold">${pet2.name}</div>
      <div class="flex items-center mt-1">
        <div class="mr-2 text-xs">${Math.ceil(pet2.health)}/${pet2.maxHealth}</div>
        <div class="h-2 bg-gray-700 rounded-full w-full">
          <div class="h-2 bg-green-500 rounded-full" style="width: ${(pet2.health / pet2.maxHealth) * 100}%"></div>
        </div>
      </div>
    `;
  }

  /**
   * Update the battle state
   */
  public updateBattleState(state: BattleState): void {
    switch (state) {
      case BattleState.WAITING:
        this.battleInfoElement.textContent = 'Waiting for battle to start...';
        break;
      case BattleState.INTRO:
        this.battleInfoElement.textContent = 'Battle starting...';
        this.battleStartTime = Date.now();
        this.startTimer();
        break;
      case BattleState.FIGHTING:
        this.battleInfoElement.textContent = 'Battle in progress';
        break;
      case BattleState.FINISHED:
        this.battleInfoElement.textContent = 'Battle finished';
        if (this.timerInterval !== null) {
          clearInterval(this.timerInterval);
          this.timerInterval = null;
        }
        break;
    }
  }

  /**
   * Add a battle event to the log
   */
  public addBattleEvent(event: BattleEvent): void {
    const eventElement = document.createElement('div');
    eventElement.className = 'text-sm mb-1';
    
    // Format the event message
    let message = event.message || '';
    
    // Add timestamp
    const timeElapsed = Math.floor((event.timestamp - this.battleStartTime) / 1000);
    const minutes = Math.floor(timeElapsed / 60).toString().padStart(2, '0');
    const seconds = (timeElapsed % 60).toString().padStart(2, '0');
    
    // Style based on event type
    switch (event.type) {
      case 'attack':
        eventElement.className += ' text-yellow-400';
        break;
      case 'damage':
        eventElement.className += ' text-red-400';
        break;
      case 'heal':
        eventElement.className += ' text-green-400';
        break;
      case 'special':
        eventElement.className += ' text-blue-400';
        break;
      case 'victory':
        eventElement.className += ' text-purple-400 font-bold';
        break;
    }
    
    eventElement.textContent = `[${minutes}:${seconds}] ${message}`;
    
    // Add to log
    this.battleLogElement.appendChild(eventElement);
    
    // Scroll to bottom
    this.battleLogElement.scrollTop = this.battleLogElement.scrollHeight;
  }

  /**
   * Start the battle timer
   */
  private startTimer(): void {
    if (this.timerInterval !== null) {
      clearInterval(this.timerInterval);
    }
    
    this.timerInterval = window.setInterval(() => {
      const elapsed = Math.floor((Date.now() - this.battleStartTime) / 1000);
      const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
      const seconds = (elapsed % 60).toString().padStart(2, '0');
      this.timerElement.textContent = `${minutes}:${seconds}`;
    }, 1000) as unknown as number;
  }

  /**
   * Check if the UI is visible
   */
  public isShown(): boolean {
    return this.isVisible;
  }

  /**
   * Clean up the UI
   */
  public destroy(): void {
    if (this.timerInterval !== null) {
      clearInterval(this.timerInterval);
    }
    
    if (this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
