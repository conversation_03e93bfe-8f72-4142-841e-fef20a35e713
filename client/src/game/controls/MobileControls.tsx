import React, { useEffect, useRef, useState, TouchEvent } from 'react';
import * as THREE from 'three';
import './MobileControls.css';

interface MobileControlsProps {
  onMove: (direction: THREE.Vector3) => void;
  onLook?: (x: number, y: number) => void;
  onJump: () => void;
  onShoot: () => void;
  onSwitchWeapon: (weaponIndex: number) => void;
  onGrapple: () => void;
  onJetpack: (active: boolean) => void;
  onInteract: () => void;
  onPet?: () => void;
}

const MobileControls: React.FC<MobileControlsProps> = ({
  onMove,
  onLook,
  onJump,
  onShoot,
  onSwitchWeapon,
  onGrapple,
  onJetpack,
  onInteract,
  onPet
}) => {
  const moveDirectionRef = useRef(new THREE.Vector3(0, 0, 0));
  const [isJetpackActive, setIsJetpackActive] = useState(false);
  const [orientation, setOrientation] = useState(
    window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
  );
  
  // Movement control state
  const [movingForward, setMovingForward] = useState(false);
  const [movingBackward, setMovingBackward] = useState(false);
  const [movingLeft, setMovingLeft] = useState(false);
  const [movingRight, setMovingRight] = useState(false);
  
  // Camera control refs
  const lookAreaRef = useRef<HTMLDivElement>(null);
  const isTouchingLookArea = useRef(false);
  const lastTouchX = useRef(0);
  const lastTouchY = useRef(0);

  // Update orientation when screen size changes
  useEffect(() => {
    const checkOrientation = () => {
      const isLandscape = window.innerWidth > window.innerHeight;
      setOrientation(isLandscape ? 'landscape' : 'portrait');
    };
    
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);
    
    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);
  
  // Movement control logic
  useEffect(() => {
    // Update direction vector based on active movement keys
    const updateMoveDirection = () => {
      let x = 0;
      let z = 0;
      
      if (movingForward) z -= 1;
      if (movingBackward) z += 1;
      if (movingLeft) x -= 1;
      if (movingRight) x += 1;
      
      // Normalize the vector if moving diagonally
      if (x !== 0 && z !== 0) {
        const length = Math.sqrt(x * x + z * z);
        x /= length;
        z /= length;
      }
      
      moveDirectionRef.current.set(x, 0, z);
      onMove(moveDirectionRef.current);
    };
    
    updateMoveDirection();
  }, [movingForward, movingBackward, movingLeft, movingRight, onMove]);
  
  // Handle movement button events
  const handleMoveForwardStart = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingForward(true);
  };
  
  const handleMoveForwardEnd = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingForward(false);
  };
  
  const handleMoveBackwardStart = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingBackward(true);
  };
  
  const handleMoveBackwardEnd = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingBackward(false);
  };
  
  const handleMoveLeftStart = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingLeft(true);
  };
  
  const handleMoveLeftEnd = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingLeft(false);
  };
  
  const handleMoveRightStart = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingRight(true);
  };
  
  const handleMoveRightEnd = (e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();
    setMovingRight(false);
  };
  
  // Camera control handlers
  const handleLookStart = (e: React.TouchEvent) => {
    if (!lookAreaRef.current) return;
    
    isTouchingLookArea.current = true;
    if (e.touches.length > 0) {
      lastTouchX.current = e.touches[0].clientX;
      lastTouchY.current = e.touches[0].clientY;
    }
  };
  
  const handleLookMove = (e: React.TouchEvent) => {
    if (!lookAreaRef.current || !isTouchingLookArea.current || !onLook) return;
    
    if (e.touches.length > 0) {
      const touch = e.touches[0];
      const deltaX = touch.clientX - lastTouchX.current;
      const deltaY = touch.clientY - lastTouchY.current;
      
      // Adjust sensitivity - higher number = slower camera movement
      const sensitivity = 15;
      const x = deltaX / sensitivity;
      const y = -deltaY / sensitivity; // Invert Y for natural camera control (up = look up)
      
      onLook(x, y);
      
      // Update last position
      lastTouchX.current = touch.clientX;
      lastTouchY.current = touch.clientY;
    }
  };
  
  const handleLookEnd = () => {
    isTouchingLookArea.current = false;
    if (onLook) onLook(0, 0); // Stop camera movement
  };

  // Handle jetpack toggle
  const handleJetpackToggle = () => {
    const newState = !isJetpackActive;
    setIsJetpackActive(newState);
    onJetpack(newState);
  };
  
  // Handle pet interaction
  const handlePet = () => {
    if (onPet) {
      onPet();
    }
  };

  return (
    <div className="mobile-controls" data-orientation={orientation}>
      {/* Movement controls - D-pad style */}
      <div className="movement-controls">
        <button 
          className="move-button forward-button"
          onTouchStart={handleMoveForwardStart}
          onTouchEnd={handleMoveForwardEnd}
          onTouchCancel={handleMoveForwardEnd}
          onTouchMove={(e) => e.preventDefault()}
          onMouseDown={handleMoveForwardStart}
          onMouseUp={handleMoveForwardEnd}
          onMouseLeave={handleMoveForwardEnd}
        >
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="white" d="M12,8L18,14H6L12,8Z" />
          </svg>
        </button>
        
        <button 
          className="move-button backward-button"
          onTouchStart={handleMoveBackwardStart}
          onTouchEnd={handleMoveBackwardEnd}
          onTouchCancel={handleMoveBackwardEnd}
          onTouchMove={(e) => e.preventDefault()}
          onMouseDown={handleMoveBackwardStart}
          onMouseUp={handleMoveBackwardEnd}
          onMouseLeave={handleMoveBackwardEnd}
        >
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="white" d="M12,16L6,10H18L12,16Z" />
          </svg>
        </button>
        
        <button 
          className="move-button left-button"
          onTouchStart={handleMoveLeftStart}
          onTouchEnd={handleMoveLeftEnd}
          onTouchCancel={handleMoveLeftEnd}
          onTouchMove={(e) => e.preventDefault()}
          onMouseDown={handleMoveLeftStart}
          onMouseUp={handleMoveLeftEnd}
          onMouseLeave={handleMoveLeftEnd}
        >
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="white" d="M16,12L10,18V6L16,12Z" />
          </svg>
        </button>
        
        <button 
          className="move-button right-button"
          onTouchStart={handleMoveRightStart}
          onTouchEnd={handleMoveRightEnd}
          onTouchCancel={handleMoveRightEnd}
          onTouchMove={(e) => e.preventDefault()}
          onMouseDown={handleMoveRightStart}
          onMouseUp={handleMoveRightEnd}
          onMouseLeave={handleMoveRightEnd}
        >
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="white" d="M8,12L14,6V18L8,12Z" />
          </svg>
        </button>
      </div>

      {/* Camera control area - touch and drag */}
      <div 
        ref={lookAreaRef}
        className="look-area"
        onTouchStart={handleLookStart}
        onTouchMove={handleLookMove}
        onTouchEnd={handleLookEnd}
        onTouchCancel={handleLookEnd}
      />

      {/* Action buttons */}
      <div className="action-buttons" style={{ 
        position: 'absolute',
        right: '20px',
        top: orientation === 'landscape' ? '20px' : '100px',
        display: 'flex',
        flexDirection: orientation === 'landscape' ? 'row' : 'column',
        gap: '15px',
        pointerEvents: 'none',
        zIndex: 1002
      }}>
        <button
          className="mobile-button pet-button"
          onClick={handlePet}
          style={{ 
            width: '60px', 
            height: '60px', 
            borderRadius: '50%', 
            border: 'none', 
            fontSize: '12px',
            backgroundColor: 'rgba(100, 200, 255, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          PET
        </button>

        <button
          className="mobile-button shoot-button"
          onTouchStart={onShoot}
          style={{ 
            width: '60px', 
            height: '60px', 
            borderRadius: '50%', 
            border: 'none', 
            fontSize: '12px',
            backgroundColor: 'rgba(255, 100, 100, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          SHOOT
        </button>

        <button
          className="mobile-button grapple-button"
          onClick={onGrapple}
          style={{ 
            width: '60px', 
            height: '60px', 
            borderRadius: '50%', 
            border: 'none', 
            fontSize: '12px',
            backgroundColor: 'rgba(100, 255, 100, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          GRAPPLE
        </button>

        <button
          className={`mobile-button jetpack-button ${isJetpackActive ? 'active' : ''}`}
          onClick={handleJetpackToggle}
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            border: 'none',
            fontSize: '12px',
            backgroundColor: isJetpackActive 
              ? 'rgba(255, 165, 0, 0.9)' 
              : 'rgba(255, 165, 0, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          JETPACK
        </button>
      </div>

      {/* Weapon selection buttons */}
      <div className="weapon-buttons" style={{ 
        position: 'absolute',
        left: '20px',
        top: '20px',
        display: 'flex',
        gap: '10px',
        pointerEvents: 'none',
        zIndex: 1002
      }}>
        <button
          className="weapon-button"
          onClick={() => onSwitchWeapon(1)}
          style={{ 
            width: '40px', 
            height: '40px', 
            borderRadius: '5px', 
            border: 'none',
            backgroundColor: 'rgba(200, 200, 200, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          1
        </button>
        <button
          className="weapon-button"
          onClick={() => onSwitchWeapon(2)}
          style={{ 
            width: '40px', 
            height: '40px', 
            borderRadius: '5px', 
            border: 'none',
            backgroundColor: 'rgba(200, 200, 200, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          2
        </button>
        <button
          className="weapon-button"
          onClick={() => onSwitchWeapon(3)}
          style={{ 
            width: '40px', 
            height: '40px', 
            borderRadius: '5px', 
            border: 'none',
            backgroundColor: 'rgba(200, 200, 200, 0.7)',
            pointerEvents: 'auto'
          }}
        >
          3
        </button>
      </div>

      {/* Interact button */}
      <button
        className="mobile-button interact-button"
        onClick={onInteract}
        style={{
          position: 'absolute',
          bottom: orientation === 'landscape' ? '140px' : '220px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '80px',
          height: '40px',
          borderRadius: '20px',
          border: 'none',
          fontSize: '14px',
          backgroundColor: 'rgba(100, 100, 255, 0.7)',
          pointerEvents: 'auto',
          zIndex: 1002
        }}
      >
        INTERACT
      </button>
    </div>
  );
};

export default MobileControls;
