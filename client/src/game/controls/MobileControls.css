.mobile-controls {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 1000 !important;
  display: block !important;
  visibility: visible !important;
  overflow: hidden !important;
  touch-action: none !important;
}

/* Movement controls (d-pad) */
.movement-controls {
  position: absolute !important;
  left: 20px !important;
  bottom: 20px !important;
  width: 150px !important;
  height: 150px !important;
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  grid-template-rows: 1fr 1fr 1fr !important;
  gap: 5px !important;
  z-index: 1001 !important;
  pointer-events: none !important;
}

.move-button {
  pointer-events: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 5px !important;
  border: none !important;
  background-color: rgba(50, 50, 50, 0.6) !important;
  cursor: pointer !important;
  -webkit-tap-highlight-color: transparent !important;
  user-select: none !important;
  touch-action: manipulation !important;
}

.move-button:active {
  transform: scale(0.95) !important;
  background-color: rgba(80, 80, 80, 0.8) !important;
}

.forward-button {
  grid-column: 2 !important;
  grid-row: 1 !important;
}

.backward-button {
  grid-column: 2 !important;
  grid-row: 3 !important;
}

.left-button {
  grid-column: 1 !important;
  grid-row: 2 !important;
}

.right-button {
  grid-column: 3 !important;
  grid-row: 2 !important;
}

/* Camera control area */
.look-area {
  position: absolute !important;
  right: 20px !important;
  bottom: 20px !important;
  width: 150px !important;
  height: 150px !important;
  background-color: rgba(50, 50, 50, 0.3) !important;
  border-radius: 75px !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  pointer-events: auto !important;
  z-index: 1001 !important;
  touch-action: none !important;
  cursor: pointer !important;
}

.look-area:active {
  background-color: rgba(50, 50, 50, 0.5) !important;
}

.mobile-button {
  pointer-events: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.1s, background-color 0.2s !important;
  z-index: 1002 !important;
  visibility: visible !important;
  opacity: 1 !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  user-select: none !important;
}

.mobile-button:active {
  transform: scale(0.95) !important;
}

.pet-button {
  background: rgba(100, 200, 255, 0.7) !important;
}

.shoot-button {
  background: rgba(255, 100, 100, 0.7) !important;
}

.grapple-button {
  background: rgba(100, 255, 100, 0.7) !important;
}

.jetpack-button {
  background: rgba(255, 165, 0, 0.7) !important;
}

.jetpack-button.active {
  background: rgba(255, 165, 0, 0.9) !important;
}

.interact-button {
  background: rgba(100, 100, 255, 0.7) !important;
  position: absolute !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 80px !important;
  height: 40px !important;
  border-radius: 20px !important;
  border: none !important;
  font-size: 14px !important;
}

.weapon-button {
  background: rgba(200, 200, 200, 0.7) !important;
  font-weight: bold !important;
  pointer-events: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 5px !important;
  border: none !important;
  z-index: 1002 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.action-buttons {
  position: absolute !important;
  right: 20px !important;
  display: flex !important;
  gap: 15px !important;
  pointer-events: none !important;
  z-index: 1002 !important;
  visibility: visible !important;
}

.action-buttons button {
  pointer-events: auto !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  border: none !important;
  font-size: 12px !important;
}

.weapon-buttons {
  position: absolute !important;
  left: 20px !important;
  top: 20px !important;
  display: flex !important;
  gap: 10px !important;
  pointer-events: none !important;
  z-index: 1002 !important;
  visibility: visible !important;
}

.weapon-buttons button {
  pointer-events: auto !important;
}

/* Landscape orientation styles */
.mobile-controls[data-orientation="landscape"] .action-buttons {
  top: 20px !important;
  flex-direction: row !important;
}

.mobile-controls[data-orientation="landscape"] .interact-button {
  bottom: 140px !important;
}

/* Portrait orientation styles */
.mobile-controls[data-orientation="portrait"] .action-buttons {
  top: 100px !important;
  flex-direction: column !important;
}

.mobile-controls[data-orientation="portrait"] .interact-button {
  bottom: 220px !important;
}

/* Small screen adjustments */
@media (max-height: 600px) {
  .action-buttons {
    top: 60px !important;
    gap: 10px !important;
  }
  
  .mobile-button, .action-buttons button {
    width: 50px !important;
    height: 50px !important;
  }
  
  .movement-controls, .look-area {
    width: 120px !important;
    height: 120px !important;
  }
  
  .look-area {
    border-radius: 60px !important;
  }
}

@media (max-width: 400px) {
  .movement-controls, .look-area {
    width: 120px !important;
    height: 120px !important;
    bottom: 10px !important;
  }
  
  .movement-controls {
    left: 10px !important;
  }
  
  .look-area {
    right: 10px !important;
    border-radius: 60px !important;
  }
}

/* Fix for iOS */
@supports (-webkit-touch-callout: none) {
  .mobile-controls, .movement-controls, .look-area, .mobile-button, .weapon-button {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .movement-controls {
    display: grid !important;
  }
}

/* Fix for Chrome/Brave specific issues */
@supports (-webkit-appearance: none) {
  .mobile-controls {
    pointer-events: all !important;
  }
  
  .mobile-controls * {
    visibility: visible !important;
  }
  
  .movement-controls {
    display: grid !important;
    position: absolute !important;
    pointer-events: none !important;
  }
  
  .move-button {
    pointer-events: auto !important;
    display: flex !important;
  }
  
  .look-area {
    position: absolute !important;
    pointer-events: auto !important;
    display: block !important;
  }
  
  .mobile-button, .weapon-button {
    z-index: 1002 !important;
    pointer-events: auto !important;
    display: flex !important;
  }
}

/* Ensure inputs work on old mobile browsers */
.movement-controls, 
.move-button,
.look-area,
.mobile-button,
.weapon-button {
  cursor: pointer !important;
}
