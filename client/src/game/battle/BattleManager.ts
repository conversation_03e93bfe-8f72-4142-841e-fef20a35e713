import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { PetSpecter } from '../entities/PetSpecter';
import { audioManager as AudioManager } from '../audio/AudioManager';

/**
 * Battle states for the autonomous battle system
 */
export enum BattleState {
  WAITING = 'waiting',
  INTRO = 'intro',
  FIGHTING = 'fighting',
  FINISHED = 'finished'
}

/**
 * Result of a battle
 */
export interface BattleResult {
  winnerId: number;
  loserIds: number[];
  duration: number;
  events: BattleEvent[];
}

/**
 * Battle event for logging and visualization
 */
export interface BattleEvent {
  timestamp: number;
  type: 'attack' | 'damage' | 'heal' | 'special' | 'victory';
  sourceId?: number;
  targetId?: number;
  value?: number;
  position?: THREE.Vector3;
  message?: string;
}

/**
 * Manages autonomous battles between pet specters in the Coliseum Arena
 */
export class BattleManager {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private pets: PetSpecter[] = [];
  private battleState: BattleState = BattleState.WAITING;
  private battleStartTime: number = 0;
  private battleEvents: BattleEvent[] = [];
  private battleDuration: number = 0;
  private winnerId: number | null = null;
  private spectatorMode: boolean = true;
  private battleInterval: number | null = null;
  private onBattleEnd: ((result: BattleResult) => void) | null = null;

  constructor(scene: THREE.Scene, world: CANNON.World) {
    this.scene = scene;
    this.world = world;
  }

  /**
   * Add a pet to the battle
   */
  public addPet(pet: PetSpecter): void {
    this.pets.push(pet);
    
    // Position pets on opposite sides of the arena
    if (this.pets.length === 1) {
      pet.setPosition(new THREE.Vector3(-20, 5, 0));
    } else if (this.pets.length === 2) {
      pet.setPosition(new THREE.Vector3(20, 5, 0));
    } else {
      // For more than 2 pets, position them in a circle
      const angle = (Math.PI * 2 / this.pets.length) * (this.pets.length - 1);
      const radius = 25;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      pet.setPosition(new THREE.Vector3(x, 5, z));
    }

    // Set pet to battle mode
    pet.behaviorState = 'attack';
    
    // Log the event
    this.logEvent({
      timestamp: Date.now(),
      type: 'special',
      sourceId: pet.id,
      message: `${pet.name} enters the arena!`,
      position: pet.getPosition()
    });
  }

  /**
   * Start the battle
   */
  public startBattle(onBattleEnd?: (result: BattleResult) => void): void {
    if (this.pets.length < 2) {
      console.error('Need at least 2 pets to start a battle');
      return;
    }

    this.onBattleEnd = onBattleEnd || null;
    this.battleState = BattleState.INTRO;
    this.battleStartTime = Date.now();
    this.battleEvents = [];
    this.winnerId = null;

    // Log battle start
    this.logEvent({
      timestamp: this.battleStartTime,
      type: 'special',
      message: 'Battle begins!',
    });

    // Start intro sequence
    setTimeout(() => {
      this.battleState = BattleState.FIGHTING;
      
      // Make pets target each other
      this.assignTargets();
      
      // Start battle update interval
      this.battleInterval = window.setInterval(() => this.updateBattle(), 1000) as unknown as number;
      
      // Play battle music
      AudioManager.playSound('battleMusic', 0.5, true);
    }, 3000);
  }

  /**
   * Update the battle state
   */
  private updateBattle(): void {
    if (this.battleState !== BattleState.FIGHTING) return;

    // Check if battle is over (only one pet with health > 0)
    const alivePets = this.pets.filter(pet => pet.health > 0);
    
    if (alivePets.length <= 1) {
      this.endBattle(alivePets.length === 1 ? alivePets[0].id : null);
      return;
    }

    // Reassign targets if needed
    this.assignTargets();

    // Update battle duration
    this.battleDuration = (Date.now() - this.battleStartTime) / 1000;
  }

  /**
   * Assign targets to pets
   */
  private assignTargets(): void {
    // For each pet, find a target that is not itself and is alive
    for (const pet of this.pets) {
      if (pet.health <= 0) continue; // Skip dead pets
      
      // If pet already has a valid target, skip
      if (pet.currentTarget && this.isValidTarget(pet, pet.currentTarget)) {
        continue;
      }
      
      // Find a new target
      const possibleTargets = this.pets.filter(p => 
        p !== pet && p.health > 0 && p.mesh && this.scene.getObjectById(p.mesh.id)
      );
      
      if (possibleTargets.length > 0) {
        // Select a random target
        const target = possibleTargets[Math.floor(Math.random() * possibleTargets.length)];
        
        // Set the target
        if (target.mesh) {
          pet.currentTarget = target.mesh;
          
          // Log the targeting event
          this.logEvent({
            timestamp: Date.now(),
            type: 'special',
            sourceId: pet.id,
            targetId: target.id,
            message: `${pet.name} targets ${target.name}!`,
          });
        }
      }
    }
  }

  /**
   * Check if a target is valid
   */
  private isValidTarget(pet: PetSpecter, target: THREE.Object3D): boolean {
    // Check if target exists in scene
    if (!this.scene.getObjectById(target.id)) {
      return false;
    }
    
    // Check if target is a pet that is alive
    for (const p of this.pets) {
      if (p.mesh && p.mesh.id === target.id && p !== pet && p.health > 0) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * End the battle
   */
  private endBattle(winnerId: number | null): void {
    this.battleState = BattleState.FINISHED;
    this.winnerId = winnerId;
    
    // Clear battle interval
    if (this.battleInterval !== null) {
      clearInterval(this.battleInterval);
      this.battleInterval = null;
    }
    
    // Log battle end
    const winnerPet = winnerId !== null ? this.pets.find(p => p.id === winnerId) : null;
    
    this.logEvent({
      timestamp: Date.now(),
      type: 'victory',
      sourceId: winnerId || undefined,
      message: winnerPet ? `${winnerPet.name} is victorious!` : 'Battle ended in a draw!',
    });
    
    // Stop battle music
    AudioManager.stopSound('battleMusic');
    
    // Play victory sound
    if (winnerPet) {
      AudioManager.playSoundEffect('victory');
    }
    
    // Create battle result
    const result: BattleResult = {
      winnerId: winnerId || 0,
      loserIds: this.pets.filter(p => p.id !== winnerId).map(p => p.id),
      duration: this.battleDuration,
      events: this.battleEvents
    };
    
    // Call onBattleEnd callback
    if (this.onBattleEnd) {
      this.onBattleEnd(result);
    }
  }

  /**
   * Log a battle event
   */
  private logEvent(event: BattleEvent): void {
    this.battleEvents.push(event);
    console.log(`[Battle] ${event.message}`);
  }

  /**
   * Get the current battle state
   */
  public getBattleState(): BattleState {
    return this.battleState;
  }

  /**
   * Get the battle events
   */
  public getBattleEvents(): BattleEvent[] {
    return this.battleEvents;
  }

  /**
   * Get the winner ID
   */
  public getWinnerId(): number | null {
    return this.winnerId;
  }

  /**
   * Get the battle duration
   */
  public getBattleDuration(): number {
    return this.battleDuration;
  }

  /**
   * Set spectator mode
   */
  public setSpectatorMode(enabled: boolean): void {
    this.spectatorMode = enabled;
  }

  /**
   * Check if in spectator mode
   */
  public isSpectatorMode(): boolean {
    return this.spectatorMode;
  }

  /**
   * Reset the battle manager
   */
  public reset(): void {
    this.pets = [];
    this.battleState = BattleState.WAITING;
    this.battleStartTime = 0;
    this.battleEvents = [];
    this.battleDuration = 0;
    this.winnerId = null;
    
    if (this.battleInterval !== null) {
      clearInterval(this.battleInterval);
      this.battleInterval = null;
    }
  }
}
