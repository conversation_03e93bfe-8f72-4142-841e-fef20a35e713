// Global type declarations

// Extend the Window interface to include gameEngine and authContext
interface Window {
  gameEngine: any;
  authContext: {
    isLoggedIn: boolean;
    currentUser: any | null;
    authMethod: 'orangeID' | 'wallet' | null;
    orangeIDUser: any | null;
    isOrangeIDLoggedIn: boolean;
    isWalletConnected: boolean;
    walletAddress: string | null;
    logout: () => Promise<void>;
    linkWalletToOrangeID: () => Promise<boolean>;
    isAuthLoading: boolean;
    walletUser: { walletAddress: string; displayName: string; } | null;
    ensureValidSession: () => boolean;
    checkSessionExpiry: () => void;
  } | undefined;
}
