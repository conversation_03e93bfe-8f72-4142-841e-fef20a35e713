declare module 'three/examples/jsm/controls/PointerLockControls' {
  import { Camera, EventDispatcher } from 'three';

  export class PointerLockControls extends EventDispatcher {
    constructor(camera: Camera, domElement?: HTMLElement);
    isLocked: boolean;
    connect(): void;
    disconnect(): void;
    dispose(): void;
    getObject(): Camera;
    getDirection(v: THREE.Vector3): THREE.Vector3;
    moveForward(distance: number): void;
    moveRight(distance: number): void;
    lock(): void;
    unlock(): void;
    pointerSpeed: number;
  }
}
