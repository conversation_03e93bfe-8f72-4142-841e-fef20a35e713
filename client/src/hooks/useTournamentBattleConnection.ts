import { useState, useEffect, useRef, useCallback } from 'react';
import { MessageType, NetworkMessage } from '@shared/schema';
import { createWebSocketConnection, WebSocketConnectionType, getWebSocketUrl } from '@/utils/webSocketUtils';
import { createTournamentBattleConnection, joinTournamentBattle as joinBattle, leaveTournamentBattle as leaveBattle } from '@/utils/unifiedWebSocket';

interface TournamentBattleConnectionState {
  connected: boolean;
  connecting: boolean;
  connectionId: string | null;
  error: string | null;
  battleId: string | null;
  lastConnectionAttempt: number;
  lastErrorCode: number | null;
}

interface TournamentBattleConnectionOptions {
  // Control whether to auto-connect when the hook is initialized
  autoConnect?: boolean;
  onConnect?: (connectionId: string) => void;
  onDisconnect?: () => void;
  onBattleUpdate?: (data: any) => void;
  onRenderInstructions?: (data: any) => void;
  onError?: (error: string) => void;
}

/**
 * Custom hook for tournament battle WebSocket connection
 * This is a dedicated connection for tournament battles to isolate from the main game WebSocket
 */
export const useTournamentBattleConnection = (options: TournamentBattleConnectionOptions = {}) => {
  const wsRef = useRef<WebSocket | null>(null);
  const [state, setState] = useState<TournamentBattleConnectionState>({
    connected: false,
    connecting: false,
    connectionId: null,
    error: null,
    battleId: null,
    lastConnectionAttempt: 0,
    lastErrorCode: null
  });

  // Handle incoming messages
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      console.log('>>> Tournament Battle: Raw WebSocket message received:', event.data);

      const message = JSON.parse(event.data) as NetworkMessage;
      console.log(`Tournament Battle: Received message of type ${message.type}:`, message);

      switch (message.type) {
        case MessageType.TournamentBattleJoin:
          // Handle join confirmation
          if (message.data.success) {
            console.log('Tournament Battle: Join successful');
            setState(prev => ({
              ...prev,
              battleId: message.data.battleId
            }));
          } else {
            console.error('Tournament Battle: Join failed:', message.data.message);
            setState(prev => ({
              ...prev,
              error: message.data.message
            }));
          }
          break;

        case MessageType.TournamentBattleUpdate:
          // Handle battle update
          console.log('Tournament Battle: Received battle update');
          if (options.onBattleUpdate) {
            options.onBattleUpdate(message.data);
          }
          break;

        case MessageType.TournamentBattleRenderInstructions:
          // Handle render instructions
          console.log('Tournament Battle: Received render instructions');
          if (options.onRenderInstructions) {
            options.onRenderInstructions(message.data);
          }
          break;

        case MessageType.Pong:
          // Handle pong response
          console.log('Tournament Battle: Received pong response');
          break;

        case MessageType.Error:
          // Handle error message
          console.error('Tournament Battle: Received error:', message.data.message);
          setState(prev => ({
            ...prev,
            error: message.data.message
          }));
          if (options.onError) {
            options.onError(message.data.message);
          }
          break;

        default:
          console.log(`Tournament Battle: Unhandled message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Tournament Battle: Error handling WebSocket message:', error);
    }
  }, [options]);

  // Handle connecting to a tournament battle
  const handleJoinBattle = useCallback((battleId: string, connectionId: string, ws: WebSocket) => {
    if (!ws || ws.readyState !== WebSocket.OPEN || !connectionId) {
      console.error('Cannot join tournament battle: WebSocket not connected or no connection ID');
      return;
    }

    console.log(`Tournament Battle: Joining battle ${battleId} as spectator with connection ID ${connectionId}`);

    // Get the wallet address if available
    const walletAddress = localStorage.getItem('walletAddress');
    const userId = walletAddress || connectionId;

    // Check if this is a test battle (UUID format)
    const isTestBattle = battleId.length >= 36;

    // Send the join message
    const joinMessage: NetworkMessage = {
      type: MessageType.TournamentBattleJoin,
      data: {
        battleId,
        isSpectator: true,
        userId,
        clientTimestamp: Date.now(),
        isTestBattle // Add flag to indicate this is a test battle
      },
      timestamp: Date.now(),
      sender: connectionId
    };

    try {
      console.log(`Tournament Battle: Sending tournament battle join request:`, joinMessage);
      ws.send(JSON.stringify(joinMessage));
      console.log(`Tournament Battle: Sent tournament battle join request`);

      // Update state with battle ID
      setState(prev => ({
        ...prev,
        battleId
      }));

      // For test battles, also send directly to the game engine via custom event
      if (isTestBattle) {
        console.log('Tournament Battle: Dispatching direct join event for test battle');
        const joinEvent = new CustomEvent('tournament-battle-join', {
          detail: {
            battleId,
            isSpectator: true,
            userId,
            isTestBattle: true
          }
        });
        window.dispatchEvent(joinEvent);
      }
    } catch (err) {
      console.error(`Tournament Battle: Error sending tournament battle join request:`, err);
    }
  }, []);

  // Connect to WebSocket server
  const connect = useCallback(() => {
    // Add debounce check to prevent rapid connection attempts
    const now = Date.now();
    const minInterval = 2000; // Minimum 2 seconds between connection attempts
    
    if (now - state.lastConnectionAttempt < minInterval) {
      console.log(`Tournament Battle: Connection attempt throttled (last attempt ${now - state.lastConnectionAttempt}ms ago)`);
      return;
    }
    
    if (wsRef.current && (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING)) {
      console.log('Tournament Battle: WebSocket connection already in progress...');
      return;
    }

    // Close any existing connection first if not on the PVP page
    if (wsRef.current && window.location.pathname !== '/pvp') {
      try {
        console.log('Tournament Battle: Closing existing WebSocket connection before creating a new one');
        wsRef.current.close(1000, 'Intentional close before reconnect');
      } catch (err) {
        console.warn('Tournament Battle: Error closing existing WebSocket connection:', err);
      }
      wsRef.current = null;
    } else if (wsRef.current) {
      console.log('Tournament Battle: On PVP page, keeping existing WebSocket connection');
      return; // Don't create a new connection if we already have one on the PVP page
    }

    setState(prev => ({
      ...prev,
      connecting: true,
      error: null,
      lastConnectionAttempt: now
    }));

    // Get battle ID from URL or session storage
    const params = new URLSearchParams(window.location.search);
    const battleIdFromUrl = params.get('battleId');
    const battleIdFromSession = sessionStorage.getItem('battleId');
    const battleId = battleIdFromUrl || battleIdFromSession;

    // Get the WebSocket URL using our utility function
    const url = getWebSocketUrl(
      WebSocketConnectionType.TOURNAMENT, 
      battleId || undefined // Convert null to undefined to satisfy type check
    );

    console.log(`Tournament Battle: Connecting to WebSocket server at: ${url} (new connection)`);

    try {
      // Create a new WebSocket connection using our consolidated utility
      console.log('Tournament Battle: Creating WebSocket connection with consolidated WebSocket utility');
      const ws = createWebSocketConnection(url, WebSocketConnectionType.TOURNAMENT);

      // Store the WebSocket connection immediately
      wsRef.current = ws;

      // Add a global reference for debugging
      (window as any).__tournamentWebSocket = ws;
      console.log('Tournament Battle: WebSocket connection stored in window.__tournamentWebSocket for debugging');

      console.log('Tournament Battle: Created WebSocket connection with readyState:', ws.readyState);
      console.log('Tournament Battle: WebSocket URL:', ws.url);
      console.log('Tournament Battle: WebSocket protocol:', ws.protocol || 'none');
      console.log('Tournament Battle: WebSocket extensions:', ws.extensions || 'none');
      console.log('Tournament Battle: WebSocket binaryType:', ws.binaryType);

      // Set up connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          console.error('Tournament Battle: Connection timeout');
          
          setState(prev => ({
            ...prev,
            connecting: false,
            error: 'Connection timeout - unable to establish connection to tournament server'
          }));

          // Display a more visible error for the user
          if (options.onError) {
            options.onError('Failed to connect to the tournament server. Please refresh and try again.');
          }
        }
      }, 10000); // 10 second timeout

      ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('Tournament Battle: WebSocket connection established');
        console.log(`Tournament Battle: WebSocket readyState: ${ws.readyState}`);
        console.log(`Tournament Battle: WebSocket URL: ${ws.url}`);
        console.log(`Tournament Battle: WebSocket protocol: ${ws.protocol}`);
        console.log(`Tournament Battle: WebSocket extensions: ${ws.extensions}`);
        console.log(`Tournament Battle: WebSocket binaryType: ${ws.binaryType}`);

        // Generate a unique connection ID
        const connectionId = `tournament_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        setState(prev => ({
          ...prev,
          connected: true,
          connecting: false,
          connectionId,
          lastErrorCode: null
        }));

        if (options.onConnect) {
          options.onConnect(connectionId);
        }
        
        // If we have a battleId from URL or session, automatically join it
        if (battleId) {
          console.log(`Tournament Battle: Auto-joining battle ${battleId} after connection established`);
          setTimeout(() => {
            // Use the handleJoinBattle function to join the battle
            handleJoinBattle(battleId, connectionId, ws);
          }, 500); // Small delay to ensure connection is fully established
        }
      };

      ws.onmessage = handleMessage;

      ws.onerror = (error) => {
        console.error('Tournament Battle: WebSocket error:', error);

        // Log more detailed error information
        console.error('Tournament Battle: WebSocket error details:');
        console.error('- WebSocket URL:', url);
        console.error('- WebSocket readyState:', ws.readyState);
        console.error('- WebSocket protocol:', ws.protocol || 'none');
        console.error('- WebSocket extensions:', ws.extensions || 'none');
        console.error('- WebSocket binaryType:', ws.binaryType);
        console.error('- Browser:', navigator.userAgent);
        console.error('- Page URL:', window.location.href);

        // Clear connection timeout if it exists
        clearTimeout(connectionTimeout);

        // Update state with error information
        setState(prev => ({
          ...prev,
          error: 'WebSocket connection error - unable to communicate with tournament server',
          connecting: false
        }));

        // Notify of the error through callback
        if (options.onError) {
          options.onError('Connection error: Unable to establish a stable connection to the tournament server. Please check your network connection and try again.');
        }
      };

      ws.onclose = (event) => {
        clearTimeout(connectionTimeout);
        console.log(`Tournament Battle: WebSocket connection closed: Code ${event.code}, Reason: ${event.reason || 'No reason provided'}`);

        // Store the error code for debugging and handling specific cases
        setState(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          lastErrorCode: event.code
        }));

        if (options.onDisconnect) {
          options.onDisconnect();
        }
        
        // Notify the user about the disconnection if it was abnormal
        if (event.code === 1006 && options.onError) {
          options.onError('Connection to tournament server was lost unexpectedly. This may be due to network issues or server problems. Please reload the page to try again.');
        }
      };
    } catch (err: any) {
      console.error('Tournament Battle: Error creating WebSocket connection:', err);
      setState(prev => ({
        ...prev,
        connecting: false,
        error: `Failed to create WebSocket connection: ${err.message || 'Unknown error'}`
      }));
      
      // Notify of the error through callback
      if (options.onError) {
        options.onError(`Failed to connect to the tournament server: ${err.message || 'Unknown error'}. Please try again later.`);
      }
    }
  }, [options, state.lastConnectionAttempt, handleMessage, handleJoinBattle]);

  // Join a tournament battle as a spectator
  const joinTournamentBattle = useCallback((battleId: string) => {
    // Check if this is a test battle (UUID format)
    const isTestBattle = battleId.length >= 36;
    console.log(`Tournament Battle: Joining ${isTestBattle ? 'test' : 'regular'} battle ${battleId}`);

    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      console.error('Tournament Battle: Cannot join tournament battle: WebSocket not connected or no connection ID');

      // If we're not connected, try to connect first and then retry joining
      if (!state.connecting && !state.connected) {
        console.log('Tournament Battle: Attempting to connect before joining tournament battle...');
        connect();

        // Set a timeout to retry joining after connection attempt
        setTimeout(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN && state.connectionId) {
            console.log('Tournament Battle: Connection established, now attempting to join tournament battle...');
            handleJoinBattle(battleId, state.connectionId, wsRef.current);
          } else {
            console.error('Tournament Battle: Failed to establish connection for tournament battle join');
          }
        }, 2000); // Wait 2 seconds for connection to establish
      }

      return;
    }

    // Use the handleJoinBattle function
    handleJoinBattle(battleId, state.connectionId, wsRef.current);
  }, [connect, state.connectionId, state.connecting, state.connected, handleJoinBattle]);

  // Leave a tournament battle
  const leaveTournamentBattle = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId || !state.battleId) {
      console.error('Tournament Battle: Cannot leave tournament battle: WebSocket not connected, no connection ID, or no battle ID');
      return;
    }

    console.log(`Tournament Battle: Leaving battle ${state.battleId} with connection ID ${state.connectionId}`);

    const message: NetworkMessage = {
      type: MessageType.TournamentBattleLeave,
      data: {
        battleId: state.battleId
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    try {
      console.log('Tournament Battle: Sending tournament battle leave request:', message);
      wsRef.current.send(JSON.stringify(message));
      console.log('Tournament Battle: Sent tournament battle leave request');

      // Update state to remove battle ID
      setState(prev => ({
        ...prev,
        battleId: null
      }));
    } catch (error) {
      console.error('Tournament Battle: Error sending tournament battle leave request:', error);
    }
  }, [state.connectionId, state.battleId]);

  // Send a message to the WebSocket server
  const sendMessage = useCallback((message: NetworkMessage) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('Tournament Battle: Cannot send message: WebSocket not connected');
      return;
    }

    try {
      console.log('Tournament Battle: Sending message:', message);
      wsRef.current.send(JSON.stringify(message));
      console.log('Tournament Battle: Sent message');
    } catch (error) {
      console.error('Tournament Battle: Error sending message:', error);
    }
  }, []);

  // Disconnect from WebSocket server
  const disconnect = useCallback(() => {
    // Don't disconnect when on the PVP page
    if (window.location.pathname.includes('/pvp')) {
      console.log('Tournament Battle: On PVP page, ignoring disconnect request');
      return;
    }

    if (wsRef.current) {
      console.log('Tournament Battle: Disconnecting from WebSocket server...');

      // Leave battle if we're in one
      if (state.battleId) {
        leaveTournamentBattle();
      }

      wsRef.current.close(1000, 'User initiated disconnect');
      wsRef.current = null;

      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        connectionId: null,
        battleId: null
      }));
    }
  }, [leaveTournamentBattle, state.battleId]);

  // Connect on mount if autoConnect is true
  useEffect(() => {
    // Check if we should auto-connect
    const shouldAutoConnect = options.autoConnect === true; // Default to false - only connect when explicitly requested

    // Check if we're on the PVP page - always auto-connect on PVP page
    const isPvpPage = window.location.pathname.includes('/pvp');
    const urlParams = new URLSearchParams(window.location.search);
    const hasBattleId = urlParams.has('battleId');

    // Force auto-connect on PVP page with battleId
    if (shouldAutoConnect || (isPvpPage && hasBattleId)) {
      console.log('Tournament Battle: Auto-connecting to WebSocket server...');
      connect();
    } else {
      console.log('Tournament Battle: Auto-connect disabled or on home page - not connecting automatically');
    }

    // Check for battle ID in URL
    if (hasBattleId) {
      console.log('Tournament Battle: Battle ID from URL:', urlParams.get('battleId'));
    }

    // Clean up on unmount
    return () => {
      // Don't close the WebSocket connection when on the PVP page
      if (wsRef.current && window.location.pathname !== '/pvp') {
        console.log('Tournament Battle: Closing WebSocket connection on component unmount');
        wsRef.current.close(1000, 'Component unmounted');
      } else if (wsRef.current) {
        console.log('Tournament Battle: On PVP page, keeping WebSocket connection active');
      }
    };
  }, [connect, options.autoConnect]);

  // Create a mutable onConnect property that components can override
  const connectionHandler = {
    onConnect: options.onConnect || null
  };

  return {
    ...state,
    connect,
    disconnect,
    joinTournamentBattle,
    leaveTournamentBattle,
    sendMessage,
    onConnect: connectionHandler.onConnect
  };
};
