import { useEffect, useRef, useState } from 'react';
import { NetworkMessage, MessageType, PlayerState, GameState, WeaponEffectData } from '@shared/schema';
import { v4 as uuidv4 } from 'uuid';

interface MultiplayerOptions {
  url: string;
  token?: string;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
  // Control whether to auto-connect when the hook is initialized
  autoConnect?: boolean;
  onConnect?: (connectionId: string) => void;
  onDisconnect?: () => void;
  onPlayerJoin?: (player: any) => void;
  onPlayerLeave?: (player: any) => void;
  onPlayerUpdate?: (player: PlayerState) => void;
  onGameStart?: (game: GameState, localId: string | null) => void;
  onGameEnd?: (data: any) => void;
  onSpecterCaptured?: (data: any) => void;
  onLevelComplete?: (data: any) => void;
  onChatMessage?: (data: any) => void;
  onError?: (error: any) => void;
  // New callbacks for weapon effects
  onWeaponEffectCreated?: (effectData: WeaponEffectData) => void;
  onWeaponEffectRemoved?: (effectId: string) => void;
  // Team management callbacks
  onTeamJoin?: (data: any) => void;
  onTeamCreate?: (data: any) => void;
  onTeamList?: (teams: any[]) => void;
  onTeamBalance?: (data: any) => void;
  // Tournament battle callbacks
  onTournamentBattleUpdate?: (data: any) => void;
  onTournamentBattleRenderInstructions?: (data: any) => void;
}

interface MultiplayerState {
  connected: boolean;
  connecting: boolean;
  connectionId: string | null;
  error: string | null;
  playerId: string | null;
  gameState: GameState | null;
  players: PlayerState[];
  connectionConfirmed: boolean;
  reconnectAttempts: number;
  reconnecting: boolean;
  lastConnectionAttempt: number;
  lastErrorCode: number | null;
}

export function useMultiplayer(options: MultiplayerOptions) {
  const wsRef = useRef<WebSocket | null>(null);
  const connectionIdRef = useRef<string | null>(null);
  const [state, setState] = useState<MultiplayerState>({
    connected: false,
    connecting: false,
    connectionId: null,
    error: null,
    playerId: null,
    gameState: null,
    players: [],
    connectionConfirmed: false,
    reconnectAttempts: 0,
    reconnecting: false,
    lastConnectionAttempt: 0,
    lastErrorCode: null
  });

  // Handle incoming messages with simplified approach
  const handleMessage = (event: MessageEvent) => {
    try {
      // Log the raw message
      console.log('>>> CRITICAL: Raw WebSocket message received:', event.data);

      // Reset reconnection attempts on successful message receipt
      if (state.reconnectAttempts > 0) {
        setState(prev => ({
          ...prev,
          reconnectAttempts: 0,
          lastErrorCode: null
        }));
      }

      // Parse the message
      const message = JSON.parse(event.data) as NetworkMessage;
      console.log(`>>> CRITICAL: Parsed message type: ${message.type}`);

      // Dispatch the raw message for debugging purposes
      document.dispatchEvent(new CustomEvent('rawWebSocketMessage', {
        detail: event.data
      }));

      switch (message.type) {
        case MessageType.PlayerJoin:
          if (message.data.id) {
            // This is the connection acknowledgement
            console.log('Received connection acknowledgement from server:', message.data.id);
            // Store the connection ID immediately and mark as confirmed
            const connectionId = message.data.id;
            connectionIdRef.current = connectionId;
            setState(prev => ({
              ...prev,
              connectionId: connectionId,
              connected: true,
              connecting: false,
              connectionConfirmed: true
            }));

            // Log the updated state for debugging
            console.log(`Connection confirmed with ID: ${connectionId}, updating state...`);

            if (options.onConnect) {
              options.onConnect(connectionId);
            }
          } else if (options.onPlayerJoin) {
            // Another player joined
            options.onPlayerJoin(message.data);
          }
          break;

        case MessageType.PlayerLeave:
          if (options.onPlayerLeave) {
            options.onPlayerLeave(message.data);
          }
          break;

        case MessageType.PlayerUpdate:
          // Ignore updates for the local player
          if (message.data.id === state.connectionId) {
            console.log("Ignoring player update for self:", message.data.id);
            break; // Don't process updates for the local player
          }

          if (options.onPlayerUpdate) {
            options.onPlayerUpdate(message.data);
          }
          break;

        case MessageType.GameStart:
          console.log('>>> CRITICAL: Received GameStart message:', message.data);

          if (message.data.id) {
            // This is a full game state
            setState(prev => ({
              ...prev,
              gameState: message.data,
              players: message.data.players || []
            }));
          }

          if (options.onGameStart) {
            const localId = connectionIdRef.current;
            console.log(`>>> CRITICAL: Calling onGameStart handler with data and localId: ${localId}`, message.data);
            options.onGameStart(message.data, localId);
            console.log('>>> CRITICAL: onGameStart handler completed');
          } else {
            console.error('>>> CRITICAL ERROR: No onGameStart handler defined!');
          }
          break;

        case MessageType.GameEnd:
          if (options.onGameEnd) {
            options.onGameEnd(message.data);
          }
          break;

        case MessageType.SpecterCaptured:
          if (options.onSpecterCaptured) {
            options.onSpecterCaptured(message.data);
          }
          break;

        case MessageType.LevelComplete:
          if (options.onLevelComplete) {
            options.onLevelComplete(message.data);
          }
          break;

        case MessageType.ChatMessage:
          if (options.onChatMessage) {
            options.onChatMessage(message.data);
          }
          break;

        case MessageType.Error:
          console.error('>>> CRITICAL: Received error message from server:', message.data.message);
          setState(prev => ({
            ...prev,
            error: message.data.message || 'Unknown error'
          }));

          if (options.onError) {
            options.onError(message.data);
          }
          break;

        // Weapon effect message handlers
        case MessageType.WeaponEffectCreated:
          if (options.onWeaponEffectCreated) {
            options.onWeaponEffectCreated(message.data);
          }
          break;

        case MessageType.WeaponEffectRemoved:
          if (options.onWeaponEffectRemoved) {
            options.onWeaponEffectRemoved(message.data.id);
          }
          break;

        // Team management message handlers
        case MessageType.TeamJoin:
          if (options.onTeamJoin) {
            options.onTeamJoin(message.data);
          }
          break;

        case MessageType.TeamCreate:
          if (options.onTeamCreate) {
            options.onTeamCreate(message.data);
          }
          break;

        case MessageType.TeamList:
          if (options.onTeamList) {
            options.onTeamList(message.data.teams);
          }
          break;

        case MessageType.TeamBalance:
          if (options.onTeamBalance) {
            options.onTeamBalance(message.data);
          }
          break;

        // Tournament battle message handlers
        case MessageType.TournamentBattleUpdate:
          if (options.onTournamentBattleUpdate) {
            options.onTournamentBattleUpdate(message.data);
          }
          break;

        case MessageType.TournamentBattleRenderInstructions:
          if (options.onTournamentBattleRenderInstructions) {
            options.onTournamentBattleRenderInstructions(message.data);
          }
          break;

        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  // Connect to WebSocket server
  const connect = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return; // Already connected
    }

    if (wsRef.current?.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket connection already in progress...');
      return; // Already attempting to connect
    }

    setState(prev => ({
      ...prev,
      connecting: true,
      error: null,
      reconnecting: prev.reconnectAttempts > 0
    }));

    try {
      // Generate a client ID if not provided
      // Use existing connectionId from state if reconnecting to maintain identity
      const clientId = state.connectionId || connectionIdRef.current || uuidv4();

      // Get the wallet address if available
      const walletAddress = localStorage.getItem('walletAddress');
      const userId = walletAddress || clientId;

      // Construct WebSocket URL
      let wsUrl;

      // Use the provided URL from options
      wsUrl = options.url || 'ws://localhost:5001';

      // Add the game-ws path to the URL
      if (!wsUrl.includes('/game-ws')) {
        wsUrl += '/game-ws';
      }

      // Add the user ID as a token parameter
      wsUrl += `?token=${encodeURIComponent(userId)}`;

      console.log(`Connecting to WebSocket server at: ${wsUrl} (${state.reconnecting ? 'reconnecting' : 'new connection'})`);

      // Create a WebSocket with default configuration
      const ws = new WebSocket(wsUrl);

      // Set connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          console.error('WebSocket connection timeout');
          ws.close();

          setState(prev => ({
            ...prev,
            error: 'Connection timeout',
            connected: false,
            connecting: false
          }));

          if (options.onError) {
            options.onError({ message: 'Connection timeout' });
          }
        }
      }, 10000); // 10 second timeout

      ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('WebSocket connection established');

        // Only update connecting status, but NOT connected yet
        // We'll set connected=true when we receive the server acknowledgment
        setState(prev => ({
          ...prev,
          connecting: false,
          error: null,
          connectionId: clientId
        }));

        // Send initial handshake message
        try {
          ws.send(JSON.stringify({
            type: MessageType.PlayerJoin,
            data: { id: clientId },
            timestamp: Date.now(),
            sender: clientId
          }));
          console.log('Sent initial handshake message with ID:', clientId);
        } catch (error) {
          console.error('Error sending handshake message:', error);
        }

        // Store the initially generated clientId in the ref as well
        connectionIdRef.current = clientId;
      };

      ws.onmessage = handleMessage;

      ws.onerror = (error) => {
        clearTimeout(connectionTimeout);
        console.error('WebSocket error:', error);
        setState(prev => ({
          ...prev,
          error: 'Connection error',
          connected: false,
          connecting: false
        }));

        if (options.onError) {
          options.onError(error);
        }
      };

      ws.onclose = (event) => {
        clearTimeout(connectionTimeout);
        console.log(`WebSocket connection closed: Code ${event.code}, Reason: ${event.reason || 'No reason provided'}`);

        // Special handling for code 1006 (abnormal closure)
        const wasAbnormalClosure = event.code === 1006;
        if (wasAbnormalClosure) {
          console.warn('Abnormal closure (code 1006) detected - this often indicates network issues');
        }

        setState(prev => ({
          ...prev,
          connected: false,
          connecting: false
        }));

        if (options.onDisconnect) {
          options.onDisconnect();
        }

        // Handle unexpected disconnections with reconnection logic
        // Don't reconnect on normal closure (1000) or if explicitly closed by user (1001)
        if (event.code !== 1000 && event.code !== 1001) {
          console.log('Unexpected disconnection, will attempt to reconnect...');

          // Track reconnection attempts with current state to avoid race conditions
          setState(prev => {
            const newAttempts = prev.reconnectAttempts + 1;
            console.log(`Incrementing reconnect attempts to ${newAttempts}`);
            return {
              ...prev,
              reconnectAttempts: newAttempts,
              reconnecting: true
            };
          });

          // Store the current connection ID for reconnection
          const currentId = connectionIdRef.current;
          if (currentId) {
            console.log(`Preserving connection ID for reconnection: ${currentId}`);
            // We'll use this ID when reconnecting to maintain identity
          }

          // Check if we've exceeded the maximum number of reconnect attempts
          const maxAttempts = options.maxReconnectAttempts || 5; // Default to 5 if not specified
          if (state.reconnectAttempts >= maxAttempts) {
            console.error(`Maximum reconnect attempts (${maxAttempts}) reached. Giving up.`);
            setState(prev => ({
              ...prev,
              error: 'Maximum reconnect attempts reached',
              reconnecting: false
            }));
            return;
          }

          // Check if we're on the home page - never reconnect on home page
          const isHomePage = window.location.pathname === '/' || window.location.pathname === '';

          if (isHomePage) {
            console.log('On home page - not attempting to reconnect');
            return;
          }

          // Check if we should reconnect based on the URL path
          // Only reconnect for game or pvp paths
          const shouldReconnect = window.location.pathname.includes('/game') || window.location.pathname.includes('/pvp');

          if (!shouldReconnect) {
            console.log('Not on a game page - not attempting to reconnect');
            return;
          }

          // Implement reconnection with exponential backoff
          // For abnormal closures (1006), use a shorter initial delay
          const baseDelay = options.reconnectDelay || (wasAbnormalClosure ? 1000 : 3000);
          const delay = Math.min(baseDelay * Math.pow(1.5, state.reconnectAttempts), 30000); // Cap at 30 seconds

          console.log(`Attempting to reconnect in ${delay}ms (attempt ${state.reconnectAttempts + 1}/${maxAttempts})...`);

          setTimeout(() => {
            console.log('Attempting to reconnect now...');
            connect();
          }, delay);
        }
      };

      wsRef.current = ws;
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to connect',
        connected: false,
        connecting: false
      }));

      if (options.onError) {
        options.onError(error);
      }
    }
  };

  // Disconnect from WebSocket server
  const disconnect = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false
      }));
    }
  };

  // Send a message to the WebSocket server
  const send = (message: NetworkMessage) => {
    if (!wsRef.current) {
      console.warn('WebSocket not initialized. Cannot send message.');
      return false;
    }

    if (wsRef.current.readyState !== WebSocket.OPEN) {
      console.warn(`WebSocket not open. Current state: ${wsRef.current.readyState}. Cannot send message.`);
      return false;
    }

    try {
      // Add connection ID to message if not already present
      if (!message.sender && state.connectionId) {
        message.sender = state.connectionId;
      }

      // Add userId to data if available
      if (message.data && !message.data.userId && state.connectionId) {
        message.data.userId = state.connectionId;
      }

      // Add a unique message ID to help with debugging and tracking
      const messageWithId = {
        ...message,
        messageId: uuidv4(), // Add unique ID to each message
        timestamp: message.timestamp || Date.now() // Ensure timestamp exists
      };

      // Serialize the message to a string
      const messageString = JSON.stringify(messageWithId);
      console.log('Sending WebSocket message:', messageWithId);

      // Use try-catch within the existing try block to handle potential
      // WebSocket errors without breaking the entire function
      try {
        // Check again if the connection is still open
        if (wsRef.current.readyState !== WebSocket.OPEN) {
          console.warn(`WebSocket state changed before sending. Current state: ${wsRef.current.readyState}`);
          return false;
        }

        wsRef.current.send(messageString);
        return true;
      } catch (sendError) {
        console.error('Error in WebSocket send operation:', sendError);

        // Check if this is a connection issue and trigger reconnection if needed
        if (wsRef.current.readyState !== WebSocket.OPEN) {
          console.warn('WebSocket connection appears to be broken, will attempt to reconnect...');
          // Close the socket to trigger the reconnection logic in onclose
          wsRef.current.close();
        }
        return false;
      }
    } catch (error) {
      console.error('Error preparing WebSocket message:', error);
      return false;
    }
  };

  // Join a game arena
  const joinArena = (arenaId: number, playerName: string, teamId?: number) => {
    console.log('>>> CRITICAL: joinArena called with arenaId:', arenaId, 'playerName:', playerName);
    console.log('>>> CRITICAL: Current WebSocket state:', wsRef.current?.readyState);
    console.log('>>> CRITICAL: Current connection confirmed status:', state.connectionConfirmed);

    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('>>> CRITICAL ERROR: Not connected to server. Cannot join arena. WebSocket state:', wsRef.current?.readyState);
      setState(prev => ({
        ...prev,
        error: 'Not connected to server'
      }));
      return;
    }

    if (!state.connectionConfirmed) {
      console.error('>>> CRITICAL ERROR: Connection not confirmed by server yet. Try again in a moment.');
      setState(prev => ({
        ...prev,
        error: 'Connection not confirmed by server yet'
      }));
      return;
    }

    // Force arenaId to be a number
    const arenaIdNumber = parseInt(String(arenaId), 10);

    if (isNaN(arenaIdNumber)) {
      console.error(`>>> CRITICAL ERROR: Invalid arena ID: ${arenaId}`);
      setState(prev => ({
        ...prev,
        error: 'Invalid arena ID'
      }));
      return;
    }

    console.log(`>>> CRITICAL: Joining arena ${arenaIdNumber} as ${playerName}${teamId ? ` on team ${teamId}` : ''}`);

    const message: NetworkMessage = {
      type: MessageType.PlayerJoin,
      data: {
        arenaId: arenaIdNumber,
        playerName,
        teamId
      },
      timestamp: Date.now(),
      sender: state.connectionId || 'unknown'
    };

    try {
      // Convert the message to a string to view exactly what we're sending
      const messageStr = JSON.stringify(message);
      console.log('>>> CRITICAL: About to send join arena request (raw):', messageStr);

      wsRef.current.send(messageStr);
      console.log('>>> CRITICAL: Sent join arena request successfully');

      // Update local state
      setState(prev => ({
        ...prev,
        playerId: state.connectionId
      }));
    } catch (error) {
      console.error('>>> CRITICAL ERROR in sending join arena request:', error);
    }
  };

  // Leave current game arena
  const leaveArena = () => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.PlayerLeave,
      data: {},
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));

    // Update local state
    setState(prev => ({
      ...prev,
      gameState: null,
      players: []
    }));
  };

  // Start the game (host only)
  const startGame = () => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.GameStart,
      data: {},
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Update player state
  const updatePlayerState = (playerState: Partial<PlayerState>) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.warn('Cannot update player state: WebSocket not connected');
      return;
    }

    if (!state.connectionId) {
      console.warn('Cannot update player state: No connection ID yet');
      return;
    }

    if (!state.connectionConfirmed) {
      console.warn('Cannot update player state: Connection not confirmed by server yet');
      // Don't return here - still try to send the update, especially for critical position data
    }

    // Ensure we have a valid connection ID
    const playerId = state.connectionId;

    // Always include the player name in updates
    const playerName = playerState.name || state.playerId || 'Player';

    // Create the complete player state message
    const message: NetworkMessage = {
      type: MessageType.PlayerUpdate,
      data: {
        id: playerId,
        name: playerName,
        ...playerState,
        // Ensure all required fields are present with defaults if not provided
        position: playerState.position || { x: 0, y: 0, z: 0 },
        rotation: playerState.rotation || { x: 0, y: 0, z: 0 },
        health: playerState.health !== undefined ? playerState.health : 100,
        jetpackFuel: playerState.jetpackFuel !== undefined ? playerState.jetpackFuel : 100,
        firing: playerState.firing !== undefined ? playerState.firing : false,
        ammoType: playerState.ammoType || 'Gravity Well',
        // Add timestamp to help with reconciliation
        timestamp: Date.now()
      },
      timestamp: Date.now(),
      sender: playerId
    };

    try {
      wsRef.current.send(JSON.stringify(message));
      // Only log periodically to reduce console spam
      if (Math.random() < 0.05) { // ~5% of updates
        console.debug('Sending player state update:', message.data);
      }
    } catch (error) {
      console.error('Error sending player update:', error);
    }
  };

  // Report a specter capture
  const reportSpecterCapture = (specterId: number, specterType: any, position: { x: number, y: number, z: number }) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.SpecterCaptured,
      data: {
        specterId,
        specterType,
        position
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Send a chat message
  const sendChatMessage = (message: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const chatMessage: NetworkMessage = {
      type: MessageType.ChatMessage,
      data: {
        message
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(chatMessage));
  };

  // Create a weapon effect
  const createWeaponEffect = (effectData: WeaponEffectData) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.WeaponEffectCreated,
      data: {
        ...effectData,
        ownerId: state.connectionId
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Remove a weapon effect
  const removeWeaponEffect = (effectId: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.WeaponEffectRemoved,
      data: {
        id: effectId
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Join a team
  const joinTeam = (teamId: number) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.TeamJoin,
      data: {
        teamId
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Create a team
  const createTeam = (teamName: string, teamColor: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.TeamCreate,
      data: {
        teamName,
        teamColor
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Request team list
  const requestTeamList = () => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      return;
    }

    const message: NetworkMessage = {
      type: MessageType.TeamList,
      data: {},
      timestamp: Date.now(),
      sender: state.connectionId
    };

    wsRef.current.send(JSON.stringify(message));
  };

  // Join a tournament battle as a spectator
  const joinTournamentBattle = (battleId: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      console.error('Cannot join tournament battle: WebSocket not connected or no connection ID');
      return false;
    }

    console.log(`Joining tournament battle ${battleId} as spectator with connection ID ${state.connectionId}`);

    // Get the wallet address if available
    const walletAddress = localStorage.getItem('walletAddress');
    const userId = walletAddress || state.connectionId;

    const message: NetworkMessage = {
      type: MessageType.TournamentBattleJoin,
      data: {
        battleId,
        isSpectator: true,
        userId,
        clientTimestamp: Date.now() // Add client timestamp for latency calculation
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    try {
      console.log('Sending tournament battle join request:', message);
      const success = send(message); // Use the send function which has error handling
      console.log(`Tournament battle join request ${success ? 'sent successfully' : 'failed'}`);
      return success;
    } catch (error) {
      console.error('Error sending tournament battle join request:', error);
      return false;
    }
  };

  // Leave a tournament battle
  const leaveTournamentBattle = (battleId: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !state.connectionId) {
      console.warn('Cannot leave tournament battle: WebSocket not connected or no connection ID');
      return false;
    }

    const message: NetworkMessage = {
      type: MessageType.TournamentBattleLeave,
      data: {
        battleId,
        userId: state.connectionId // Ensure userId is included
      },
      timestamp: Date.now(),
      sender: state.connectionId
    };

    return send(message); // Use the send function which has error handling
  };

  // Connect on mount if autoConnect is true and we're in a multiplayer context
  useEffect(() => {
    // Check if we should auto-connect
    const shouldAutoConnect = options.autoConnect === true; // Default to false - only connect when explicitly requested

    // Check if we're on the home page - never auto-connect on home page
    const isHomePage = window.location.pathname === '/' || window.location.pathname === '';

    // Check if we're in a multiplayer context based on session storage
    const storedGameMode = sessionStorage.getItem('gameMode');
    const inGame = sessionStorage.getItem('inGame') !== 'false';
    const isMultiplayerMode = storedGameMode && storedGameMode !== 'single' && inGame;

    // Only auto-connect if:
    // 1. Auto-connect is explicitly enabled
    // 2. We're not on the home page
    // 3. We're in a multiplayer game mode
    if (shouldAutoConnect && !isHomePage && isMultiplayerMode) {
      console.log(`Auto-connecting to WebSocket server for ${storedGameMode} mode...`);
      connect();
    } else {
      if (shouldAutoConnect) {
        console.log(`Auto-connect conditions not met: isHomePage=${isHomePage}, isMultiplayerMode=${isMultiplayerMode}`);
      } else {
        console.log('Auto-connect disabled - not connecting automatically');
      }
    }

    // Clean up WebSocket connection on unmount
    return () => {
      if (wsRef.current) {
        console.log('Closing WebSocket connection on component unmount');
        wsRef.current.close(1000, 'Component unmounted'); // Use normal closure code
      }
    };
  }, []);

  // Add a ping interval to keep the connection alive
  useEffect(() => {
    if (!state.connected) return;

    // Send a ping every 30 seconds to keep the connection alive
    const pingInterval = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        console.log('Sending ping to keep connection alive');
        send({
          type: MessageType.Ping,
          data: { timestamp: Date.now() },
          timestamp: Date.now(),
          sender: state.connectionId || 'unknown'
        });
      }
    }, 30000); // 30 seconds

    return () => clearInterval(pingInterval);
  }, [state.connected]);

  return {
    ...state,
    connect,
    disconnect,
    send,
    joinArena,
    leaveArena,
    startGame,
    updatePlayerState,
    reportSpecterCapture,
    sendChatMessage,
    // New weapon effect methods
    createWeaponEffect,
    removeWeaponEffect,
    // New team management methods
    joinTeam,
    createTeam,
    requestTeamList,
    // Tournament battle methods
    joinTournamentBattle,
    leaveTournamentBattle
  };
}