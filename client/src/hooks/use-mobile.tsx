import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const checkIfMobile = () => {
      // Check for touch capability
      const hasTouch = 'ontouchstart' in window || 
                      navigator.maxTouchPoints > 0 ||
                      (navigator as any).msMaxTouchPoints > 0

      // Check for mobile user agent strings
      const userAgent = navigator.userAgent.toLowerCase()
      const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile|tablet/i.test(userAgent)
      
      // Check for small screen (additional check)
      const hasSmallScreen = window.innerWidth < MOBILE_BREAKPOINT
      
      // Chrome and Brave specific detection
      const isChrome = /chrome/i.test(userAgent) && !/edge|edg/i.test(userAgent)
      // Brave detection without directly accessing the non-standard property
      const isBrave = isChrome && (navigator as any).brave !== undefined
      
      // If it's Chrome or Brave on a small screen, consider it mobile
      if ((isChrome || isBrave) && hasSmallScreen) {
        return true
      }
      
      // Otherwise rely on standard detection methods
      return hasTouch && (isMobileUA || hasSmallScreen)
    }
    
    // Initial check
    setIsMobile(checkIfMobile())
    
    // Setup listeners for window resize
    const handleResize = () => {
      setIsMobile(checkIfMobile())
    }
    
    // Add multiple event listeners for better detection
    window.addEventListener("resize", handleResize)
    window.addEventListener("orientationchange", handleResize)
    
    // Clean up event listeners
    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("orientationchange", handleResize)
    }
  }, [])

  return !!isMobile
}
