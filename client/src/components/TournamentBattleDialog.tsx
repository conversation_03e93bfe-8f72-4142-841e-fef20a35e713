import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Trophy } from 'lucide-react';
import TournamentBattleView from './TournamentBattleView';
import { useMultiplayer } from '@/hooks/useMultiplayer';
import { TestTournamentBattle } from '@/game/battle/TestTournamentBattle';
import { RenderInstruction } from '@shared/schema';

interface TournamentBattleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tournamentId: number;
  battleId: string;
  isSpectator?: boolean;
  participantId?: number;
  gameEngine?: any;
}

const TournamentBattleDialog: React.FC<TournamentBattleDialogProps> = ({
  isOpen,
  onClose,
  tournamentId,
  battleId,
  isSpectator = true,
  participantId,
  gameEngine
}) => {
  const [isGamePaused, setIsGamePaused] = useState(false);
  const multiplayer = useMultiplayer();

  // Pause the game when the dialog opens and start test battle if needed
  useEffect(() => {
    if (isOpen && gameEngine) {
      // Pause the game
      gameEngine.pause();
      setIsGamePaused(true);

      // Enable spectator mode
      gameEngine.setSpectatorMode(true);

      // Start test battle if in test mode
      if (process.env.NODE_ENV === 'development' && !multiplayer.connected) {
        console.log('Starting test tournament battle');
        const testBattle = TestTournamentBattle.getInstance();
        testBattle.startBattle(battleId, tournamentId);

        // If not a spectator, set participantId to 1 for testing
        if (!isSpectator && participantId === undefined) {
          console.log('Setting participantId to 1 for testing');
          // We can't update the prop directly, but we can dispatch an event to update the state
          const event = new CustomEvent('tournamentBattle', {
            detail: {
              tournamentId,
              battleId,
              isSpectator: false,
              participantId: 1
            }
          });
          document.dispatchEvent(event);
        }
      }
    }

    // Resume the game when the dialog closes
    return () => {
      if (gameEngine && isGamePaused) {
        // Disable spectator mode
        gameEngine.setSpectatorMode(false);

        // Resume the game
        gameEngine.resume();
        setIsGamePaused(false);

        // Stop test battle if in test mode
        if (process.env.NODE_ENV === 'development' && !multiplayer.connected) {
          console.log('Stopping test tournament battle');
          const testBattle = TestTournamentBattle.getInstance();
          testBattle.stopBattle();
        }
      }
    };
  }, [isOpen, gameEngine, battleId, multiplayer.connected]);

  // Handle test render instructions
  useEffect(() => {
    // Only set up in development mode and when not connected to a real server
    if (process.env.NODE_ENV !== 'development' || multiplayer.connected) return;

    const handleTestRenderInstructions = (event: CustomEvent) => {
      const { battleId: eventBattleId, instructions } = event.detail;

      // Only process instructions for this battle
      if (eventBattleId !== battleId) return;

      // Process instructions if game engine is available
      if (gameEngine && instructions) {
        const renderInstructions = instructions as RenderInstruction[];
        gameEngine.processTournamentRenderInstructions(renderInstructions);
      }
    };

    // Add event listener
    document.addEventListener('tournamentRenderInstructions', handleTestRenderInstructions as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('tournamentRenderInstructions', handleTestRenderInstructions as EventListener);
    };
  }, [gameEngine, battleId, multiplayer.connected]);

  // Handle dialog close
  const handleClose = () => {
    // Disable spectator mode
    if (gameEngine) {
      gameEngine.setSpectatorMode(false);

      // Resume the game
      if (isGamePaused) {
        gameEngine.resume();
        setIsGamePaused(false);
      }
    }

    // Stop test battle if in test mode
    if (process.env.NODE_ENV === 'development' && !multiplayer.connected) {
      console.log('Stopping test tournament battle');
      const testBattle = TestTournamentBattle.getInstance();
      testBattle.stopBattle();
    }

    // Call the parent onClose
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center">
            <Trophy className="mr-2 h-5 w-5 text-yellow-500" />
            Tournament Battle
          </DialogTitle>
        </DialogHeader>

        <div className="h-[600px]">
          <TournamentBattleView
            tournamentId={tournamentId}
            battleId={battleId}
            onClose={handleClose}
            isSpectator={isSpectator}
            participantId={participantId}
            websocket={multiplayer.socket}
            gameEngine={gameEngine}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TournamentBattleDialog;
