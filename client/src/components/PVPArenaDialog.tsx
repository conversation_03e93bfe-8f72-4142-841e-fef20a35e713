import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { PowerupType } from '@shared/schema';
import { Swords, Trophy, Beaker, Loader2, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useWeb3 } from '@/contexts/Web3Context';
import { PetService } from '@/services/petService';
import { useLocation } from 'wouter';

interface PVPArenaDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onJoinTournament: () => void;
}

const PVPArenaDialog: React.FC<PVPArenaDialogProps> = ({ isOpen, onClose, onJoinTournament }) => {
  const [activeTab, setActiveTab] = useState('tournaments');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingPets, setIsFetchingPets] = useState(false);
  const { toast } = useToast();
  const { account, isConnected } = useWeb3();
  const [, setLocation] = useLocation();

  // State for user's NFT pets
  const [userPets, setUserPets] = useState<any[]>([]);

  // Tournament creation state
  const [tournamentName, setTournamentName] = useState('');
  const [entryFee, setEntryFee] = useState('2');
  const [maxParticipants, setMaxParticipants] = useState('8');
  const [selectedPet, setSelectedPet] = useState('');

  // Fetch user's NFT pets when the dialog opens
  useEffect(() => {
    const fetchUserPets = async () => {
      if (isConnected && account && isOpen) {
        setIsFetchingPets(true);
        try {
          const pets = await PetService.getPetSpectersByWallet(account);
          // Filter to only include pets with tokenId (NFT pets)
          const nftPets = pets.filter(pet => pet.tokenId);
          setUserPets(nftPets);

          // If we have pets but none selected, select the first one
          if (nftPets.length > 0 && !selectedPet) {
            setSelectedPet(nftPets[0].gameId);
          }
        } catch (error) {
          console.error('Error fetching user pets:', error);
          toast({
            title: 'Error',
            description: 'Failed to fetch your NFT pets. Please try again.',
            variant: 'destructive',
          });
        } finally {
          setIsFetchingPets(false);
        }
      }
    };

    fetchUserPets();
  }, [isConnected, account, isOpen, selectedPet, toast]);
  const [selectedPowerups, setSelectedPowerups] = useState<PowerupType[]>([]);
  const [isTestMode, setIsTestMode] = useState(false);

  // Tournament list state
  const [availableTournaments, setAvailableTournaments] = useState<any[]>([]);
  const [selectedTournamentId, setSelectedTournamentId] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle powerup selection
  const handlePowerupChange = (powerup: PowerupType) => {
    if (selectedPowerups.includes(powerup)) {
      setSelectedPowerups(selectedPowerups.filter(p => p !== powerup));
    } else {
      // Only allow selecting 2 powerups
      if (selectedPowerups.length < 2) {
        setSelectedPowerups([...selectedPowerups, powerup]);
      } else {
        // Replace the first selected powerup
        setSelectedPowerups([selectedPowerups[1], powerup]);
      }
    }
  };

  // Fetch available tournaments
  const fetchTournaments = async () => {
    setIsRefreshing(true);
    try {
      // Fetch tournaments from the server with cache busting
      const response = await fetch(`/api/tournament-battles/tournaments?_=${Date.now()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch tournaments');
      }

      const tournaments = await response.json();
      console.log('Fetched tournaments:', tournaments);

      setAvailableTournaments(tournaments.map((t: any) => ({
        id: t.id,
        name: t.name,
        entryFee: `${t.entryFee} MATIC`,
        participants: t.currentParticipants,
        maxParticipants: t.maxParticipants,
        currentParticipants: t.currentParticipants,
        status: t.status === 'registration' ? 'Registering' : t.status === 'in_progress' ? 'In Progress' : t.status
      })));

      // Clear selection if the selected tournament is no longer available
      if (selectedTournamentId && !tournaments.some(t => t.id === selectedTournamentId)) {
        setSelectedTournamentId(null);
      }

      setIsRefreshing(false);
    } catch (error) {
      console.error('Error fetching tournaments:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch tournaments. Please try again.',
        variant: 'destructive',
      });
      setIsRefreshing(false);
    }
  };

  // Create a new tournament
  const handleCreateTournament = async () => {
    if (selectedPowerups.length !== 2) {
      toast({
        title: 'Selection Required',
        description: 'Please select exactly 2 powerups for your pet.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedPet || selectedPet === 'none' || selectedPet === 'loading') {
      toast({
        title: 'Selection Required',
        description: 'Please select a valid NFT pet specter for the tournament.',
        variant: 'destructive',
      });
      return;
    }

    // Find the selected pet from userPets
    const pet = userPets.find(p => p.gameId === selectedPet);
    if (!pet) {
      toast({
        title: 'Invalid Selection',
        description: 'The selected pet is not a valid NFT pet.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Create a tournament on the server
      const response = await fetch('/api/tournament-battles/tournaments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: account || 'anonymous',
          name: tournamentName,
          maxParticipants: parseInt(maxParticipants),
          entryFee: entryFee,
          petId: selectedPet,
          powerups: selectedPowerups
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create tournament');
      }

      const data = await response.json();
      const tournamentId = data.tournamentId;

      toast({
        title: 'Tournament Created',
        description: `Your tournament "${tournamentName}" has been created successfully with your pet ${pet.name} automatically registered.`,
        duration: 5000
      });

      // Refresh tournaments list
      fetchTournaments();

      setIsLoading(false);
      // Do NOT navigate anywhere - just close the dialog
      onClose();
    } catch (error) {
      console.error('Error creating tournament:', error);
      toast({
        title: 'Error',
        description: 'Failed to create tournament. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Join an existing tournament
  const handleJoinTournament = async () => {
    if (!selectedTournamentId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a tournament to join.',
        variant: 'destructive',
      });
      return;
    }

    if (selectedPowerups.length !== 2) {
      toast({
        title: 'Selection Required',
        description: 'Please select exactly 2 powerups for your pet.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedPet || selectedPet === 'none' || selectedPet === 'loading') {
      toast({
        title: 'Selection Required',
        description: 'Please select a valid NFT pet specter for the tournament.',
        variant: 'destructive',
      });
      return;
    }

    // Find the selected pet from userPets
    const pet = userPets.find(p => p.gameId === selectedPet);
    if (!pet) {
      toast({
        title: 'Invalid Selection',
        description: 'The selected pet is not a valid NFT pet.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Join a tournament on the server
      const response = await fetch(`/api/tournament-battles/tournaments/${selectedTournamentId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: account || 'anonymous',
          petId: selectedPet,
          powerups: selectedPowerups
        })
      });

      if (!response.ok) {
        throw new Error('Failed to join tournament');
      }

      toast({
        title: 'Tournament Joined',
        description: 'You have successfully joined the tournament.',
        duration: 3000
      });

      setIsLoading(false);
      // Do NOT navigate anywhere - just close the dialog
      onClose();
    } catch (error) {
      console.error('Error joining tournament:', error);
      toast({
        title: 'Error',
        description: 'Failed to join tournament. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // State for active battles
  const [activeBattles, setActiveBattles] = useState<any[]>([]);
  const [selectedBattleId, setSelectedBattleId] = useState<string | null>(null);
  const [isViewingBattles, setIsViewingBattles] = useState(false);

  // Fetch active battles for a tournament
  const fetchActiveBattles = async (tournamentId: string) => {
    if (!tournamentId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a tournament to view battles.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Get active battles for this tournament
      const response = await fetch(`/api/tournament-battles/tournaments/${tournamentId}/battles`);

      if (!response.ok) {
        throw new Error('Failed to get active battles for tournament');
      }

      const battles = await response.json();
      console.log('Active battles for tournament:', battles);
      setActiveBattles(battles);
      setIsViewingBattles(true);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching active battles:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch active battles. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Join as spectator
  const handleJoinAsSpectator = async () => {
    if (!selectedBattleId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a battle to spectate.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Join the selected battle as spectator
      sessionStorage.setItem('battleId', selectedBattleId);

      // Clear any existing game state
      sessionStorage.removeItem('gameState');

      toast({
        title: 'Joining Battle',
        description: `Joining battle ${selectedBattleId} as spectator. You will be able to move around in the Coliseum Arena.`,
        duration: 3000
      });

      // Navigate to the dedicated PVP page with the battle ID
      setLocation(`/pvp?battleId=${selectedBattleId}`);

      // Close the dialog
      onClose();

      setIsLoading(false);
    } catch (error) {
      console.error('Error joining as spectator:', error);
      toast({
        title: 'Error',
        description: 'Failed to join as spectator. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Back to tournaments list
  const handleBackToTournaments = () => {
    setIsViewingBattles(false);
    setSelectedBattleId(null);
  };

  // State for test battles
  const [testBattles, setTestBattles] = useState<any[]>([]);
  const [selectedTestBattleId, setSelectedTestBattleId] = useState<string | null>(null);
  const [isViewingTestBattles, setIsViewingTestBattles] = useState(false);

  // Fetch active test battles
  const fetchTestBattles = async () => {
    setIsLoading(true);
    try {
      // Get active test battles
      const response = await fetch('/api/tournament-battles/active');

      if (!response.ok) {
        throw new Error('Failed to get active battles');
      }

      const battles = await response.json();
      console.log('Active test battles:', battles);
      setTestBattles(battles);
      setIsViewingTestBattles(true);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching test battles:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch test battles. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Start test mode
  const handleStartTestMode = async () => {
    // In test mode, we create a server-side battle with simulated NFT specters
    setIsLoading(true);
    try {
      // Create a test battle on the server
      const response = await fetch('/api/tournament-battles/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: account || 'anonymous'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create test battle');
      }

      const data = await response.json();
      const battleId = data.battleId;

      // Store battle ID in session storage
      sessionStorage.setItem('battleId', battleId);

      // Clear any existing game state
      sessionStorage.removeItem('gameState');

      // Navigate to the dedicated PVP page with the battle ID
      setLocation(`/pvp?battleId=${battleId}`);

      // Close the dialog
      onClose();

      toast({
        title: 'Test Battle Created',
        description: 'Joining the Coliseum Arena as a spectator. The battle will begin shortly.',
        duration: 5000
      });

      console.log(`Created test battle with ID: ${battleId}. Joining as spectator.`);
      setIsLoading(false);
    } catch (error) {
      console.error('Error starting test mode:', error);
      toast({
        title: 'Error',
        description: 'Failed to start test mode. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Join as spectator for test battle
  const handleJoinTestBattleAsSpectator = async () => {
    if (!selectedTestBattleId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a test battle to spectate.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Join the selected test battle as spectator
      sessionStorage.setItem('battleId', selectedTestBattleId);

      // Clear any existing game state
      sessionStorage.removeItem('gameState');

      toast({
        title: 'Joining Test Battle',
        description: `Joining battle ${selectedTestBattleId} as spectator. You will be able to move around in the Coliseum Arena.`,
        duration: 3000
      });

      // Navigate to the dedicated PVP page with the battle ID
      setLocation(`/pvp?battleId=${selectedTestBattleId}`);

      // Close the dialog
      onClose();

      setIsLoading(false);
    } catch (error) {
      console.error('Error joining as spectator:', error);
      toast({
        title: 'Error',
        description: 'Failed to join as spectator. Please try again.',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Back to test battles list
  const handleBackToTestBattles = () => {
    setIsViewingTestBattles(false);
    setSelectedTestBattleId(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gray-900 border-blue-400 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl text-blue-400">PVP Arena</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full grid grid-cols-3 bg-gray-800">
            <TabsTrigger value="tournaments" className="flex items-center">
              <Trophy className="h-4 w-4 mr-2" />
              Tournaments
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center">
              <Swords className="h-4 w-4 mr-2" />
              Create
            </TabsTrigger>
            <TabsTrigger value="test" className="flex items-center">
              <Beaker className="h-4 w-4 mr-2" />
              Test Mode
            </TabsTrigger>
          </TabsList>

          {/* Tournaments Tab */}
          <TabsContent value="tournaments" className="mt-4">
            <div className="space-y-4">
              {isViewingBattles ? (
                // Battle list view
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleBackToTournaments}
                      className="h-8"
                    >
                      ← Back to Tournaments
                    </Button>
                    <h3 className="text-sm font-medium">Active Battles</h3>
                  </div>

                  <div className="max-h-60 overflow-y-auto border border-gray-700 rounded-md p-2">
                    {activeBattles.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Swords className="h-10 w-10 mx-auto mb-2 opacity-30" />
                        <p className="text-sm">No active battles</p>
                        <p className="text-xs mt-1">Battles will start when enough participants have registered</p>
                      </div>
                    ) : (
                      activeBattles.map((battle) => (
                        <div
                          key={battle.id}
                          className={`p-3 mb-2 rounded-md cursor-pointer border ${
                            selectedBattleId === battle.id
                              ? 'border-blue-500 bg-blue-900/20'
                              : 'border-gray-700 hover:bg-gray-800'
                          }`}
                          onClick={() => setSelectedBattleId(battle.id)}
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">Battle #{battle.id.substring(0, 8)}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              battle.status === 'in_progress' ? 'bg-green-900 text-green-300' : 'bg-yellow-900 text-yellow-300'
                            }`}>
                              {battle.status === 'in_progress' ? 'In Progress' : 'Starting Soon'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-400 mt-1">
                            Participants: {battle.participants}
                          </div>
                          <div className="text-sm text-gray-400">
                            Spectators: {battle.spectators}
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  <Button
                    onClick={handleJoinAsSpectator}
                    disabled={isLoading || !selectedBattleId || activeBattles.length === 0}
                    className="w-full mt-4 bg-blue-600 hover:bg-blue-700"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Joining...
                      </>
                    ) : (
                      'Spectate Battle'
                    )}
                  </Button>
                </div>
              ) : (
                // Tournament list view
                <div>
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium">Available Tournaments</h3>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={fetchTournaments}
                      disabled={isRefreshing}
                      className="h-8"
                    >
                      {isRefreshing ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Refresh'
                      )}
                    </Button>
                  </div>

                  <div className="max-h-60 overflow-y-auto border border-gray-700 rounded-md p-2">
                    {availableTournaments.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Trophy className="h-10 w-10 mx-auto mb-2 opacity-30" />
                        <p className="text-sm">No tournaments available</p>
                        <p className="text-xs mt-1">Click refresh or create your own tournament</p>
                      </div>
                    ) : (
                      availableTournaments.map((tournament) => (
                        <div
                          key={tournament.id}
                          className={`p-3 mb-2 rounded-md cursor-pointer border ${
                            selectedTournamentId === tournament.id
                              ? 'border-blue-500 bg-blue-900/20'
                              : 'border-gray-700 hover:bg-gray-800'
                          }`}
                          onClick={() => setSelectedTournamentId(tournament.id)}
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">{tournament.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              tournament.status === 'Registering' ? 'bg-green-900 text-green-300' : 'bg-yellow-900 text-yellow-300'
                            }`}>
                              {tournament.status}
                            </span>
                          </div>
                          <div className="text-sm text-gray-400 mt-1">
                            Entry Fee: {tournament.entryFee}
                          </div>
                          <div className="text-sm text-gray-400">
                            Participants: {tournament.participants || tournament.currentParticipants || 0}/{tournament.maxParticipants}
                          </div>
                          <div className="flex justify-end mt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                fetchActiveBattles(tournament.id);
                              }}
                            >
                              View Battles
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  <div className="space-y-3 mt-4">
                    <div className="space-y-2">
                      <Label>Select Your Pet Specter</Label>
                      <Select value={selectedPet} onValueChange={setSelectedPet}>
                        <SelectTrigger className="bg-gray-800 border-gray-700">
                          <SelectValue placeholder="Choose a pet specter" />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-800 border-gray-700 text-white">
                          {isFetchingPets ? (
                            <SelectItem value="loading" disabled>
                              <Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
                              Loading pets...
                            </SelectItem>
                          ) : userPets.length > 0 ? (
                            userPets.map(pet => (
                              <SelectItem key={pet.gameId} value={pet.gameId}>
                                {pet.name} (Level {pet.level})
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="none" disabled>
                              No NFT pets found
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Select 2 Powerups</Label>
                      <div className="flex gap-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="fire"
                            checked={selectedPowerups.includes(PowerupType.FIRE)}
                            onCheckedChange={() => handlePowerupChange(PowerupType.FIRE)}
                          />
                          <label htmlFor="fire" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Fire
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="ice"
                            checked={selectedPowerups.includes(PowerupType.ICE)}
                            onCheckedChange={() => handlePowerupChange(PowerupType.ICE)}
                          />
                          <label htmlFor="ice" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Ice
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="shield"
                            checked={selectedPowerups.includes(PowerupType.SHIELD)}
                            onCheckedChange={() => handlePowerupChange(PowerupType.SHIELD)}
                          />
                          <label htmlFor="shield" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Shield
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 mt-4">
                    <Button
                      onClick={handleJoinTournament}
                      disabled={isLoading || !selectedTournamentId || !selectedPet || selectedPowerups.length !== 2}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Registering...
                        </>
                      ) : (
                        'Register Pet'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Create Tournament Tab */}
          <TabsContent value="create" className="mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Tournament Name</Label>
                <Input
                  placeholder="Enter tournament name"
                  value={tournamentName}
                  onChange={(e) => setTournamentName(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>

              <div className="space-y-2">
                <Label>Entry Fee (MATIC)</Label>
                <Select value={entryFee} onValueChange={setEntryFee}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue placeholder="Select entry fee" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700 text-white">
                    <SelectItem value="0.1">0.1 POL (Test)</SelectItem>
                    <SelectItem value="0.5">0.5 POL</SelectItem>
                    <SelectItem value="1">1 POL</SelectItem>
                    <SelectItem value="2">2 POL</SelectItem>
                    <SelectItem value="5">5 POL</SelectItem>
                    <SelectItem value="10">10 POL</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Maximum Participants</Label>
                <Select value={maxParticipants} onValueChange={setMaxParticipants}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue placeholder="Select max participants" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700 text-white">
                    <SelectItem value="4">4 participants</SelectItem>
                    <SelectItem value="8">8 participants</SelectItem>
                    <SelectItem value="12">12 participants</SelectItem>
                    <SelectItem value="16">16 participants</SelectItem>
                    <SelectItem value="20">20 participants</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Select Your Pet Specter</Label>
                <Select value={selectedPet} onValueChange={setSelectedPet}>
                  <SelectTrigger className="bg-gray-800 border-gray-700">
                    <SelectValue placeholder="Choose a pet specter" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700 text-white">
                    {isFetchingPets ? (
                      <SelectItem value="loading" disabled>
                        <Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
                        Loading pets...
                      </SelectItem>
                    ) : userPets.length > 0 ? (
                      userPets.map(pet => (
                        <SelectItem key={pet.gameId} value={pet.gameId}>
                          {pet.name} (Level {pet.level})
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>
                        No NFT pets found
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="bg-yellow-900/20 border border-yellow-500/50 rounded-md p-3">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-400">Tournament Creation</h4>
                    <p className="text-xs text-gray-300 mt-1">
                      You must select one of your NFT pet specters to enter into the tournament.
                      This pet will represent you in battles once the tournament begins.
                    </p>
                    <p className="text-xs text-gray-300 mt-1">
                      Each player can only enter one pet specter per tournament.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Select 2 Powerups</Label>
                <div className="flex gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="fire-create"
                      checked={selectedPowerups.includes(PowerupType.FIRE)}
                      onCheckedChange={() => handlePowerupChange(PowerupType.FIRE)}
                    />
                    <label htmlFor="fire-create" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Fire
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="ice-create"
                      checked={selectedPowerups.includes(PowerupType.ICE)}
                      onCheckedChange={() => handlePowerupChange(PowerupType.ICE)}
                    />
                    <label htmlFor="ice-create" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Ice
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="shield-create"
                      checked={selectedPowerups.includes(PowerupType.SHIELD)}
                      onCheckedChange={() => handlePowerupChange(PowerupType.SHIELD)}
                    />
                    <label htmlFor="shield-create" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Shield
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-2">
                <Checkbox
                  id="test-mode"
                  checked={isTestMode}
                  onCheckedChange={() => setIsTestMode(!isTestMode)}
                />
                <label htmlFor="test-mode" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Test Mode (Use smaller amounts for testing)
                </label>
              </div>

              <div className="bg-blue-900/20 border border-blue-500/50 rounded-md p-3 mt-2">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-400">Prize Distribution</h4>
                    <p className="text-xs text-gray-300 mt-1">
                      70% of the prize pool will be distributed to winners based on their performance.
                      30% will be used for platform development.
                    </p>
                    <p className="text-xs text-gray-300 mt-1">
                      Tournament requires between 4-20 participants and a 2 $POL entry fee.
                      Battles run automatically at 1-minute intervals once slots are filled.
                    </p>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCreateTournament}
                disabled={isLoading || !tournamentName || !selectedPet || selectedPowerups.length !== 2}
                className="w-full bg-blue-600 hover:bg-blue-700 mt-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Tournament'
                )}
              </Button>
            </div>
          </TabsContent>

          {/* Test Mode Tab */}
          <TabsContent value="test" className="mt-4">
            <div className="space-y-4">
              {isViewingTestBattles ? (
                // Test battle list view
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleBackToTestBattles}
                      className="h-8"
                    >
                      ← Back to Test Mode
                    </Button>
                    <h3 className="text-sm font-medium">Active Test Battles</h3>
                  </div>

                  <div className="max-h-60 overflow-y-auto border border-gray-700 rounded-md p-2">
                    {testBattles.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Swords className="h-10 w-10 mx-auto mb-2 opacity-30" />
                        <p className="text-sm">No active test battles</p>
                        <p className="text-xs mt-1">Create a test battle first</p>
                      </div>
                    ) : (
                      testBattles.map((battle) => (
                        <div
                          key={battle.id}
                          className={`p-3 mb-2 rounded-md cursor-pointer border ${
                            selectedTestBattleId === battle.id
                              ? 'border-blue-500 bg-blue-900/20'
                              : 'border-gray-700 hover:bg-gray-800'
                          }`}
                          onClick={() => setSelectedTestBattleId(battle.id)}
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">Test Battle #{battle.id.substring(0, 8)}</h4>
                            <span className={`text-xs px-2 py-1 rounded ${
                              battle.status === 'in_progress' ? 'bg-green-900 text-green-300' : 'bg-yellow-900 text-yellow-300'
                            }`}>
                              {battle.status === 'in_progress' ? 'In Progress' : 'Starting Soon'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-400 mt-1">
                            Participants: {battle.participants}
                          </div>
                          <div className="text-sm text-gray-400">
                            Spectators: {battle.spectators}
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  <Button
                    onClick={handleJoinTestBattleAsSpectator}
                    disabled={isLoading || !selectedTestBattleId || testBattles.length === 0}
                    className="w-full mt-4 bg-blue-600 hover:bg-blue-700"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Joining...
                      </>
                    ) : (
                      'Spectate Test Battle'
                    )}
                  </Button>
                </div>
              ) : (
                // Test mode info view
                <div>
                  <div className="bg-yellow-900/20 border border-yellow-500/50 rounded-md p-3">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-400">Test Mode</h4>
                        <p className="text-xs text-gray-300 mt-1">
                          This mode allows you to test PVP battles with simulated NFT specters without spending real MATIC.
                          You will join as a spectator to observe battles in the Coliseum Arena.
                        </p>
                        <p className="text-xs text-gray-300 mt-1">
                          The system will automatically create two simulated pet specters with random stats and powerups.
                          No pet selection is required for test mode.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 mt-4">
                    <Button
                      onClick={handleStartTestMode}
                      disabled={isLoading}
                      className="flex-1 bg-yellow-600 hover:bg-yellow-700"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Test Battle'
                      )}
                    </Button>
                    <Button
                      onClick={fetchTestBattles}
                      disabled={isLoading}
                      variant="outline"
                      className="flex-1"
                    >
                      View Active Battles
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default PVPArenaDialog;
