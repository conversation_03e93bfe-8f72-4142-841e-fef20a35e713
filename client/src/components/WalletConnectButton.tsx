import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useWeb3 } from '@/contexts/Web3Context';
import { Wallet, LogOut, Loader2, TestTube } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const WalletConnectButton: React.FC = () => {
  const { isConnected, isConnecting, account, balance, isTestMode, toggleTestMode, connectWallet, disconnectWallet } = useWeb3();

  // Format account address for display
  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  if (isConnected && account) {
    return (
      <div className="flex items-center gap-2">
        <div className="hidden md:flex flex-col items-end mr-2">
          <span className="text-sm font-medium">{formatAddress(account)}</span>
          <span className="text-xs text-muted-foreground">{balance ? `${parseFloat(balance).toFixed(4)} MATIC` : 'Loading...'}</span>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center gap-2 mr-1"
                disabled={true}
              >
                <TestTube className="h-4 w-4 text-green-400" />
                <span className="hidden md:inline">Test Mode</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Currently in test mode - all transactions are simulated without spending real MATIC</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button
          variant="outline"
          size="sm"
          onClick={disconnectWallet}
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          <span className="hidden md:inline">Disconnect</span>
        </Button>
      </div>
    );
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={connectWallet}
      disabled={isConnecting}
      className="flex items-center gap-2"
    >
      {isConnecting ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Wallet className="h-4 w-4" />
      )}
      <span>Connect Wallet</span>
    </Button>
  );
};

export default WalletConnectButton;
