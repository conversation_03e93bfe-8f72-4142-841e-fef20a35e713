import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface StatusIssuesDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const StatusIssuesDialog: React.FC<StatusIssuesDialogProps> = ({
  isOpen,
  onClose
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="bg-gray-900 text-white border-blue-400 max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="title-font text-xl text-blue-400">Status & Known Issues</DialogTitle>
          <DialogDescription className="text-gray-300">
            Current status of SpecterShift development and known issues
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 my-4">
          <ol className="list-decimal pl-5 space-y-3">
            <li>NFT Support & Pet Specters is Not Fully Implemented (WIP). Demo "Off-Grid" specters can be generated currently as an early demo.</li>
            <li>May be a bug in MerchGenie Kiosk pause states - if it seems to hang and not properly resume the game, hit ESC key to pause and then click resume.</li>
            <li>PVP Is Disabled (WIP) - Tournaments and CO-OP Modes In Development.</li>
            <li>Note: Enemy Specters are elusive at first. This is intentional, so that you have a minute to get familiar with the controls etc.</li>
            <li>Weapon effects could be improved. Transparencies in particle effects do not properly blend with the rest of the weapon effect, making it look janky.</li>
            <li>Pet specter leveling and training is still an early implementation which is not as intended, and is incomplete.</li>
                        <li>Dungeon and quests is incomplete. Also pet specters spawn in the dungeon, outside the walls :(</li>
            <li>Movement controls aren't quite where I would like them to be but still playable on Desktop. Use the grappling hook (G) or Jetpack (SHIFT) if you need to.</li>
            <li>Email <NAME_EMAIL> to report any bugs not listed here</li>
            <li>Contribute to my Development costs: 0x4716910cbCD8d5F790F0817490d3fA2c5B3a7467</li>
            <li>Made Possible By Using Resources from Replit/Augment Code,(100% Free free trials) Plus pro tier for Cursor, free tier of Riffusion, Groq, and paid tier of Fal.ai.</li>
          </ol>
        </div>
        
        <DialogFooter>
          <Button 
            onClick={onClose}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StatusIssuesDialog;
