import React from 'react';
import { Button } from '@/components/ui/button';
import { useWeb3 } from '@/contexts/Web3Context';
import { useAuth } from '@/contexts/AuthContext';
import { Wallet, LogOut, User } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useLocation } from 'wouter';

const DualLoginButton: React.FC = () => {
  const { isConnected, account, balance } = useWeb3();
  const { isLoggedIn, authMethod, currentUser, logout } = useAuth();
  const [, setLocation] = useLocation();

  // Format account address for display
  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // Handle logout
  const handleLogout = async () => {
    await logout();
  };

  // If logged in with either method, show user info and logout button
  if (isLoggedIn && currentUser) {
    return (
      <div className="flex items-center gap-2">
        <div className="hidden md:flex flex-col items-end mr-2">
          <span className="text-sm font-medium">
            {authMethod === 'wallet' && account ? formatAddress(account) : currentUser.displayName || currentUser.name}
          </span>
          <span className="text-xs text-muted-foreground">
            {authMethod === 'wallet' ?
              (balance ? `${parseFloat(balance).toFixed(4)} MATIC` : 'Loading...') :
              `via ${authMethod === 'orangeID' ? 'Orange ID' : 'Wallet'}`
            }
          </span>
        </div>

        {/* Show wallet icon for wallet users */}
        {authMethod === 'wallet' && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="flex items-center gap-2 mr-1"
                >
                  <Wallet className="h-4 w-4 text-green-400" />
                  <span className="hidden md:inline">Wallet</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Connected with wallet: {account}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          <span className="hidden md:inline">Logout</span>
        </Button>
      </div>
    );
  }

  // If not logged in, show login button
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setLocation('/')}
      className="flex items-center gap-2"
    >
      <User className="h-4 w-4" />
      <span>Login</span>
    </Button>
  );
};

export default DualLoginButton;
