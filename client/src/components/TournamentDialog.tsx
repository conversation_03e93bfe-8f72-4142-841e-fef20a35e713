import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useWeb3 } from '@/contexts/Web3Context';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Trophy, Calendar, Users, Coins } from 'lucide-react';

interface Tournament {
  id: number;
  name: string;
  description: string;
  entryFee: string;
  prizePool: string;
  maxParticipants: number;
  currentParticipants: number;
  status: string;
  startTime: string;
  endTime: string;
}

interface TournamentDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const TournamentDialog: React.FC<TournamentDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { isConnected, account, connectWallet, sendTransaction } = useWeb3();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null);

  // Mock tournaments data - in a real implementation, this would come from an API
  useEffect(() => {
    const mockTournaments: Tournament[] = [
      {
        id: 1,
        name: "Weekly Specter Showdown",
        description: "Weekly tournament for all pet specter owners. Top players win 50% of the prize pool, with 50% going to development.",
        entryFee: "2",
        prizePool: "64", // 32 participants * 2 MATIC
        maxParticipants: 32,
        currentParticipants: 18,
        status: "registration",
        startTime: new Date(Date.now() + ********).toISOString(), // Tomorrow
        endTime: new Date(Date.now() + *********).toISOString(), // Day after tomorrow
      },
      {
        id: 2,
        name: "Elite Phantom League",
        description: "High stakes tournament for experienced players. 50% of entry fees go to winners, 50% to development.",
        entryFee: "2",
        prizePool: "32", // 16 participants * 2 MATIC
        maxParticipants: 16,
        currentParticipants: 7,
        status: "registration",
        startTime: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
        endTime: new Date(Date.now() + 432000000).toISOString(), // 5 days from now
      },
      {
        id: 3,
        name: "Beginner's Battle",
        description: "Tournament for new players with pet specters level 5 or below. 50% of entry fees to winners, 50% to development.",
        entryFee: "2",
        prizePool: "48", // 24 participants * 2 MATIC
        maxParticipants: 24,
        currentParticipants: 12,
        status: "registration",
        startTime: new Date(Date.now() + *********).toISOString(), // 2 days from now
        endTime: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
      }
    ];

    setTournaments(mockTournaments);
  }, []);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Handle tournament registration
  const handleRegister = async (tournament: Tournament) => {
    if (!isConnected) {
      toast({
        title: "Wallet Not Connected",
        description: "Please connect your wallet to register for tournaments",
        variant: "destructive",
        duration: 3000
      });
      return;
    }

    setIsLoading(true);

    try {
      // Create registration data
      const registrationData = {
        tournamentId: tournament.id,
        entryFee: tournament.entryFee,
        timestamp: Date.now()
      };

      // Send transaction
      const txHash = await sendTransaction(tournament.entryFee, JSON.stringify(registrationData));

      if (txHash) {
        toast({
          title: "Registration Successful",
          description: `You've registered for ${tournament.name}!`,
          duration: 5000
        });

        // Update tournament data (in a real app, this would be done via API)
        setTournaments(tournaments.map(t => {
          if (t.id === tournament.id) {
            return {
              ...t,
              currentParticipants: t.currentParticipants + 1
            };
          }
          return t;
        }));
      } else {
        toast({
          title: "Registration Failed",
          description: "Failed to register for tournament. Please try again.",
          variant: "destructive",
          duration: 5000
        });
      }
    } catch (error: any) {
      console.error("Error registering for tournament:", error);
      toast({
        title: "Registration Error",
        description: error.message || "An error occurred during registration.",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center">
            <Trophy className="mr-2 h-5 w-5 text-yellow-500" />
            Tournaments
          </DialogTitle>
          <DialogDescription>
            Register for tournaments to compete with your pet specters in the Coliseum Arena. Entry fee is 2 MATIC, with 50% of the pool distributed to winners and 50% for development.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
          {tournaments.map(tournament => (
            <div
              key={tournament.id}
              className="border border-gray-700 rounded-lg p-4 hover:bg-gray-800/50 transition-colors"
            >
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-bold">{tournament.name}</h3>
                  <p className="text-sm text-gray-400 mt-1">{tournament.description}</p>
                </div>
                <div className="bg-blue-900/50 px-3 py-1 rounded-full text-xs text-blue-300 uppercase">
                  {tournament.status}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                  <div>
                    <div className="text-gray-400">Start Time</div>
                    <div>{formatDate(tournament.startTime)}</div>
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Users className="h-4 w-4 mr-2 text-gray-400" />
                  <div>
                    <div className="text-gray-400">Participants</div>
                    <div>{tournament.currentParticipants} / {tournament.maxParticipants}</div>
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Coins className="h-4 w-4 mr-2 text-gray-400" />
                  <div>
                    <div className="text-gray-400">Entry Fee</div>
                    <div>{tournament.entryFee} $POL</div>
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Trophy className="h-4 w-4 mr-2 text-yellow-500" />
                  <div>
                    <div className="text-gray-400">Prize Pool</div>
                    <div className="text-yellow-400">{tournament.prizePool} $POL (50% to winners)</div>
                  </div>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <Button
                  onClick={() => handleRegister(tournament)}
                  disabled={isLoading || tournament.currentParticipants >= tournament.maxParticipants}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Registering...
                    </>
                  ) : tournament.currentParticipants >= tournament.maxParticipants ? (
                    'Full'
                  ) : (
                    'Register'
                  )}
                </Button>
              </div>
            </div>
          ))}

          {tournaments.length === 0 && (
            <div className="text-center py-8">
              <Trophy className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
              <p className="text-gray-500">No tournaments available at the moment.</p>
              <p className="text-gray-600 text-sm mt-2">Check back later for upcoming tournaments!</p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TournamentDialog;
