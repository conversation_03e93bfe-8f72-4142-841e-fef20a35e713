import React, { ReactNode } from 'react';
import { Button } from './button';
import { motion } from 'framer-motion';

interface GameButtonProps {
  onClick: () => void;
  label: string;
  icon?: ReactNode;
  primary?: boolean;
  disabled?: boolean;
}

export const GameButton: React.FC<GameButtonProps> = ({ 
  onClick, 
  label, 
  icon, 
  primary = false,
  disabled = false
}) => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="w-full"
    >
      <Button
        onClick={onClick}
        disabled={disabled}
        className={`
          w-full py-6 font-semibold text-lg title-font tracking-widest
          ${primary 
            ? 'bg-blue-600 hover:bg-blue-700 text-white border-2 border-blue-400' 
            : 'bg-gray-800 hover:bg-gray-700 text-blue-300 border border-blue-500'}
          transition-all duration-200 flex items-center justify-center gap-2
        `}
      >
        {icon && <span>{icon}</span>}
        {label}
      </Button>
    </motion.div>
  );
};
