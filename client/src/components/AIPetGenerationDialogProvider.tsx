import React, { useEffect, useState } from 'react';
import AIPetGenerationDialog from './AIPetGenerationDialog';

/**
 * Provider component that listens for AI pet generation events
 */
const AIPetGenerationDialogProvider: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [onPetGenerated, setOnPetGenerated] = useState<(tokenId: string) => void>(() => () => {});
  const [gameEngine, setGameEngine] = useState<any>(null);

  useEffect(() => {
    // Listen for the showAIPetGenerationDialog event
    const handleShowAIPetGenerationDialog = (event: CustomEvent) => {
      const { isOpen, onPetGenerated, gameEngine } = event.detail;

      // Make sure the game is paused before showing the dialog
      if (gameEngine && typeof gameEngine.pause === 'function') {
        gameEngine.pause();
      }

      // Dispatch an event to notify the Game component that the AI pet dialog is open
      const aiPetDialogOpenedEvent = new CustomEvent('aiPetDialogOpened');
      document.dispatchEvent(aiPetDialogOpenedEvent);

      setIsOpen(isOpen);
      setOnPetGenerated(() => onPetGenerated);
      setGameEngine(gameEngine);
    };

    // Add event listener
    document.addEventListener('showAIPetGenerationDialog', handleShowAIPetGenerationDialog as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('showAIPetGenerationDialog', handleShowAIPetGenerationDialog as EventListener);
    };
  }, []);

  // Handle dialog close
  const handleClose = () => {
    setIsOpen(false);

    // Dispatch an event to notify the Game component that the AI pet dialog is closed
    const aiPetDialogClosedEvent = new CustomEvent('aiPetDialogClosed');
    document.dispatchEvent(aiPetDialogClosedEvent);

    // Let the Game component handle resuming the game
    // This ensures proper synchronization with the game's pause state
  };

  return (
    <AIPetGenerationDialog
      isOpen={isOpen}
      onClose={handleClose}
      onPetGenerated={(tokenId) => {
        onPetGenerated(tokenId);
        // Game resumption is now handled by the Game component
      }}
    />
  );
};

export default AIPetGenerationDialogProvider;
