import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/contexts/AuthContext';

// Simple component to ensure pages are protected from unauthenticated access
const AuthProtection = () => {
  const { isLoggedIn, isAuthLoading, checkSessionExpiry } = useAuth();
  const [, setLocation] = useLocation();
  const [checkedStorage, setCheckedStorage] = useState(false);

  useEffect(() => {
    // Check for session expiry first
    checkSessionExpiry();

    // Helper function to validate stored Orange ID user
    const isValidOrangeIDUser = (userString: string): boolean => {
      try {
        const parsedUser = JSON.parse(userString);
        return parsedUser && parsedUser.id && (parsedUser.email || parsedUser.displayName);
      } catch {
        return false;
      }
    };

    // First check localStorage directly with validation
    const storedUser = localStorage.getItem('orangeIDUser');
    const walletAddress = localStorage.getItem('walletAddress');

    const hasValidOrangeID = storedUser &&
                            storedUser !== 'undefined' &&
                            storedUser !== 'null' &&
                            isValidOrangeIDUser(storedUser);
    const hasValidWallet = walletAddress && walletAddress !== 'undefined' && walletAddress !== 'null';
    const hasStoredAuth = hasValidOrangeID || hasValidWallet;

    setCheckedStorage(true);

    // If auth is still loading, wait for it
    if (isAuthLoading) {
      console.log('AuthProtection: Auth state loading, waiting...');
      return;
    }

    // If no auth in context and no valid stored auth, redirect to login
    if (!isLoggedIn && !hasStoredAuth && !window.location.pathname.includes('/auth/callback')) {
      console.log('AuthProtection: User not authenticated, redirecting to login page');
      setLocation('/');
    }
  }, [isLoggedIn, isAuthLoading, setLocation, checkSessionExpiry]);

  // This component doesn't render anything
  return null;
};

export default AuthProtection;