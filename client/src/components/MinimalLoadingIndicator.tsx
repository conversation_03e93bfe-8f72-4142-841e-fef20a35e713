import React from 'react';

interface MinimalLoadingIndicatorProps {
  size?: 'small' | 'medium' | 'large';
  fixed?: boolean;
}

/**
 * A minimal loading indicator that doesn't take over the screen
 * This is used as a fallback for Suspense to avoid a full loading screen
 */
const MinimalLoadingIndicator: React.FC<MinimalLoadingIndicatorProps> = ({ 
  size = 'small',
  fixed = true
}) => {
  const dotSizes = {
    small: 'w-4 h-4',
    medium: 'w-6 h-6',
    large: 'w-8 h-8'
  };
  
  const spacing = {
    small: 'space-x-2',
    medium: 'space-x-3',
    large: 'space-x-4'
  };
  
  const containerClass = fixed 
    ? "fixed bottom-4 right-4 z-50 bg-black bg-opacity-70 p-2 rounded-md shadow-lg"
    : "bg-black bg-opacity-70 p-2 rounded-md shadow-lg";
  
  return (
    <div className={containerClass}>
      <div className={`flex items-center ${spacing[size]}`}>
        <div className={`${dotSizes[size]} rounded-full bg-blue-500 animate-pulse`}></div>
        <div className={`${dotSizes[size]} rounded-full bg-blue-500 animate-pulse`} style={{ animationDelay: '0.2s' }}></div>
        <div className={`${dotSizes[size]} rounded-full bg-blue-500 animate-pulse`} style={{ animationDelay: '0.4s' }}></div>
      </div>
    </div>
  );
};

export default MinimalLoadingIndicator;
