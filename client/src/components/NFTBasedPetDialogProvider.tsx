import React, { useEffect, useState } from 'react';
import NFTBasedPetDialog from './NFTBasedPetDialog';

/**
 * Provider component that listens for NFT-based pet generation events
 */
const NFTBasedPetDialogProvider: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [onPetGenerated, setOnPetGenerated] = useState<(tokenId: string) => void>(() => () => {});
  const [gameEngine, setGameEngine] = useState<any>(null);

  useEffect(() => {
    // Listen for the showNFTBasedPetDialog event
    const handleShowNFTBasedPetDialog = (event: CustomEvent) => {
      const { isOpen, onPetGenerated, gameEngine } = event.detail;

      // Make sure the game is paused before showing the dialog
      if (gameEngine && typeof gameEngine.pause === 'function') {
        gameEngine.pause();
      }

      // Dispatch an event to notify the Game component that the NFT based pet dialog is open
      const nftBasedPetDialogOpenedEvent = new CustomEvent('aiPetDialogOpened');
      document.dispatchEvent(nftBasedPetDialogOpenedEvent);

      setIsOpen(isOpen);
      setOnPetGenerated(() => onPetGenerated);
      setGameEngine(gameEngine);
    };

    // Add event listener
    document.addEventListener('showNFTBasedPetDialog', handleShowNFTBasedPetDialog as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('showNFTBasedPetDialog', handleShowNFTBasedPetDialog as EventListener);
    };
  }, []);

  // Handle dialog close
  const handleClose = () => {
    setIsOpen(false);

    // Dispatch an event to notify the Game component that the NFT based pet dialog is closed
    const nftBasedPetDialogClosedEvent = new CustomEvent('aiPetDialogClosed');
    document.dispatchEvent(nftBasedPetDialogClosedEvent);

    // Let the Game component handle resuming the game
    // This ensures proper synchronization with the game's pause state
  };

  return (
    <NFTBasedPetDialog
      isOpen={isOpen}
      onClose={handleClose}
      onPetGenerated={(tokenId) => {
        onPetGenerated(tokenId);
        // Game resumption is now handled by the Game component
      }}
    />
  );
};

export default NFTBasedPetDialogProvider;
