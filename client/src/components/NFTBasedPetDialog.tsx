import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Wand2 } from 'lucide-react';
import { useWeb3 } from '@/contexts/Web3Context';
import { PetService } from '@/services/petService';
import { PetGeneratorService } from '@/services/petGeneratorService';
import NFTBrowser from './NFTBrowser';

// Interface for NFT item
interface NFTItem {
  contractAddress: string;
  tokenId: string;
  name: string;
  symbol: string;
  image?: string;
  attributes?: any[];
  metadata?: any;
}

interface NFTBasedPetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPetGenerated: (tokenId: string) => void;
}

const NFTBasedPetDialog: React.FC<NFTBasedPetDialogProps> = ({
  isOpen,
  onClose,
  onPetGenerated
}) => {
  const { toast } = useToast();
  const { account, mintNFT } = useWeb3();
  const [isNFTBrowserOpen, setIsNFTBrowserOpen] = useState(false);
  const [selectedNFT, setSelectedNFT] = useState<NFTItem | null>(null);
  const [generatedPet, setGeneratedPet] = useState<any | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isMinting, setIsMinting] = useState(false);

  // Open NFT browser
  const handleOpenNFTBrowser = () => {
    // Create a custom event to open the NFT browser dialog
    const event = new CustomEvent('showNFTBrowserDialog', {
      detail: {
        isOpen: true,
        onSelectNFT: handleSelectNFT,
        gameEngine: window.gameEngine, // Use the global gameEngine reference
        supportedCollections: [] // Empty array to check all NFTs
      }
    });
    document.dispatchEvent(event);
  };

  // Handle NFT selection
  const handleSelectNFT = (nft: NFTItem) => {
    setSelectedNFT(nft);
    setGeneratedPet(null); // Clear any previously generated pet
    // No need to set isNFTBrowserOpen to false as the dialog is now handled by the provider
  };

  // Generate pet from selected NFT
  const handleGeneratePet = () => {
    if (!selectedNFT) {
      toast({
        title: 'No NFT Selected',
        description: 'Please select an NFT to generate a pet',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Generate pet data based on the NFT
      const petData = PetGeneratorService.generatePetFromNFT(selectedNFT);
      setGeneratedPet(petData);

      toast({
        title: 'Pet Generated',
        description: `Generated a ${petData.type.name} pet based on your NFT!`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error generating pet:', error);
      toast({
        title: 'Generation Failed',
        description: 'Failed to generate pet from NFT. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Mint the generated pet as an NFT
  const handleMintPet = async () => {
    if (!generatedPet || !account) {
      toast({
        title: 'Cannot Mint',
        description: 'Please generate a pet first and ensure your wallet is connected',
        variant: 'destructive'
      });
      return;
    }

    setIsMinting(true);

    try {
      // Generate a unique game ID for the pet
      const gameId = `pet-${Date.now()}`;

      // Create metadata for the NFT
      const metadata = {
        name: generatedPet.name,
        description: `A ${generatedPet.type.name} Pet Specter from Specter Shift game, generated from NFT ${selectedNFT?.contractAddress}/${selectedNFT?.tokenId}`,
        image: generatedPet.type.texture,
        attributes: [
          {
            trait_type: 'Type',
            value: generatedPet.type.name
          },
          {
            trait_type: 'Source NFT Contract',
            value: selectedNFT?.contractAddress
          },
          {
            trait_type: 'Source NFT ID',
            value: selectedNFT?.tokenId
          },
          ...generatedPet.traits.map((trait: any) => ({
            trait_type: trait.type,
            value: trait.level
          }))
        ]
      };

      // Calculate price based on the generated pet
      const price = PetGeneratorService.getPriceForGeneratedPet(generatedPet);

      // Create pet data for database
      const petData = {
        gameId,
        name: generatedPet.name,
        specterType: generatedPet.type.name,
        walletAddress: account,
        level: 1,
        xp: 0,
        traits: generatedPet.traits,
        stats: generatedPet.stats,
        metadata: {
          sourceNFT: {
            contractAddress: selectedNFT?.contractAddress,
            tokenId: selectedNFT?.tokenId,
            name: selectedNFT?.name
          }
        }
      };

      try {
        // First create the pet in the database
        await PetService.createPetSpecter(petData);

        // Then mint the NFT
        const tokenURI = JSON.stringify(metadata);
        const tokenId = await mintNFT(tokenURI, price, petData);

        if (tokenId) {
          // Link the pet to the NFT
          await PetService.linkPetSpecterToNFT(gameId, tokenId, account);

          // Call the success callback
          onPetGenerated(tokenId);

          // Close the dialog
          onClose();
        }
      } catch (error) {
        console.error('Error creating pet or minting NFT:', error);
        toast({
          title: 'Error',
          description: 'Failed to create pet or mint NFT. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error minting pet:', error);
      toast({
        title: 'Minting Failed',
        description: 'Failed to mint pet. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsMinting(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Generate Pet from Your NFT</DialogTitle>
            <DialogDescription>
              Select one of your NFTs to generate a unique Pet Specter with traits based on your NFT's attributes.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {selectedNFT ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-20 overflow-hidden rounded-lg">
                    {selectedNFT.image ? (
                      <img
                        src={selectedNFT.image}
                        alt={selectedNFT.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span>No Image</span>
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium">{selectedNFT.name}</h3>
                    <p className="text-sm text-gray-500">#{selectedNFT.tokenId}</p>
                    <p className="text-xs text-gray-400">{selectedNFT.contractAddress.substring(0, 8)}...</p>
                  </div>
                </div>

                <Button
                  onClick={handleGeneratePet}
                  disabled={isGenerating}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="mr-2 h-4 w-4" />
                      Generate Pet from this NFT
                    </>
                  )}
                </Button>

                {generatedPet && (
                  <div className="w-full border rounded-lg p-4 mt-4">
                    <h3 className="font-medium text-center mb-2">{generatedPet.name}</h3>
                    <div className="flex justify-center mb-4">
                      <div
                        className="w-16 h-16 rounded-full"
                        style={{
                          backgroundColor: generatedPet.type.color,
                          boxShadow: `0 0 15px ${generatedPet.type.color}`
                        }}
                      />
                    </div>
                    <p className="text-center mb-2">Type: {generatedPet.type.name}</p>

                    <div className="grid grid-cols-2 gap-2 mb-4">
                      <div>
                        <h4 className="text-sm font-medium">Traits:</h4>
                        <ul className="text-xs">
                          {generatedPet.traits.map((trait: any) => (
                            <li key={trait.type}>
                              {trait.type}: Level {trait.level}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium">Stats:</h4>
                        <ul className="text-xs">
                          <li>HP: {generatedPet.stats.health}</li>
                          <li>Attack: {generatedPet.stats.attackPower}</li>
                          <li>Defense: {generatedPet.stats.defenseValue}</li>
                          <li>Speed: {generatedPet.stats.speed}</li>
                        </ul>
                      </div>
                    </div>

                    <p className="text-center text-sm mb-4">
                      Price: {PetGeneratorService.getPriceForGeneratedPet(generatedPet)} MATIC
                    </p>

                    <Button
                      onClick={handleMintPet}
                      disabled={isMinting}
                      className="w-full"
                    >
                      {isMinting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Minting...
                        </>
                      ) : (
                        'Mint this Pet Specter'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-4">
                <p className="text-center">
                  Select one of your NFTs to use as a seed for generating a unique Pet Specter.
                  Different NFTs will produce different types of specters with unique traits.
                </p>
                <Button onClick={handleOpenNFTBrowser}>
                  Browse Your NFTs
                </Button>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* NFT Browser Dialog is now handled by NFTBrowserDialogProvider */}
    </>
  );
};

export default NFTBasedPetDialog;
