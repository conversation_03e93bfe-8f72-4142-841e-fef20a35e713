import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useWeb3 } from '@/contexts/Web3Context';
import { useAuth } from '@/contexts/AuthContext';
import { usePvpAccess } from '@/contexts/PvpAccessContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { PetService } from '@/services/petService';

interface PVPRequirementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: () => void;
}

const PVPRequirementDialog: React.FC<PVPRequirementDialogProps> = ({
  isOpen,
  onClose,
  onProceed,
}) => {
  const { isConnected: isWalletConnected, account, connectWallet } = useWeb3();
  const { isLoggedIn, authMethod, walletAddress } = useAuth();
  const { isPvpEnabled } = usePvpAccess();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [hasNFTPet, setHasNFTPet] = useState(false);
  const [petCount, setPetCount] = useState(0);

  // Determine if we have a wallet connection (either directly or via OrangeID)
  const effectiveWalletAddress = walletAddress || account;
  const isConnected = isLoggedIn && (authMethod === 'wallet' || (authMethod === 'orangeID' && walletAddress));

  // Check if the user has an NFT pet when the wallet is connected
  useEffect(() => {
    const checkForNFTPets = async () => {
      if (isConnected && effectiveWalletAddress) {
        setIsLoading(true);
        try {
          const pets = await PetService.getPetSpectersByWallet(effectiveWalletAddress);
          const nftPets = pets.filter(pet => pet.tokenId);
          setHasNFTPet(nftPets.length > 0);
          setPetCount(nftPets.length);
        } catch (error) {
          console.error('Error checking for NFT pets:', error);
          setHasNFTPet(false);
          setPetCount(0);
        } finally {
          setIsLoading(false);
        }
      } else {
        setHasNFTPet(false);
        setPetCount(0);
      }
    };

    checkForNFTPets();
  }, [isConnected, effectiveWalletAddress]);

  // Handle connect wallet button click
  const handleConnectWallet = async () => {
    setIsConnecting(true);
    try {
      await connectWallet();
    } catch (error) {
      console.error('Error connecting wallet:', error);
      toast({
        title: 'Connection Failed',
        description: 'Failed to connect wallet. Please try again.',
        variant: 'destructive',
        duration: 3000
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle proceed button click
  const handleProceed = () => {
    // Allow access if user has NFT pets OR they have the special wallet
    if (hasNFTPet || isPvpEnabled) {
      onProceed();
    } else {
      toast({
        title: 'NFT Pet Required',
        description: 'You need to own at least one NFT pet to enter PVP mode.',
        variant: 'destructive',
        duration: 3000
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>PVP Mode Requirements</DialogTitle>
          <DialogDescription>
            You need to own at least one NFT pet specter to enter PVP mode.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-center mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400 mb-2">PVP Arena</div>
              <div className="text-sm text-gray-400">
                Battle your pet specters against other players and win MATIC!
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          ) : isConnected ? (
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-sm text-gray-400">Wallet</div>
                  <div className="text-white font-medium">
                    {effectiveWalletAddress?.substring(0, 6)}...{effectiveWalletAddress?.substring(38)}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-400">NFT Pets</div>
                  <div className={`font-bold ${hasNFTPet ? 'text-green-400' : 'text-red-400'}`}>
                    {petCount}
                  </div>
                </div>
              </div>

              {isPvpEnabled ? (
                <div className="mt-4 bg-green-900 bg-opacity-30 border border-green-500 rounded p-3 text-green-300 text-sm">
                  You have special access to PVP mode!
                </div>
              ) : hasNFTPet ? (
                <div className="mt-4 bg-green-900 bg-opacity-30 border border-green-500 rounded p-3 text-green-300 text-sm">
                  You have {petCount} NFT pet{petCount !== 1 ? 's' : ''}! You can enter PVP mode.
                </div>
              ) : (
                <div className="mt-4 bg-red-900 bg-opacity-30 border border-red-500 rounded p-3 text-red-300 text-sm">
                  You don't have any NFT pets. Please mint an NFT pet first.
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-center text-gray-400 mb-4">
                Please connect your wallet to check for NFT pets
              </div>
              <Button
                className="w-full"
                onClick={handleConnectWallet}
                disabled={isConnecting}
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  'Connect Wallet'
                )}
              </Button>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleProceed}
            disabled={(!hasNFTPet && !isPvpEnabled) || isLoading}
            className={(!hasNFTPet && !isPvpEnabled) ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Enter PVP Mode
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PVPRequirementDialog;
