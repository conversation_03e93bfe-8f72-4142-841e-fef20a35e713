import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface QueueStatusIndicatorProps {
  requestId: string | null;
  queuePosition: number;
  onComplete?: () => void;
}

/**
 * Component to display the status of a request in the queue
 */
const QueueStatusIndicator: React.FC<QueueStatusIndicatorProps> = ({
  requestId,
  queuePosition,
  onComplete
}) => {
  const [position, setPosition] = useState<number>(queuePosition);
  const [status, setStatus] = useState<string>('pending');
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState<boolean>(!!requestId && queuePosition > 0);

  // Poll for status updates
  useEffect(() => {
    if (!requestId || queuePosition <= 0) {
      return;
    }

    let isMounted = true;
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/ai/request-status/${requestId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to get request status: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (isMounted) {
          setPosition(data.queuePosition);
          setStatus(data.status);
          
          // If request is completed, stop polling and call onComplete
          if (data.status === 'completed') {
            clearInterval(pollInterval);
            setIsPolling(false);
            if (onComplete) {
              onComplete();
            }
          }
          
          // If request failed, stop polling and show error
          if (data.status === 'failed') {
            clearInterval(pollInterval);
            setIsPolling(false);
            setError('Request processing failed. Please try again.');
          }
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error polling for request status:', err);
          setError('Failed to get request status. Please try again.');
          clearInterval(pollInterval);
          setIsPolling(false);
        }
      }
    }, 2000); // Poll every 2 seconds
    
    return () => {
      isMounted = false;
      clearInterval(pollInterval);
    };
  }, [requestId, queuePosition, onComplete]);

  // If no request ID or position is 0, don't show anything
  if (!requestId || queuePosition <= 0) {
    return null;
  }

  // If there was an error
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">
        <strong className="font-bold">Error: </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  // Show loading indicator with position
  return (
    <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded relative mt-4 flex items-center">
      <Loader2 className="animate-spin mr-2" size={20} />
      <div>
        <p className="font-semibold">
          {status === 'processing' ? 'Processing your request...' : 'Your request is in queue'}
        </p>
        {position > 0 && (
          <p className="text-sm">
            Position in queue: {position}
            {position === 1 ? ' (You\'re next!)' : ''}
          </p>
        )}
        <p className="text-xs mt-1">
          This may take a few moments. Please don't refresh the page.
        </p>
      </div>
    </div>
  );
};

export default QueueStatusIndicator;
