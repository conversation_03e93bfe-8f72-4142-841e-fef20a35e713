import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Wand2, Image as ImageIcon, Sparkles } from 'lucide-react';
import { useWeb3 } from '@/contexts/Web3Context';
import { PetService } from '@/services/petService';
import { AIGenerationService } from '@/services/aiGenerationService';
// Import types
interface SpecterType {
  id: number;
  name: string;
  color: string;
  points: number;
  texture: string;
}

interface AIPetGenerationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPetGenerated: (tokenId: string) => void;
}

// Available specter types
const specterTypes: SpecterType[] = [
  { id: 0, name: 'Class 1', color: '#3399ff', points: 100, texture: 'assets/textures/wisp.png' },
  { id: 1, name: 'Class 2', color: '#ff3399', points: 250, texture: 'assets/textures/phantom.png' },
  { id: 2, name: 'Class 3', color: '#33ff99', points: 500, texture: 'assets/textures/wraith.png' },
  { id: 3, name: 'Class 4', color: '#9933ff', points: 1000, texture: 'assets/textures/poltergeist.png' },
  { id: 4, name: 'Class 5', color: '#ff9933', points: 2000, texture: 'assets/textures/banshee.png' }
];

// Available style options
const styleOptions = [
  { value: 'pixel', label: 'Pixel Art' },
  { value: 'cartoon', label: 'Cartoon' },
  { value: 'realistic', label: 'Realistic' },
  { value: 'abstract', label: 'Abstract' },
  { value: 'fantasy', label: 'Fantasy' },
  { value: 'cyberpunk', label: 'Cyberpunk' },
  { value: 'vaporwave', label: 'Vaporwave' },
  { value: 'anime', label: 'Anime' }
];

const AIPetGenerationDialog: React.FC<AIPetGenerationDialogProps> = ({
  isOpen,
  onClose,
  onPetGenerated,
}) => {
  const { isConnected, account, mintNFT, connectWallet } = useWeb3();
  const { toast } = useToast();

  // Form state
  const [petName, setPetName] = useState('');
  const [selectedType, setSelectedType] = useState<string>('Class 1');
  const [description, setDescription] = useState('');
  const [color, setColor] = useState('#3399ff');
  const [style, setStyle] = useState('fantasy');

  // UI state
  const [isGenerating, setIsGenerating] = useState(false);
  const [isMinting, setIsMinting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(null);
  const [generatedPrompt, setGeneratedPrompt] = useState<string | null>(null);
  const [hasGeneratedBefore, setHasGeneratedBefore] = useState(false);

  // Get the selected specter type object
  const getSelectedTypeObject = (): SpecterType => {
    const found = specterTypes.find(type => type.name === selectedType);
    return found || specterTypes[0];
  };

  // Handle image generation
  const handleGenerateImage = async () => {
    if (!description) {
      toast({
        title: 'Description Required',
        description: 'Please provide a description for your pet specter',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);

    try {
      // If this is a regeneration (user clicked generate again), bypass the cache
      const bypassCache = hasGeneratedBefore;

      // Call the AI service to generate an image
      const result = await AIGenerationService.generatePetSpecterImage({
        specterType: selectedType,
        description,
        color,
        style
      }, { bypassCache });

      setGeneratedImageUrl(result.imageUrl);
      setGeneratedPrompt(result.prompt);
      setHasGeneratedBefore(true); // Mark that we've generated at least once

      toast({
        title: bypassCache ? 'New Image Generated' : 'Image Generated',
        description: bypassCache ?
          'A new pet specter image has been generated!' :
          'Your pet specter image has been generated!',
        duration: 3000
      });
    } catch (error) {
      console.error('Error generating image:', error);
      toast({
        title: 'Generation Failed',
        description: 'Failed to generate pet specter image. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle creating an off-grid pet (without minting as NFT)
  const handleCreateOffGridPet = async () => {
    if (!generatedImageUrl) {
      toast({
        title: 'No Image Generated',
        description: 'Please generate an image first',
        variant: 'destructive'
      });
      return;
    }

    if (!petName) {
      toast({
        title: 'Name Required',
        description: 'Please provide a name for your pet specter',
        variant: 'destructive'
      });
      return;
    }

    setIsMinting(true);

    try {
      // Generate a unique game ID for the pet
      const gameId = `pet-${Date.now()}`;

      // Store the image URL in local storage for backup
      localStorage.setItem(`pet_image_${gameId}`, generatedImageUrl);
      console.log(`Stored pet image URL in local storage with key: pet_image_${gameId}`);

      // Get the selected specter type
      const specterType = getSelectedTypeObject();

      // Create pet data for database
      const petData = {
        gameId,
        name: petName,
        specterType: specterType.name,
        walletAddress: account || undefined,
        level: 1,
        xp: 0,
        traits: [
          { type: 'ATTACK', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'DEFENSE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'SPEED', level: 1, xp: 0, xpToNextLevel: 100 }
        ],
        stats: {
          health: 100,
          maxHealth: 100,
          attackPower: 10,
          defenseValue: 5,
          speed: 5
        },
        metadata: {
          aiGenerated: true,
          imageUrl: generatedImageUrl,
          prompt: generatedPrompt,
          isOffGrid: true // Mark this as an off-grid pet
        }
      };

      // Create the pet in the database without minting
      const createdPet = await PetService.createPetSpecter(petData);
      console.log('Created off-grid pet:', createdPet);

      // Call the success callback with a special "off-grid" prefix
      onPetGenerated(`off-grid-${gameId}`);

      toast({
        title: 'Success',
        description: 'Your AI-generated pet specter has been created for single player use!',
        duration: 5000
      });

      // Close the dialog
      onClose();
    } catch (error) {
      console.error('Error creating off-grid pet:', error);
      toast({
        title: 'Error',
        description: 'Failed to create pet. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsMinting(false);
    }
  };

  // Handle mint button click (currently disabled)
  const handleMint = async () => {
    if (!generatedImageUrl) {
      toast({
        title: 'No Image Generated',
        description: 'Please generate an image first',
        variant: 'destructive'
      });
      return;
    }

    if (!petName) {
      toast({
        title: 'Name Required',
        description: 'Please provide a name for your pet specter',
        variant: 'destructive'
      });
      return;
    }

    if (!isConnected) {
      setIsConnecting(true);
      await connectWallet();
      setIsConnecting(false);
      return;
    }

    setIsMinting(true);

    try {
      // Generate a unique game ID for the pet
      const gameId = `pet-${Date.now()}`;

      // Store the image URL in local storage for backup
      localStorage.setItem(`pet_image_${gameId}`, generatedImageUrl);
      console.log(`Stored pet image URL in local storage with key: pet_image_${gameId}`);

      // Get the selected specter type
      const specterType = getSelectedTypeObject();

      // Create metadata for the NFT
      const metadata = {
        name: petName,
        description: `A ${specterType.name} Pet Specter from Specter Shift game. ${description}`,
        image: generatedImageUrl, // Use the AI-generated image
        attributes: [
          {
            trait_type: 'Type',
            value: specterType.name
          },
          {
            trait_type: 'Color',
            value: color
          },
          {
            trait_type: 'Style',
            value: style
          },
          {
            trait_type: 'Level',
            value: '1'
          },
          {
            trait_type: 'XP',
            value: '0'
          }
        ],
        // Store the prompt used to generate the image
        generationPrompt: generatedPrompt
      };

      // Create pet data for database
      const petData = {
        gameId,
        name: petName,
        specterType: specterType.name,
        walletAddress: account || undefined, // Fix null type issue
        level: 1,
        xp: 0,
        traits: [
          { type: 'ATTACK', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'DEFENSE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'SPEED', level: 1, xp: 0, xpToNextLevel: 100 }
        ],
        stats: {
          health: 100,
          maxHealth: 100,
          attackPower: 10,
          defenseValue: 5,
          speed: 5
        },
        metadata: {
          aiGenerated: true,
          imageUrl: generatedImageUrl,
          prompt: generatedPrompt
        }
      };

      // Calculate price based on the specter type
      const price = (0.001 * (specterType.id + 1)).toFixed(3);

      try {
        // First create the pet in the database
        const createdPet = await PetService.createPetSpecter(petData);
        console.log('Created pet:', createdPet);

        // Then mint the NFT
        const tokenURI = JSON.stringify(metadata);
        // Use the gameId instead of the numeric id
        const tokenId = await mintNFT(tokenURI, price, gameId);

        if (tokenId) {
          // Store the NFT image URL in localStorage with the token ID as the key
          // This ensures we can retrieve it even if the database connection fails
          localStorage.setItem(`nft_pet_image_${tokenId}`, generatedImageUrl);
          console.log(`Stored NFT pet image URL in local storage with key: nft_pet_image_${tokenId}`);

          // Link the pet to the NFT
          await PetService.linkPetSpecterToNFT(gameId, tokenId, account || '');

          // Call the success callback
          onPetGenerated(tokenId);

          toast({
            title: 'Success',
            description: 'Your AI-generated pet specter has been minted as an NFT!',
            duration: 5000
          });

          // Close the dialog
          onClose();
        }
      } catch (error) {
        console.error('Error creating pet or minting NFT:', error);
        toast({
          title: 'Error',
          description: 'Failed to create pet or mint NFT. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error minting NFT:', error);
      toast({
        title: 'Minting Failed',
        description: 'Failed to mint NFT. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsMinting(false);
    }
  };

  // Use a custom handler for dialog changes to ensure we don't interfere with game state
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Only call onClose when the dialog is being closed
      onClose();
    }
    // We don't do anything when the dialog is being opened
    // This is handled by the AIPetGenerationDialogProvider
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create AI-Generated Pet Specter</DialogTitle>
          <DialogDescription>
            Describe your perfect pet specter and our AI will generate a unique image for it.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Pet Name */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={petName}
              onChange={(e) => setPetName(e.target.value)}
              placeholder="Enter pet name"
              className="col-span-3"
            />
          </div>

          {/* Specter Type */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="type" className="text-right">
              Type
            </Label>
            <Select
              value={selectedType}
              onValueChange={setSelectedType}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                {specterTypes.map((type) => (
                  <SelectItem key={type.id} value={type.name}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Color */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="color" className="text-right">
              Color
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="color"
                type="color"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="w-12 h-10 p-1"
              />
              <Input
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="flex-1"
              />
            </div>
          </div>

          {/* Style */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="style" className="text-right">
              Style
            </Label>
            <Select
              value={style}
              onValueChange={setStyle}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select style" />
              </SelectTrigger>
              <SelectContent>
                {styleOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your pet specter in detail..."
              className="col-span-3 min-h-[100px]"
            />
          </div>

          {/* Generate Button */}
          <Button
            onClick={handleGenerateImage}
            disabled={isGenerating || !description}
            className="mt-2"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                {hasGeneratedBefore ? 'Generate New Image' : 'Generate Pet Image'}
              </>
            )}
          </Button>

          {/* Generated Image Preview */}
          {generatedImageUrl && (
            <div className="mt-4 border rounded-lg p-4">
              <h3 className="font-medium text-center mb-2">Generated Pet Specter</h3>
              <div className="flex justify-center mb-4">
                <div className="relative w-48 h-48 bg-gray-800 rounded-lg overflow-hidden">
                  <img
                    src={generatedImageUrl}
                    alt="Generated Pet Specter"
                    className="w-full h-full object-contain"
                  />
                  <div className="absolute bottom-2 right-2">
                    <Sparkles className="h-5 w-5 text-yellow-400" />
                  </div>
                </div>
              </div>

              {/* Off-Grid Button */}
              <Button
                onClick={handleCreateOffGridPet}
                disabled={isMinting || !petName}
                className="w-full mt-2 bg-purple-600 hover:bg-purple-700"
              >
                {isMinting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <ImageIcon className="mr-2 h-4 w-4" />
                    Create Off-Grid Pet Specter
                  </>
                )}
              </Button>

              {/* Mint Button - Disabled with Coming Soon message */}
              <Button
                disabled={true}
                className="w-full mt-2 opacity-70"
              >
                <ImageIcon className="mr-2 h-4 w-4" />
                Mint as NFT (Coming Soon)
              </Button>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AIPetGenerationDialog;
