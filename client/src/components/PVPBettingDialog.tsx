import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useWeb3 } from '@/contexts/Web3Context';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface PVPBettingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  matchId: string;
  opponentName: string;
  opponentPetName: string;
  opponentPetLevel: number;
  yourPetName: string;
  yourPetLevel: number;
  onBetPlaced: (amount: string) => void;
}

const PVPBettingDialog: React.FC<PVPBettingDialogProps> = ({
  isOpen,
  onClose,
  matchId,
  opponentName,
  opponentPetName,
  opponentPetLevel,
  yourPetName,
  yourPetLevel,
  onBetPlaced,
}) => {
  const { isConnected, account, connectWallet, sendTransaction } = useWeb3();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [betAmount, setBetAmount] = useState('0.0005');

  // Calculate platform fee (3%)
  const platformFee = parseFloat(betAmount) * 0.03;
  const totalCost = parseFloat(betAmount) + platformFee;

  // Calculate odds based on pet levels
  const calculateOdds = () => {
    const levelDifference = yourPetLevel - opponentPetLevel;

    if (levelDifference > 5) return '1.2'; // Heavy favorite
    if (levelDifference > 2) return '1.5';
    if (levelDifference >= 0) return '1.8';
    if (levelDifference >= -2) return '2.0';
    if (levelDifference >= -5) return '2.5';
    return '3.0'; // Heavy underdog
  };

  const odds = calculateOdds();
  const potentialWinnings = (parseFloat(betAmount) * parseFloat(odds)).toFixed(6);

  // Handle bet button click
  const handlePlaceBet = async () => {
    if (!isConnected) {
      setIsConnecting(true);
      await connectWallet();
      setIsConnecting(false);
      return;
    }

    if (parseFloat(betAmount) <= 0) {
      toast({
        title: "Invalid Bet",
        description: "Please enter a valid bet amount",
        variant: "destructive",
        duration: 3000
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Create bet data
      const betData = {
        matchId,
        opponentName,
        opponentPetName,
        yourPetName,
        betAmount,
        odds,
        timestamp: Date.now()
      };

      // Send transaction
      const txHash = await sendTransaction(totalCost.toFixed(6), JSON.stringify(betData));

      if (txHash) {
        toast({
          title: "Bet Placed Successfully",
          description: `You've placed a bet of ${betAmount} $POL on ${yourPetName}`,
          duration: 5000
        });

        // Call the success callback
        onBetPlaced(betAmount);
      } else {
        toast({
          title: "Bet Failed",
          description: "Failed to place bet. Please try again.",
          variant: "destructive",
          duration: 5000
        });
      }
    } catch (error: any) {
      console.error("Error placing bet:", error);
      toast({
        title: "Bet Error",
        description: error.message || "An error occurred while placing your bet.",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      setIsProcessing(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Place Your Bet</DialogTitle>
          <DialogDescription>
            Bet on your pet specter in this PVP match against {opponentName}'s {opponentPetName}.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-center mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400 mb-2">PVP Match</div>
              <div className="text-sm text-gray-400">
                Winner takes the prize pool minus platform fees
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="text-sm text-gray-400">Your Pet</div>
              <div className="text-lg font-bold text-white">{yourPetName}</div>
              <div className="text-sm text-blue-400">Level {yourPetLevel}</div>
            </div>

            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="text-sm text-gray-400">Opponent's Pet</div>
              <div className="text-lg font-bold text-white">{opponentPetName}</div>
              <div className="text-sm text-blue-400">Level {opponentPetLevel}</div>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="bet-amount" className="text-right">
              Bet Amount
            </Label>
            <div className="col-span-3">
              <Input
                id="bet-amount"
                type="number"
                step="0.001"
                min="0.001"
                value={betAmount}
                onChange={(e) => setBetAmount(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="odds" className="text-right">
              Odds
            </Label>
            <Input
              id="odds"
              value={`${odds}x`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="potential-win" className="text-right">
              Potential Win
            </Label>
            <Input
              id="potential-win"
              value={`${potentialWinnings} MATIC`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="platform-fee" className="text-right">
              Platform Fee (3%)
            </Label>
            <Input
              id="platform-fee"
              value={`${platformFee.toFixed(6)} MATIC`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="total-cost" className="text-right font-bold">
              Total
            </Label>
            <Input
              id="total-cost"
              value={`${totalCost.toFixed(6)} MATIC`}
              className="col-span-3 font-bold"
              disabled
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handlePlaceBet} disabled={isProcessing || isConnecting}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting...
              </>
            ) : !isConnected ? (
              'Connect Wallet to Bet'
            ) : (
              'Place Bet'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PVPBettingDialog;
