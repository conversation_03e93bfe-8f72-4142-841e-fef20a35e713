import React, { useEffect, useState } from 'react';
import NFTBrowser from './NFTBrowser';

interface NFTItem {
  contractAddress: string;
  tokenId: string;
  name: string;
  symbol: string;
  image?: string;
  attributes?: any[];
  metadata?: any;
}

/**
 * Provider component that listens for NFT browser events
 */
const NFTBrowserDialogProvider: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [onSelectNFT, setOnSelectNFT] = useState<(nft: NFTItem) => void>(() => () => {});
  const [gameEngine, setGameEngine] = useState<any>(null);
  const [supportedCollections, setSupportedCollections] = useState<string[]>([]);

  useEffect(() => {
    // Listen for the showNFTBrowserDialog event
    const handleShowNFTBrowserDialog = (event: CustomEvent) => {
      const { isOpen, onSelectNFT, gameEngine, supportedCollections = [] } = event.detail;

      // Make sure the game is paused before showing the dialog
      if (gameEngine && typeof gameEngine.pause === 'function') {
        gameEngine.pause();
      }

      // Dispatch an event to notify the Game component that the NFT browser dialog is open
      const nftBrowserDialogOpenedEvent = new CustomEvent('nftBrowserDialogOpened');
      document.dispatchEvent(nftBrowserDialogOpenedEvent);

      setIsOpen(isOpen);
      setOnSelectNFT(() => onSelectNFT);
      setGameEngine(gameEngine);
      setSupportedCollections(supportedCollections);
    };

    // Add event listener
    document.addEventListener('showNFTBrowserDialog', handleShowNFTBrowserDialog as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('showNFTBrowserDialog', handleShowNFTBrowserDialog as EventListener);
    };
  }, []);

  // Handle dialog close
  const handleClose = () => {
    setIsOpen(false);

    // Dispatch an event to notify the Game component that the NFT browser dialog is closed
    const nftBrowserDialogClosedEvent = new CustomEvent('nftBrowserDialogClosed');
    document.dispatchEvent(nftBrowserDialogClosedEvent);

    // Let the Game component handle resuming the game
    // This ensures proper synchronization with the game's pause state
  };

  return (
    <NFTBrowser
      isOpen={isOpen}
      onClose={handleClose}
      onSelectNFT={(nft) => {
        onSelectNFT(nft);
        handleClose();
      }}
      supportedCollections={supportedCollections}
    />
  );
};

export default NFTBrowserDialogProvider;
