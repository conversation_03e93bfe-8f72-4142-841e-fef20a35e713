import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useWeb3 } from '@/contexts/Web3Context';
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface TestModeToggleProps {
  className?: string;
}

const TestModeToggle: React.FC<TestModeToggleProps> = ({ className }) => {
  const { isTestMode, toggleTestMode } = useWeb3();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Switch
        id="test-mode"
        checked={isTestMode}
        onCheckedChange={toggleTestMode}
      />
      <Label htmlFor="test-mode" className="text-sm cursor-pointer">
        Test Mode
      </Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="h-4 w-4 text-gray-400 cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">
              When enabled, transactions will be simulated without spending real MATIC.
              <br /><br />
              This is useful for testing the game's monetization features without using up your testnet MATIC.
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default TestModeToggle;
