import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useWeb3 } from '@/contexts/Web3Context';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { SpecterTraitType } from '@/game/entities/PetSpecter';

interface TrainingFeeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  petName: string;
  petId: string;
  traitType: SpecterTraitType;
  onTrainingSuccess: () => void;
}

const TrainingFeeDialog: React.FC<TrainingFeeDialogProps> = ({
  isOpen,
  onClose,
  petName,
  petId,
  traitType,
  onTrainingSuccess,
}) => {
  const { isConnected, account, connectWallet, sendTransaction } = useWeb3();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Define the price based on trait type
  const getPrice = () => {
    // Base price is 0.0001 MATIC (very small for testing)
    const basePrice = '0.0001';

    // Adjust price based on trait type
    switch (traitType) {
      case SpecterTraitType.ATTACK:
        return basePrice;
      case SpecterTraitType.DEFENSE:
        return basePrice;
      case SpecterTraitType.SPEED:
        return basePrice;
      case SpecterTraitType.INTELLIGENCE:
        return (parseFloat(basePrice) * 1.5).toFixed(6); // More expensive
      case SpecterTraitType.LOYALTY:
        return (parseFloat(basePrice) * 0.8).toFixed(6); // Less expensive
      default:
        return basePrice;
    }
  };

  const price = getPrice();

  // Format trait name for display
  const formatTraitName = (trait: SpecterTraitType): string => {
    return trait.charAt(0).toUpperCase() + trait.slice(1).toLowerCase();
  };

  // Handle payment button click
  const handlePayment = async () => {
    if (!isConnected) {
      setIsConnecting(true);
      await connectWallet();
      setIsConnecting(false);
      return;
    }

    setIsProcessing(true);

    try {
      // Create transaction data
      const transactionData = {
        petId,
        traitType,
        price,
        timestamp: Date.now()
      };

      // Send transaction
      const txHash = await sendTransaction(price, JSON.stringify(transactionData));

      if (txHash) {
        toast({
          title: "Training Payment Successful",
          description: `You've paid ${price} $POL to train ${petName}'s ${formatTraitName(traitType)} trait.`,
          duration: 5000
        });

        // Call the success callback
        onTrainingSuccess();
      } else {
        toast({
          title: "Payment Failed",
          description: "Failed to process training payment. Please try again.",
          variant: "destructive",
          duration: 5000
        });
      }
    } catch (error: any) {
      console.error("Error processing training payment:", error);
      toast({
        title: "Payment Error",
        description: error.message || "An error occurred while processing your payment.",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      setIsProcessing(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Train {petName}'s {formatTraitName(traitType)}</DialogTitle>
          <DialogDescription>
            Pay a small fee in $POL to train your pet's {formatTraitName(traitType)} trait.
            Training with $POL provides 3x the XP of regular training!
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-center mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-purple-400 mb-2">Premium Training</div>
              <div className="text-sm text-gray-400">
                Accelerate your pet's growth with premium training
              </div>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="text-right text-sm font-medium">Pet:</div>
            <div className="col-span-3">{petName}</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="text-right text-sm font-medium">Trait:</div>
            <div className="col-span-3 text-purple-400">{formatTraitName(traitType)}</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="text-right text-sm font-medium">Price:</div>
            <div className="col-span-3 font-bold">{price} MATIC</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <div className="text-right text-sm font-medium">Bonus:</div>
            <div className="col-span-3 text-green-400">3x XP Gain</div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handlePayment} disabled={isProcessing || isConnecting}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting...
              </>
            ) : !isConnected ? (
              'Connect Wallet to Pay'
            ) : (
              'Pay & Train'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TrainingFeeDialog;
