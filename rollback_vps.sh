#!/bin/bash
set -e

# Script to rollback to a previous VPS deployment if the update fails
echo "Rolling back SpecterShift deployment on VPS..."

# Variables
VPS_IP="**************"
VPS_USER="root"
REMOTE_DIR="/opt/spectershift"

# List available backups
echo "Available backups on VPS:"
ssh $VPS_USER@$VPS_IP "ls -la /opt/ | grep spectershift_backup"

# Ask for backup directory name
read -p "Enter the backup directory name to restore from (e.g., spectershift_backup_20230101_120000): " BACKUP_DIR

if [ -z "$BACKUP_DIR" ]; then
  echo "No backup directory specified. Exiting."
  exit 1
fi

FULL_BACKUP_PATH="/opt/$BACKUP_DIR"

# Check if backup directory exists
if ssh $VPS_USER@$VPS_IP "[ ! -d $FULL_BACKUP_PATH ]"; then
  echo "Backup directory $FULL_BACKUP_PATH does not exist on the VPS. Exiting."
  exit 1
fi

# Confirm rollback
read -p "Are you sure you want to roll back to $FULL_BACKUP_PATH? This will replace the current deployment. (y/n): " CONFIRM

if [ "$CONFIRM" != "y" ]; then
  echo "Rollback cancelled."
  exit 0
fi

# Stop the current application
echo "Stopping current application..."
ssh $VPS_USER@$VPS_IP "cd $REMOTE_DIR && pm2 stop shattershift || true"

# Backup current deployment before rollback
CURRENT_BACKUP="/opt/spectershift_pre_rollback_$(date +%Y%m%d_%H%M%S)"
echo "Creating backup of current deployment at $CURRENT_BACKUP..."
ssh $VPS_USER@$VPS_IP "mkdir -p $CURRENT_BACKUP && cp -r $REMOTE_DIR/* $CURRENT_BACKUP/ 2>/dev/null || true"

# Restore from backup
echo "Restoring from backup $FULL_BACKUP_PATH..."
ssh $VPS_USER@$VPS_IP "rm -rf $REMOTE_DIR/* && cp -r $FULL_BACKUP_PATH/* $REMOTE_DIR/"

# Restart the application
echo "Restarting application..."
ssh $VPS_USER@$VPS_IP "cd $REMOTE_DIR && pm2 restart shattershift || pm2 start ecosystem.config.js"

echo "Rollback complete. The application has been restored from $FULL_BACKUP_PATH."
echo "To check the application status: ssh $VPS_USER@$VPS_IP \"pm2 status\""
echo "To view logs: ssh $VPS_USER@$VPS_IP \"pm2 logs shattershift\""
