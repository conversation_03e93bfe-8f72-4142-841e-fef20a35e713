#!/bin/bash
set -e

# <PERSON><PERSON>t to safely update the SpecterShift deployment on VPS
echo "Starting safe update of SpecterShift on VPS..."

# Variables
VPS_IP="**************"
VPS_USER="root"
REMOTE_DIR="/opt/spectershift"

# Make sure all scripts are executable
chmod +x backup_vps.sh
chmod +x upload_to_vps.sh
chmod +x rollback_vps.sh

# Step 1: Run the backup script
echo "Step 1: Creating backup of current deployment..."
./backup_vps.sh

# Step 2: Upload files to VPS
echo "Step 2: Uploading files to VPS..."
./upload_to_vps.sh

# Step 3: Run the deployment script on the VPS
echo "Step 3: Running deployment script on VPS..."
ssh $VPS_USER@$VPS_IP "cd $REMOTE_DIR && ./deploy_shattershift.sh"

# Step 4: Verify the application is running
echo "Step 4: Verifying application status..."
ssh $VPS_USER@$VPS_IP "pm2 status"

echo "Update complete! The application should now be running with the latest code."
echo "If you encounter any issues, run ./rollback_vps.sh to restore from a backup."
