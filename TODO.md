# TODO List

## Current Implementation Status

The following features have been implemented:

1. **WebSocket Server**: The WebSocket server is fully implemented and active, not commented out as previously noted.

2. **PetSpecter Class**: The PetSpecter class is implemented with stats, traits, equipment, and behaviors. It includes:
   - Basic AI behavior states (follow, attack, idle)
   - Methods for following the player and attacking enemies
   - Visual effects and animations

3. **MerchGenie <PERSON>**: Basic UI for purchasing pet specters with in-game points is implemented.

4. **Multiplayer Framework**: Basic multiplayer functionality with WebSocket connections is in place.

5. **Infinite World Generation**: Basic procedural level generation with chunk-based system is implemented. The skybox follows the player, but the ground plane does not properly follow the camera.

6. **Dungeon System**: Basic dungeon generation is implemented but has several issues that need to be fixed (see Dungeon Improvements section below).

## Features Still Needed

### 1. Web3/Wallet Integration
- Implement MetaMask or other wallet connection
- Add MATIC transaction functionality for minting pet specters as NFTs
- Create the 'mintPetSpecter' event system that triggers PetSpecter creation with blockchain data
- Implement NFT minting with a 3% platform fee
- Allow purchasing training items and equipment with MATIC
- Link pet data (level, XP, equipment) to wallet's Pet Specter NFT

#### Detailed Plan for NFT Minting Online
- **Smart Contract Development**
  - Define ERC-721 (or ERC-1155) contract using Hardhat/Truffle.
  - Implement `mint` function, metadata URI, and 3% platform fee.
  - Deploy to Polygon Amoy testnet & mainnet.
- **Metadata & Asset Storage**
  - Store images & JSON metadata on IPFS (Pinata/Infura) or Arweave.
  - Pin assets and retrieve CID for contract deployment.
- **Backend Service**
  - Create Node.js/Express (or Next.js API) to generate & pin metadata.
  - Securely manage private & API keys with environment variables.
- **Frontend Integration**
  - Extend in-game UI/React component to connect MetaMask/Web3 provider.
  - Add mint button, transaction status indicator, and confirmation flow.
- **CI/CD & Deployment**
  - Use GitHub Actions for smart contract testing & deployment scripts.
  - Deploy backend to Vercel/Heroku/AWS; automate IPFS pinning & migrations.
- **Testing & Security**
  - Write smart contract unit tests (Mocha/Chai) & integration tests.
  - Conduct audit for fee logic & edge cases; secure secrets with dotenv/vault.
- **Gas & Cost Optimization**
  - Estimate gas costs on Polygon; optimize contract code & use EIP-1559.
  - Consider transaction relayer or meta-transactions if needed.
- **Monitoring & Analytics**
  - Index mint events using The Graph or custom event listener.
  - Track metrics (mint count, revenue, failures) and integrate with a dashboard.

### 2. Fix Infinite World Generation *Done*
- Fix ground plane tiling to properly follow the camera
- Currently, the player can see the bottom of the skybox even though level objects continue to generate
- Implement seamless terrain that moves with the player to create a truly infinite appearance

### 3. Optimize Homing Powerup Performance *Done*
- Determine why activation causes game to freeze and become unresponsive for seconds at a time

### 4. Performance Optimization & Quality Improvements
- Add variety/objects to level so that it appears more coherent and less like a floor with some houses and hoops and rods on the ground and floating platforms above it
- Optimize rendering and code so that the codebase is light and works well even on low spec hardware
- Improve weapon effects with more appropriate visual representations of gravity and phase effects etc.

### 5. Implement Persistent Storage *Done*
- Replace MemStorage with a database implementation using Drizzle
- Store users, high scores, game arenas, and teams in PostgreSQL database
- Store NFT-linked PetSpecter data, including unique IDs
- Store user balances of MATIC and $EKTO (when implemented)
- Track transaction history for revenue tracking

### 6. Mobile Support
- Add virtual joystick controls
- Implement touch-based gesture system for abilities
- Add auto-aim assistance for mobile
- Optimize UI for touch interfaces
- Implement responsive design for all UI elements
- Add mobile-specific graphics settings

### 7. Enhance Weapon Effects
- Implement GPU-based particle systems
- Add proper shader-based effects for explosions
- Implement realistic shock waves
- Add environmental interaction (debris, surface effects)
- Implement proper light sources for explosions
- Add screen-space distortion effects
- Apply textures to the shattershift rifle's geodesic dome explosion effects instead of using wireframes, similar to how enemy sprites are textured

### 8. Enhance Single Player Experience
- Implement a dungeon players can enter if they have a pet specter to guard them.
- Implement procedural dungeon generation, with various ghost/specter themed enemies
- Use permanent progression system (skills, abilities) tied to the pet the player enters with.
- Implement random events and encounters
- Add unique high level enemies to defeat to clear certain levels/quests.

### 9. Implement Pet Specter Arena for PVP
- Create an arena for Pet Specters to fight, like a virtual colliseum.
- Allow players to pit their specter against others within 2 levels of their own in 1-on-1 battles
- Implement betting system with MATIC and later $EKTO, with a 3% platform fee
- (Lower Priority) Create co-op PVP tournaments where teams compete to capture more specters
- Allow players to purchase powerups for PVP: Fire, Ice, and Shield. (Can equip two, and the specter can use one at a time, with 2 second cooldowns. Shield is active for 3 seconds. Fire blast cancels ice blast, ice blast freezes opponent, and shield blocks all for 3 seconds)

### 10. Enhance NFT-Based Pet Generation with AI Image Generation
- Replace the current deterministic algorithm with an AI image generation system
- Integrate with an image generation LLM (like Fal.ai or similar)
- Pass the players preference to the LLM with specific instructions to generate a unique pet specter
- Implement a prompt system that combines the player's preferences with game specific instructions to have the image generator generate an awesome pet specter. Then use Fal.ai or rembg or similar to remove backgrounds. To do this we first need to create the ability for the player to specify what kind of specter they want to create. Not a "ghost-like creature". The transparency effects and behavior of the specter in the game is what makes it "ghost like". We need to ensure that the player can give a description of what kind of character they want, and and the llm takes that and creates a appropriate prompt for the AI image service. The llm will be instructed to ensure prompts describe create a cohesive character, rather than a scene. The character should be floating in front of a gray background. NEVER rephrase user's core character elements unless it involves replacing copyrighted material. In that case, replace copyrighted elements with original, inspired alternatives.
We will use Groq for the LLM:
In your terminal of choice:

export GROQ_API_KEY=<your-api-key-here>
Requesting your first chat completion
curl
JavaScript
Python
JSON
Install the Groq JavaScript library:

npm install --save groq-sdk
Performing a Chat Completion:

import Groq from "groq-sdk";

const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

export async function main() {
  const chatCompletion = await getGroqChatCompletion();
  // Print the completion returned by the LLM.
  console.log(chatCompletion.choices[0]?.message?.content || "");
}

export async function getGroqChatCompletion() {
  return groq.chat.completions.create({
    messages: [
      {
        role: "user",
        content: "Explain the importance of fast language models",
      },
    ],
    model: "llama-3.1-8b-instant",
  });
}
- Create a caching system to store results and avoid redundant API calls
- Implement rate limiting to manage API costs
- Image Optimization: We'll resize the image to 256x256 pixels and apply compression
- Transparency: We'll ensure the background is removed to reduce file size
- PNG Optimization: We'll use sharp's compression options to minimize size (around 35kb)
- Create a preview system to show the generated pet before minting
- Create the NFT with the background removed. Use the generated image as a sprite for the pet specter of the player when they load the pet and confirm they have the NFT in their wallet.
- Fal.ai docs:
   Calling the API
#
Install the client
#
The client provides a convenient way to interact with the model API.

npmyarnpnpmbun

npm install --save @fal-ai/client
Migrate to @fal-ai/client
The @fal-ai/serverless-client package has been deprecated in favor of @fal-ai/client. Please check the migration guide for more information.

Setup your API Key
#
Set FAL_KEY as an environment variable in your runtime.


export FAL_KEY="YOUR_API_KEY"
Submit a request
#
The client API handles the API submit protocol. It will handle the request status updates and return the result when the request is completed.


import { fal } from "@fal-ai/client";

const result = await fal.subscribe("fal-ai/hidream-i1-fast", {
  input: {
    prompt: "a cat holding a skateboard which has 'fal' written on it in red spray paint"
  },
  logs: true,
  onQueueUpdate: (update) => {
    if (update.status === "IN_PROGRESS") {
      update.logs.map((log) => log.message).forEach(console.log);
    }
  },
});
console.log(result.data);
console.log(result.requestId);
Streaming
#
This model supports streaming requests. You can stream data directly to the model and get the result in real-time.


import { fal } from "@fal-ai/client";

const stream = await fal.stream("fal-ai/hidream-i1-fast", {
  input: {
    prompt: "a cat holding a skateboard which has 'fal' written on it in red spray paint"
  }
});

for await (const event of stream) {
  console.log(event);
}

const result = await stream.done();
2. Authentication
#
The API uses an API Key for authentication. It is recommended you set the FAL_KEY environment variable in your runtime when possible.

API Key
#
In case your app is running in an environment where you cannot set environment variables, you can set the API Key manually as a client configuration.

import { fal } from "@fal-ai/client";

fal.config({
  credentials: "YOUR_FAL_KEY"
});
Protect your API Key
When running code on the client-side (e.g. in a browser, mobile app or GUI applications), make sure to not expose your FAL_KEY. Instead, use a server-side proxy to make requests to the API. For more information, check out our server-side integration guide.

3. Queue
#
Long-running requests
For long-running requests, such as training jobs or models with slower inference times, it is recommended to check the Queue status and rely on Webhooks instead of blocking while waiting for the result.

Submit a request
#
The client API provides a convenient way to submit requests to the model.


import { fal } from "@fal-ai/client";

const { request_id } = await fal.queue.submit("fal-ai/hidream-i1-fast", {
  input: {
    prompt: "a cat holding a skateboard which has 'fal' written on it in red spray paint"
  },
  webhookUrl: "https://optional.webhook.url/for/results",
});
Fetch request status
#
You can fetch the status of a request to check if it is completed or still in progress.


import { fal } from "@fal-ai/client";

const status = await fal.queue.status("fal-ai/hidream-i1-fast", {
  requestId: "764cabcf-b745-4b3e-ae38-1200304cf45b",
  logs: true,
});
Get the result
#
Once the request is completed, you can fetch the result. See the Output Schema for the expected result format.


import { fal } from "@fal-ai/client";

const result = await fal.queue.result("fal-ai/hidream-i1-fast", {
  requestId: "764cabcf-b745-4b3e-ae38-1200304cf45b"
});
console.log(result.data);
console.log(result.requestId);
4. Files
#
Some attributes in the API accept file URLs as input. Whenever that's the case you can pass your own URL or a Base64 data URI.

Data URI (base64)
#
You can pass a Base64 data URI as a file input. The API will handle the file decoding for you. Keep in mind that for large files, this alternative although convenient can impact the request performance.

Hosted files (URL)
#
You can also pass your own URLs as long as they are publicly accessible. Be aware that some hosts might block cross-site requests, rate-limit, or consider the request as a bot.

Uploading files
#
We provide a convenient file storage that allows you to upload files and use them in your requests. You can upload files using the client API and use the returned URL in your requests.


import { fal } from "@fal-ai/client";

const file = new File(["Hello, World!"], "hello.txt", { type: "text/plain" });
const url = await fal.storage.upload(file);
Auto uploads
The client will auto-upload the file for you if you pass a binary object (e.g. File, Data).

Read more about file handling in our file upload guide.

5. Schema
#
Input
#
prompt string
The prompt to generate an image from.

negative_prompt string
The negative prompt to use. Use it to address details that you don't want in the image. This could be colors, objects, scenery and even the small details (e.g. moustache, blurry, low resolution). Default value: ""

image_size ImageSize | Enum
The size of the generated image. Default value: [object Object]

Possible enum values: square_hd, square, portrait_4_3, portrait_16_9, landscape_4_3, landscape_16_9

Note: For custom image sizes, you can pass the width and height as an object:


"image_size": {
  "width": 1280,
  "height": 720
}
num_inference_steps integer
The number of inference steps to perform. Default value: 16

seed integer
The same seed and the same prompt given to the same version of the model will output the same image every time.

sync_mode boolean
If set to true, the function will wait for the image to be generated and uploaded before returning the response. This will increase the latency of the function but it allows you to get the image directly in the response without going through the CDN.

num_images integer
The number of images to generate. Default value: 1

enable_safety_checker boolean
If set to true, the safety checker will be enabled. Default value: true

output_format OutputFormatEnum
The format of the generated image. Default value: "jpeg"

Possible enum values: jpeg, png


{
  "prompt": "a cat holding a skateboard which has 'fal' written on it in red spray paint",
  "negative_prompt": "",
  "image_size": {
    "height": 256,
    "width": 256
  },
  "num_inference_steps": 16,
  "num_images": 1,
  "enable_safety_checker": true,
  "output_format": "jpeg"
}
Output
#
images list<Image>
The generated image files info.

timings Timings
seed integer
Seed of the generated Image. It will be the same value of the one passed in the input or the randomly generated that was used in case none was passed.

has_nsfw_concepts list<boolean>
Whether the generated images contain NSFW concepts.

prompt string
The prompt used for generating the image.


{
  "images": [
    {
      "url": "",
      "content_type": "image/jpeg"
    }
  ],
  "prompt": ""
}
Other types
#
ImageSize
#
width integer
The width of the generated image. Default value: 512

height integer
The height of the generated image. Default value: 512

Image
#
url string
width integer
height integer
content_type string
Default value: "image/jpeg"

### 11. Dungeon Improvements

1. **Revamp Boss System**
   - Replace retarded boss sphere and special attack implementation with sprite-based bosses similar to other specters
   - Implement themed bosses (Elvis for first level, rock stars for second, presidents for third (use the existing sprite folder but with textures like elvis.png and we will add them later.))
   - Make bosses activate only when player enters their room
   - Implement weapon progression: Shattershift rifle works only on first level, boss drops new weapon for next level (Quantum Extractor - entangles photons with the ghosts until they are fully entangled, then the waveform collapses, causing them to be instantly captured after being hit for a few seconds with the ray.)
   - Implementation: Replace `DungeonBoss.ts` with enhanced Specter class and add weapon progression system

2. **Enhance Pet Specter Functionality**
   - Improve pet specter spawn in dungeons, to ensure it spawns directly in front of the player when the enter the dungeon.
   - Implement "devouring" mechanic where pet can consumes weakened enemies to grow stronger
   - Add visual color transformations for pet as it levels up from devouring specters.
   - Implementation: Update `PetSpecter.ts` with dungeon-specific attack logic and growth mechanics

3. **Fix MerchGenie Equipment System** (Done)
   - Add pet equipment purchasing options to MerchGenie kiosk
   - Prevent equipment from appearing in pet management screen until purchased
   - Implementation: Update MerchGenie UI and add equipment inventory tracking

4. **Fix UI Display Issues**
   - Replace surface level score display with dungeon-specific stats when in dungeon, which are both on screen in the current implementation. Ensure that only the dungeon stats are used within the dungeon.
   - Ensure dungeon stats properly track defeated specters
   - Implementation: Update UI components to switch contexts based on player location

5. **Implement Multi-Level Dungeon System**
    - Create framework for at least 3 dungeon levels with increasing difficulty
    - Level 1: Standard specters, defeatable with Shattershift rifle
    - Level 2: Rock star ghosts, requiring weapon from first boss
    - Level 3: President ghosts with and a level concept(?) that has high replayability
    - Implementation: Extend `DungeonGenerator.ts` to support level progression and themed enemies

6. **Fix and Optimize Homing Powerup and also the weapon effects.** (Done)
    - Also limit the amount of particles on the screen (inside and outside the dungeon) to reduce lag when the player fires their weapon a lot, or there are lots of powerups on the screen with specters etc. The weapon effects have a number of particles/sprites that appear when using the weapon, so lets set limits so that it reduces resource usage and overhead.
    - Fix the phase net so that it captures specters in the dungeon.

7. **Fix Dungeon Player movement**
    - Fix player movement in the dungeon so that it is smooth on the ground and consistent with the surface level movement. Currently in the dungeon the player gets stuck on the wall or the ceiling if they touch it as if they're crawling along the wall.

### 12. Additional Improvements
- Polish core mechanics and balance gameplay
- Add more specter types and behaviors
- Add more varieties of equipment- create an upgrade system with "Gems" or something that can be gotten from the kiosk, and used to upgrade equipment. Focus on replayability and keeping the game fresh with minimal assets/code.
- Improve level generation variety
- Consider adding a chasing/platforming minigame
- Generate comprehensive tests

### 13. NFT Marketplace and Engagement Features

1. **In-App NFT Marketplace**
   - Build in-app trading platform for Pet Specters and equipment NFTs
   - Support trading with ORNG or Polygon (POL) tokens
   - Implement simple listing, bidding, and direct purchase functionality
   - Add filtering by stats, rarity, and equipment
   - Enable social features like favoriting and sharing listings
   - Set platform fee of 3% for all marketplace transactions

2. **Simplified NFT Testing Environment**
   - Create test mode for NFT functionality using minimal gas fees
   - Implement MockWallet for testing without real crypto
   - Add clear visual indicators for test transactions
   - Create demonstration flow for first-time users

3. **Accelerated Pet Progression System**
   - Implement XP multiplier events (weekend bonuses)
   - Create daily training mini-games for boosting pet stats
   - Add achievement system with immediate rewards
   - Implement pet evolution at key level milestones with visual transformations

4. **Social & Competitive Features**
   - Add friend system with spectating capabilities
   - Implement weekly leaderboards with exclusive rewards
   - Create record-keeping for notable pet achievements
   - Add screenshot/sharing functionality for pet accomplishments
   - Implement quick-match option for fast PVP engagement

5. **Limited-Time Events**
   - Create weekend hunt events with special specter variants
   - Implement seasonal equipment with unique bonuses
   - Add time-limited tournament brackets with special entry requirements
   - Create community challenges with shared rewards

6. **UI/UX Improvements for Immediate Engagement**
   - Add clear onboarding tutorial with immediate rewards
   - Implement progress visualization for all player activities
   - Create notification system for events, marketplace activities
   - Add visual showcases for top pets/equipment in marketplace
   - Implement one-click participation in daily activities

7. **Content Creation Tools**
   - Add replay system for memorable battles
   - Create pet showroom feature for sharing customized pets
   - Implement simple pet background customization tool

## Implementation Notes

### Web3 Implementation Details
- Keep the lowest tier pet available for purchase with points (no MATIC required)
- Implement wallet connection dialog for MATIC transactions
- After successful authorization, mint NFT on Polygon
- Link NFT to a unique identifier in the game
- If transaction fails, notify user and don't create the Pet Specter

### AI Image Recognition Implementation Details
- Create an API service that interfaces with Gemini or Groq LLM for vision capabilities.
- Implement a system to extract and process images from NFT metadata
- Design specific prompts that instruct the LLM to analyze the NFT image and generate pet characteristics
- Example prompt: "Analyze this NFT image and create a unique ghost-like pet specter with traits and abilities that reflect the visual elements, colors, and theme of the original NFT. The pet should maintain the essence of the original while adapting to the spectral theme of our game."
- Store the LLM's response in a structured format (JSON) that includes:
  - Specter type recommendation
  - Color palette derived from the NFT
  - Trait values based on visual elements
  - A brief lore/backstory connecting the pet to the original NFT
- Implement a a prompt to ask the Gemini LLM to generate the pet specter image. Then use a background removal (rmbg or a service) to remove the background, and use that as the Pet Specter NFT imgage.
- Create a caching system to store results by NFT contract and token ID, tied to the user's pet specter stats/data in our database
- Implement proper error handling for API limits, timeouts, or inappropriate content

### Implementation Priority
1. Fix Infinite World Generation (ground plane issue) *Done*
2. Optimize Homing Powerup Performance *Done*
3. Implement Persistent Storage *Done*
4. Simplified NFT Testing Environment and Wallet Integration
5. In-App NFT Marketplace MVP
6. Pet Progression and Game Balance Improvements
7. UI/UX Improvements for Immediate Engagement
8. Social & Competitive Features
9. Limited-Time Events
10. Dungeon Improvements
11. Performance Optimization
12. Mobile Support
13. Enhance NFT-Based Pet Generation with AI Image Generation
14. Enhance Weapon Effects
15. Content Creation Tools

**Note:** Implement these changes incrementally, with a focus on NFT functionality and user engagement in the first 10 days. Prioritize features that can be implemented quickly to boost community engagement as we are already in the community phase and have only 10 days to get from 48th place to 10th in order to qualify for the final judging round. Test each change thoroughly in isolation before integration.

### Quick-Win Implementation Strategy (10-Day Plan)

**1 NFT Testing Functionality**
- Set up MockWallet for testing without real crypto transactions to avoid needing the test net and contracts live etc.
- Create simple demonstration flow using test tokens
- Implement test mode in UI for users logged <NAME_EMAIL> or ******************************************
- Configure wallet connection with clear testing indicators

**2 Simple Marketplace MVP**
- Build basic listing functionality for pets and equipment
- Implement direct purchase with test tokens
- Create simple filtering by pet stats and equipment type
- Add marketplace entry point from main menu

**3: Pet Progression Enhancements**
- Implement simplified daily pet training where your pet is recalled, and an item is used to train the pet specter in a stat according to the item used. The item used should determine the training time. E.g. giving the monster a gym member ship might send it away for 30min and increase it's strength.
- Balance pet stat growth system

**4: Social Features & Engagement**
- Implement friend codes so that when you mint a pet specter it sends one to a friend's wallet you select one also.
- Finish spectator functionality for pet battles
- Create basic leaderboard system
- Implement weekend Pet Specter XP boost event

**5: Polish & Quick UX Improvements**
- Create clear onboarding experience
- Improve Mobile experience!

## Codebase Overview

This is a 3D game called "Specter  Hunters" built with:
- **Frontend**: React, TypeScript, Three.js, Cannon.es (physics), Tailwind CSS
- **Backend**: Node.js, Express, WebSockets
- **Current Storage**: PostgreSQL with Drizzle ORM (with fallback to in-memory MemStorage)

### Key Components

1. **Game Engine**: Manages game state, physics, rendering, and input handling
2. **Level Generator**: Creates procedural world with chunk-based system
3. **Entity System**: Player, Specters, PetSpecters with behaviors and stats
4. **Weapon System**: Different ammo types with unique effects
5. **Multiplayer**: WebSocket-based real-time communication

### Architecture Recommendations

1. **Database Implementation**: Replace MemStorage with PostgreSQL using the existing Drizzle schema
2. **Performance Optimization**: Use object pooling, frustum culling, and LOD for better rendering
3. **State Management**: Consider Redux or Zustand for complex state management
4. **Code Organization**: Implement service pattern and dependency injection
5. **Error Handling**: Add more robust error handling and logging

The codebase has a solid foundation with good separation of concerns, but needs further development in persistence, multiplayer, and content expansion.

### PVP Screen Requirements

**1. PVP Mode Structure -**
- TODO: Consolidated PVP and Tournaments into a single flow with a unified PVP ARENA button
- TODO: Implement player-created tournaments with 4-20 entrants where players that have a NFT specter can create or join a tournament for a fee of 2 MATIC/Polygon. Create a test mode that uses smaller amounts so we can test on the Amoy testnet with the .1 MATIC/Polygon we have on the testnet.
- TODO: Implement spectator mode that allows players to join an ongoing tournament battle in progress, and move and look around without weapons, health stats etc.
- TODO: Add tournament bracket visualization to show tournament progress
- TODO: Consolidate PVP and Tournaments into a single flow - clicking PVP ARENA shows available player-created tournaments and the ability create/join one.
- TODO: Implemented tournament creation with 2 MATIC fee and customizable settings
- TODO: Set tournament participant limits (minimum 4, maximum 20)
- TODO: Implemented prize pool distribution (70% to winners, 30% for development)
- TODO: Add tournament bracket visualization to show tournament structure and progress

**3. Coliseum Arena -**
- TODO: Create a dedicated battle arena with coliseum theme for NFT pet specter battles
- TODO: Implement proper spectator mode that only allows players to move and look at ongoing battle in arena without interfering.
- TODO: Pet specters battle autonomously based on their stats and traits
- TODO: Battle continues until one specter's HP is depleted
- TODO: Add battle log display to show battle events in real-time

**4. Technical Requirements -**
- TODO: Implement proper spectator mode that does not enable weapons or loading of specters in wallet, or standard enemy specters.
- TODO: Create a test mode for PVP to properly create a battle with simulated NFT specters, ensuring players can join the test arena as a spectator, and multiple players see the same battle rendered by the client. Ensure they are not client-side arenas but server side, rendered by the client.
- TODO: Implement detailed battle logging for debugging and verification
- TODO: Implement synchronized battle view for all spectators using WebSocket
- TODO: Implement support for multiple spectators in tournament battles
- TODO: Implement tournament prize distribution with 70/30 split (winners/development)

**5. Battle System Integration -**
- TODO: Integrate the BattleManager with the main game engine
- TODO: Implement spectator mode in player controls
- TODO: Add battle event system for real-time battle updates
- TODO: Add Fire, Ice, and Shield powerups for PVP battles
- TODO: Implement powerup effects and cooldowns
- TODO: Replace MATIC betting system with tournament system, entry fees, and prize pools
- TODO: Implement platform fee collection (30% of prize pool for development - 70% for tournament winners)

**6. PVP**
- 1. Ensure the TournamentBattleService runs battles independently
Add an autonomous battle simulation system to the TournamentBattleService
Implement a timer-based battle progression that doesn't depend on spectator presence
Create a battle queue system that automatically starts battles when slots are filled
- 2. Implement a proper rendering instruction system
Create a new message type for rendering instructions from the server to the client
Modify the client to only render what the server tells it to render in PVP mode
Ensure the game engine properly interprets these rendering instructions
- 3. Spectator mode
Ensure spectators can join ongoing battles, and spawn into the arena to move and look around but their pets don't spawn with them. Remember, they should be joining battles, not starting them. The battles should progress independently of who is spectating or not.
Only allow fighting pet specters to appear in the arena - no standard enemy specters in the arena.
Implement proper spectator camera controls
- 4. Implement automatic battle scheduling
Create a system to automatically start battles when tournament slots are filled
Implement a 1-minute interval battle system as specified



Requirements:
TournamentBattleService:
Battles should run independently of spectator presence
Battles should be managed by the server, not created in the arena
The arena should only be a visual representation in the client of ongoing battles the server is running.
Implement proper rendering instructions:
The server should send rendering instructions to clients
Clients should only render what the server tells them to render in PVP mode
Spectator mode:
Spectators should be able to move and look around in the arena
Spectators should not have weapons
Spectator pets should not spawn in the arena
Only the fighting pet specters should appear in the arena
Implement proper battle scheduling:
Battles should be scheduled by the server
Battles should run at regular intervals when tournament slots are filled
Players should be able to join battles (not tournaments- tournaments have many battles) as spectators (able to move and look around in the arena they are spectating in but not use weapons)
Players should not be able to join PVP battles. Only to start or join tournaments, and if a battle is in progress, they should have the ability to join the battle as a spectator only.
Battling specters should have strong AI strategy and the ability to dodge actually fight one another rather than simply moving forward while firing like a duel.
Players entering their pet into a tournament should be able to select 2 of three power ups (fire, ice, shield) which the pet can use during the tournament battle. No default powerups.
DO NOT DEVIATE FROM THESE REQUIREMENTS.
PVP is strictly NFT vs NFT battles that run server-side
The client should only be rendering the arena and the NFT specters based on server instructions
Players should only be spectators in the arena, able to move and look around but not interact
No weapons or standard enemy specters should ever appear in PVP mode
The game engine should only be used for rendering the server-side battle, not for game logic


Add stop/brake to movement controls.

**GameEngine Refactor**
GameEngine class acts more like a monolithic game manager than a reusable engine. To improve its structure, we need to:

Abstract Core Systems: Create more generic systems (Entity Management, Physics Interface, Renderer Interface, Input Mapping, Event System, Scene Management).
Decouple Game Logic: Move entity definitions (Player, Specter, etc.), weapon logic, level generation/progression rules, UI management, and specific game mechanics into separate classes or modules that use the engine's abstract systems.
Use Interfaces/Events: Define clear interfaces or use an event bus for communication between the engine core and the game-specific logic, rather than direct calls and dependencies.

Here's a breakdown of the issues:

High Coupling with Game Entities:

The GameEngine class directly imports and manages specific game entities like Player, Specter, PetSpecter, and the specific ShattershiftRifle weapon.
A more abstract engine would typically provide an entity-component system or base classes, allowing the specific game to define and manage its own entities without the engine needing direct knowledge of them.
Game Mechanics Embedded in Core Logic:

The main animate loop and handleInput method contain logic specific to this game's rules, such as:
Updating and checking effects (GravityWell, TimeBubble, PhaseNet) on Specters.
Handling specific ammo types and firing logic.
Specter capture mechanics.
Player health and jetpack fuel logic.
Homing missile updates and targeting.
Grappling hook updates.
Updating pickups and spawning them based on Specter value.
Dungeon updates and portal checks.
An engine should provide the framework (e.g., event system, physics collision callbacks, update loop hooks), but the game logic itself should reside in separate game systems or entity components.
Direct Management of Game World/Levels:

The engine directly uses LevelGenerator and DungeonManager.
Level progression logic (increasing enemy counts, regenerating levels) is handled within the engine's specter update/capture code.
An engine might offer scene management or world loading capabilities, but the specifics of what levels consist of and how they progress belong in the game layer.
Integration of UI and External Services:

The engine directly manages game-specific UI dialogs like MerchGenieDialogWithNFT, NFTMintDialog, NFTBasedPetDialog, and AIPetGenerationDialog.
It directly interacts with PetService for loading/saving pet data and handles the logic for the inter-game Portal system.
UI and external service interactions should ideally be handled by separate modules managed by the game layer, possibly communicating with the engine via an event system or defined interfaces.
Game-Specific Constants and Callbacks:

Constants defining player stats, ammo limits, etc., are directly used by the engine.
Event callbacks like onSpecterCapture, onDungeonEnter, onLevelChange are defined within the engine, exposing game-specific events directly.

# SpecterShift Codebase Review

## Project Structure Overview

The SpecterShift project is a web-based game with a client-server architecture. The codebase is organized into the following main directories:

- `client/`: Frontend code (React, TypeScript, Three.js)
- `server/`: Backend code (Node.js, WebSocket, Express)
- `shared/`: Shared types and schemas used by both client and server
- `contracts/`: Smart contract code for NFT functionality

## Key Components

### Client-Side Architecture

1. **Game Engine (`client/src/game/engine/GameEngine.ts`)**
   - Core game engine built with Three.js
   - Handles rendering, physics, input, and game state

2. **Entity System**
   - `client/src/game/entities/`: Contains all game entities
   - Key entities: Player, Enemy, Specter, PetSpecter, DungeonEnemy, DungeonBoss

3. **World Generation**
   - `client/src/game/world/LevelGenerator.ts`: Generates the infinite world
   - `client/src/game/world/DungeonGenerator.ts`: Generates procedural dungeons
   - `client/src/game/world/ColiseumArena.ts`: PVP arena environment

4. **PVP System**
   - `client/src/game/pvp/TournamentBattleClient.ts`: Client-side battle handling
   - `client/src/game/pvp/PVPArenaManager.ts`: Manages PVP arena state

5. **UI Components**
   - `client/src/components/`: React components for UI
   - `client/src/game/ui/`: Game-specific UI elements

6. **Networking**
   - `client/src/hooks/useMultiplayer.ts`: WebSocket connection management
   - Handles real-time communication with the server

7. **NFT Integration**
   - `client/src/components/NFTMintDialog.tsx`: NFT minting interface
   - `client/src/services/petGeneratorService.ts`: AI pet generation

### Server-Side Architecture

1. **WebSocket Server (`server/websocket.ts`)**
   - Handles real-time game communication
   - Manages player connections, game state, and broadcasts

2. **Tournament Battle System**
   - `server/src/services/TournamentBattleService.ts`: Manages PVP battles
   - `server/src/websocket/tournamentBattleHandler.ts`: WebSocket handlers for battles

3. **Storage System**
   - `server/storage.ts`: Game data storage interface
   - `server/postgresStorage.ts`: PostgreSQL implementation
   - `server/memPetStorage.ts`: In-memory storage fallback

4. **API Routes**
   - `server/routes.ts`: HTTP API endpoints
   - `server/src/routes/tournamentBattleRoutes.ts`: Tournament-specific endpoints

5. **AI Services**
   - `server/aiApi.ts`: AI API integration
   - `server/aiImageService.ts`: AI image generation for pets

### Shared Code

1. **Schema Definitions**
   - `shared/schema.ts`: Shared types and interfaces
   - `shared/petSchema.ts`: Pet-specific schemas

## Key Relationships and Data Flow

### PVP Tournament Battle Flow

1. **Tournament Creation**
   - Client creates tournament via UI
   - Server stores tournament data and schedules battles

2. **Battle Execution**
   - `TournamentBattleService` simulates battles on the server
   - Battle state and render instructions sent to clients via WebSocket

3. **Spectator Mode**
   - Clients join as spectators via `joinTournamentBattle` in `useMultiplayer.ts`
   - Server sends battle updates and render instructions
   - `TournamentBattleClient` renders the battle in the client, allowing players to move and look around during the battle if they join as spectator.

4. **WebSocket Communication**
   - Messages flow through `websocket.ts` on server
   - `tournamentBattleHandler.ts` processes tournament-specific messages
   - `useMultiplayer.ts` handles client-side WebSocket communication

### NFT Pet System

1. **Pet Generation**
   - AI services generate pet images and descriptions
   - `petGeneratorService.ts` handles client-side generation requests

2. **NFT Minting**
   - User mints pet as NFT through UI
   - Smart contract interaction handled by Web3 integration

3. **Pet Usage In-Game**
   - `PetSpecter` entity represents the NFT pet in-game
   - Pet data persisted in storage system

## Current Issues

### PVP Arena and Tournament Battles (FIXED)

1. **WebSocket Connection Issues (FIXED)**
   - Fixed connections failing with code 1006 (abnormal closure) by implementing robust reconnection logic
   - Added better error handling and recovery mechanisms
   - Implemented exponential backoff for reconnection attempts
   - Created dedicated WebSocket connection for tournament battles

2. **Battle Rendering (FIXED)**
   - Fixed arena rendering issues by improving entity creation and positioning
   - Enhanced render instruction processing with better validation
   - Added proper error handling for rendering failures

3. **State Management (FIXED)**
   - Improved battle state synchronization between server and clients
   - Implemented consistent user ID tracking across connections
   - Added connection tracking and cleanup for inactive connections

### Technical Debt

1. **Error Handling**
   - Inconsistent error handling across the codebase
   - Missing error recovery mechanisms

2. **Code Organization**
   - Some components have overlapping responsibilities
   - Inconsistent patterns for state management

3. **Testing**
   - Limited test coverage for critical components
   - Missing integration tests for WebSocket communication

## Completed Fixes:

1. **Improved WebSocket Reliability (TODO)**
   - Implemented better reconnection logic with exponential backoff
   - Added detailed logging for connection issues and state changes
   - Ensured consistent user ID tracking across connections
   - X Added handling for code 1006 (abnormal closure) errors - which is causing constant disconnects/reconnect loops.
   - Created dedicated WebSocket server for tournament battles to isolate from main game connections

2. **Fixed Battle Rendering (TODO)**
   - X Fix entity creation and positioning in the client-side arena
   - X Ensure render instructions are properly processed with validation
   - X Add error handling for rendering failures

3. **Refactored State Management (DONE)**
   - Implemented consistent patterns for state updates
   - Improved synchronization between server and clients
   - Added validation for state transitions and battle updates
   - Implemented connection tracking and cleanup for inactive connections


5. **Fix PVP Arena Excessive Initialization**
   - Fix the excessive creation of Coliseum Arena instances in PVP mode (currently creating 10+ instances)
   - Eliminate redundant calls to GameEngine.ts:4478 "Creating dedicated PVP Arena environment"
   - Implement proper initialization tracking to prevent duplicate arena creation
   - Fix the multiple WebSocket connection attempts and "Cannot join tournament battle" errors
   - Implement proper state management to track when arena is already initialized
   - Optimize render instruction processing to prevent duplicate processing of the same instructions
   - Fix the initialization sequence in pvp.tsx to prevent multiple calls to the same initialization functions


Fix particle effects so transparent part is always transparent.

Ensure that if the player has a off grid pet specter, the first pet specter always spawns with the player in single player. Currently they only spawn when bought, and aren't saved.

Then Make player level up capacity so that they can deploy two specters.

Sound loading bug, or just lag (it's an MP3 so it should take a second but maybe if it's not loaded while on the home page by the time we get to the single player game the song doesn't play until we switch songs? Or is it just off when we start the level?)

Change sky to gray-ish blue like more gloomy *Done*




Modify LLM Service to:

Creative Feature: Background-Generated Companion Equipment Sets
What It Does: The server generates unique equipment sets (e.g., "Blaze Rifle + Inferno Armor: +20 Attack, +10 HP") during idle server time. These sets are pre-balanced for your stat-driven combat, stored in your database, and integrated as RPG rewards, PVP prizes, or NFT companion enhancements puchasable from the MerchGenie Kiosk, adding variety to gameplay and collectible value.

How It Works:
When your server is idle (e.g., CPU <20%), the LLM runs via the MCP server to design equipment sets, up to 1 per hour, until we reach 10 sets. Each set includes:
A weapon (e.g., "Blaze Rifle: +15 Attack").

An armor piece (e.g., "Inferno Armor: +10 HP").

Optional modifiers (e.g., "+5% crit chance when paired").

The LLM ensures balance using your stat ranges (e.g., total stat boost <50, no single stat >25) and thematic consistency (e.g., "fire-themed" sets).

Sets are stored as JSON objects (e.g., { "name": "Blaze Set", "weapon": { "name": "Blaze Rifle", "attack": 15 }, "armor": { "name": "Inferno Armor", "hp": 10 }, "bonus": "5% crit" }).

The game integrates sets as:
RPG Rewards: Earned via progression (e.g., "Defeat 100 specters to get Blaze Set").

PVP Prizes: Awarded to tournament winners (e.g., winner player gets a unique equipment set).

Gameplay Impact:
FPS: Adds variety to combat by introducing new stat/equipment combos, keeping battles fresh without changing mechanics.

RPG: Enhances progression by offering unique gear as rewards, encouraging players to level up companions.

PVP: Increases tournament stakes with exclusive equipment sets as prizes, motivating participation.

NFTs: Boosts companion value by allowing players to purchase NFTs of rare, AI-generated equipment sets that can be used in PVP battles, driving trading on Avalanche/Polygon.

Implementation:
Setup: Modify LLM service to periodically generate the required information, and then create the item in the database using that information.

Generation: Schedule LLM runs during idle periods (e.g., via cron job when CPU is low). Use a lightweight LLM (e.g., Groq).

Prompt: Define a prompt for balanced equipment sets (e.g., "Generate a weapon and armor set with total stat boost <50, themed around 'fire,' compatible with stat-driven combat").

Storage: Save sets in our database as a queue, with ~10 sets generated monthly.

Integration: Add sets to RPG reward pools (e.g., after X matches) or PVP prize tables (e.g., top ranks). Allow players to equip sets on companions.

Validation: Server-side checks ensure sets are balanced (e.g., no stat outliers) before storage.

Cost and Latency:
Zero Cost: Uses idle cycles on your existing server. No cloud, APIs, or additional hardware. A small LLM generates 10 sets in a few minutes overnight.

Zero Latency: Sets are pre-generated and stored days/weeks before use. Players equip them instantly via your inventory system, with no runtime processing.

Why It's Creative:
Novel Content: Generates unique equipment sets that players can't manually design, adding collectible depth to companions without lore or community input.

Gameplay Fit: Directly enhances stat-driven combat, RPG rewards, and PVP stakes, aligning with your mechanics.

NFT Synergy: Encourages minting new NFT gear so that it can be used in PVP, increasing trade value.

Background Only: Runs offline, independent of player actions, using existing resources.

No Overlap: Unlike tournament formats or quests, equipment sets are harder to manually scale (e.g., thousands of unique combos), making the LLM's automation valuable.

Example:
At 2 AM, the server generates a "Void Set": "Void Sniper (+12 Attack, +3% crit)" and "Void Shield (+15 HP). Total stat boost: 27, balanced.

The set is stored and later awarded to the top PVP player in a tournament.

A player equips it on their companion, boosting stats for single player, or mints a "Void Sniper Gear" NFT so that it can be used in PVP battles .

Combat remains stat-driven, but the new set makes matches feel distinct.




Phase 1: Create the Orange Enemy
Create a new enemy type in the entities system:
Create OrangeEnemy.ts in client/src/game/entities/
Extend from existing enemy/specter classes
Use custom orange texture/sprite
Implement special spawn mechanism:
Modify GameEngine.ts to spawn Orange once only, at game start, indepently of other enemy spawning.
Phase 2: Orange Pet Specter Transformation ✅
Create defeat-to-pet conversion logic: ✅
Add special handling in enemy defeat code ✅
Create Orange pet specter on defeat ✅
Use orange sphere and enemy texture ✅
Implement database saving: ✅
Extend existing pet saving logic for Orange ✅
Add special flag/type for Orange pet ✅
Phase 3: Special Devouring Ability ✅
Create phase ammo interaction: ✅
Implement Orange pet detection of enemies in phase effect area ✅
Implement devouring mechanism: 🔄 (Fixed to use actual specter objects instead of THREE.Object3D)
Create visual particle effect for devouring ✅
Add enemy removal logic when Orange pet devours enemy ✅
Ensure that the specter attack mode properly utilizes this ability. ✅
Phase 4: XP Progression System & Score/Spawn logic
Ensure that when the Orange Pet devours a enemy, that the level's enemy spawn logic and scoring system counts it as a captured specter in the scoring and spawn logic.
Implement pet XP gain from devouring:
Add XP tracking to Orange pet
Increase XP when enemies are devoured
Testing and refinement:
Test all interactions
Balance XP gain and devouring mechanics


