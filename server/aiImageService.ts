import { fal } from '@fal-ai/client';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import crypto from 'crypto';
import { exec } from 'child_process';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Set up Fal.ai client with API key from environment variables
const falKey = process.env.FAL_KEY;
console.log('FAL_KEY status:', falKey ? 'Found' : 'Not found');

if (falKey) {
  try {
    fal.config({
      credentials: falKey
    });
    console.log('Fal.ai client configured successfully');
  } catch (error) {
    console.error('Error configuring Fal.ai client:', error);
  }
} else {
  console.warn('FAL_KEY environment variable not set. AI image generation will not work.');
}

// Constants
const IMAGE_STORAGE_PATH = process.env.IMAGE_STORAGE_PATH || './uploads/images';
const MAX_CACHE_AGE_DAYS = parseInt(process.env.MAX_CACHE_AGE_DAYS || '30', 10);
const MAX_REQUESTS_PER_HOUR = 50; // Rate limiting
const MODEL_ID = 'fal-ai/hidream-i1-fast'; // The AI model to use

// Create storage directory if it doesn't exist
if (!fs.existsSync(IMAGE_STORAGE_PATH)) {
  fs.mkdirSync(IMAGE_STORAGE_PATH, { recursive: true });
}

// Rate limiting
const requestCounts: Map<string, { count: number, timestamp: number }> = new Map();

// Cache for generated images
interface CachedImage {
  path: string;
  timestamp: number;
}
const imageCache: Map<string, CachedImage> = new Map();

/**
 * AI Image Generation Service
 */
export class AIImageService {
  /**
   * Generate a pet specter image based on user preferences
   */
  static async generatePetSpecterImage(
    preferences: {
      specterType: string;
      description?: string;
      color?: string;
      style?: string;
    },
    userId: string,
    bypassCache: boolean = false
  ): Promise<{ url: string, localPath: string, prompt: string }> {
    try {
      // Check rate limiting
      this.checkRateLimit(userId);

      // Create a cache key based on preferences
      const cacheKey = this.createCacheKey(preferences);

      // Check if image is already in cache (unless bypass is requested)
      if (!bypassCache) {
        const cachedImage = imageCache.get(cacheKey);
        if (cachedImage && fs.existsSync(cachedImage.path)) {
          console.log(`Using cached image for ${cacheKey}`);
          return {
            url: this.getPublicUrl(cachedImage.path),
            localPath: cachedImage.path,
            prompt: preferences.description || ''
          };
        }
      } else {
        console.log(`Bypassing cache for ${cacheKey} as requested`);
      }

      // Check if a description is provided
      if (!preferences.description) {
        throw new Error('Description is required to generate a pet specter image');
      }

      // Use the provided description directly as the prompt
      // This should be the LLM-enhanced prompt from the client
      const prompt = preferences.description;
      console.log('Using prompt for image generation:', prompt);

      // Call Fal.ai API to generate image
      console.log('Calling Fal.ai with parameters:', {
        prompt,
        image_size: { width: 256, height: 256 },
        steps: 16,
        model: MODEL_ID
      });

      // Log the exact API call parameters for debugging
      console.log('Full Fal.ai API parameters:', {
        model: MODEL_ID,
        input: {
          prompt,
          image_size: { width: 256, height: 256 },
          num_inference_steps: 16,
          output_format: "png"
        }
      });

      const result = await fal.subscribe(MODEL_ID, {
        input: {
          prompt,
          negative_prompt: "blurry, cropped, bust, amputee, low quality, distorted, deformed, disfigured, bad anatomy, watermark, signature, text",
          image_size: {
            width: 256,
            height: 256
          },
          num_inference_steps: 16,
          output_format: "png"
        }
      });

      // Log the result details
      console.log('Image generation result details:', {
        width: result.data.images?.[0]?.width || 'unknown',
        height: result.data.images?.[0]?.height || 'unknown',
        url: result.data.images?.[0]?.url
      });

      // Log the full result for debugging
      console.log('Full Fal.ai API response:', JSON.stringify(result.data, null, 2));

      if (!result.data.images || result.data.images.length === 0) {
        throw new Error('No images generated');
      }

      // Get the image URL from the result
      const imageUrl = result.data.images[0].url;

      // Download and process the image
      const processedImagePath = await this.processAndSaveImage(imageUrl, cacheKey);

      // Add to cache
      imageCache.set(cacheKey, {
        path: processedImagePath,
        timestamp: Date.now()
      });

      // Increment request count for rate limiting
      this.incrementRequestCount(userId);

      return {
        url: this.getPublicUrl(processedImagePath),
        localPath: processedImagePath,
        prompt: prompt
      };
    } catch (error) {
      console.error('Error generating pet specter image:', error);
      throw error;
    }
  }



  /**
   * Process and save the generated image
   * - Download the image
   * - Remove background using rembg
   * - Optimize and save as PNG
   */
  private static async processAndSaveImage(imageUrl: string, cacheKey: string): Promise<string> {
    try {
      // Create a unique filename
      const filename = `${cacheKey}.png`;
      const outputPath = path.join(IMAGE_STORAGE_PATH, filename);
      const tempPath = path.join(IMAGE_STORAGE_PATH, `temp_${cacheKey}.png`);

      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }

      const imageBuffer = await response.arrayBuffer();

      // Get image metadata
      const metadata = await sharp(Buffer.from(imageBuffer)).metadata();
      console.log('Original image metadata:', {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format
      });

      // Save the original image to a temporary file without resizing
      await sharp(Buffer.from(imageBuffer))
        .toFile(tempPath);

      console.log('Saved original image to temporary file:', tempPath);

      // Use rembg to remove the background
      // This requires rembg to be installed on the system
      // npm install rembg

      return new Promise((resolve, reject) => {
        console.log('Running rembg command:', `/opt/rembg_venv/bin/rembg i ${tempPath} ${outputPath}`);

        exec(`/opt/rembg_venv/bin/rembg i ${tempPath} ${outputPath}`, async (error: any) => {
          // Clean up the temporary file
          if (fs.existsSync(tempPath)) {
            fs.unlinkSync(tempPath);
            console.log('Removed temporary file:', tempPath);
          }

          if (error) {
            console.error('Error removing background with rembg:', error);
            reject(error);
            return;
          }

          try {
            // Check the image size after rembg
            const afterRembgMetadata = await sharp(outputPath).metadata();
            console.log('Image metadata after rembg:', {
              width: afterRembgMetadata.width,
              height: afterRembgMetadata.height,
              format: afterRembgMetadata.format
            });

            // Resize to 256x256 if needed
            if (afterRembgMetadata.width !== 256 || afterRembgMetadata.height !== 256) {
              console.log('Resizing image to 256x256...');

              // Create a temporary path for the resized image
              const resizedTempPath = path.join(IMAGE_STORAGE_PATH, `resized_${cacheKey}.png`);

              // Resize the image
              await sharp(outputPath)
                .resize(256, 256, {
                  fit: 'contain',
                  background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
                })
                .toFile(resizedTempPath);

              // Replace the original file with the resized one
              fs.unlinkSync(outputPath);
              fs.renameSync(resizedTempPath, outputPath);

              // Check the final image size
              const finalMetadata = await sharp(outputPath).metadata();
              console.log('Final image metadata after resize:', {
                width: finalMetadata.width,
                height: finalMetadata.height,
                format: finalMetadata.format
              });
            }
          } catch (metadataError) {
            console.error('Error processing final image:', metadataError);
          }

          resolve(outputPath);
        });
      });
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  }

  /**
   * Create a cache key based on preferences
   */
  private static createCacheKey(preferences: any): string {
    const preferencesString = JSON.stringify(preferences);
    return crypto.createHash('md5').update(preferencesString).digest('hex');
  }

  /**
   * Get public URL for an image
   */
  private static getPublicUrl(localPath: string): string {
    // Extract filename from path
    const filename = path.basename(localPath);
    // Return URL path with the images subdirectory
    return `/uploads/images/${filename}`;
  }

  /**
   * Check rate limit for a user
   */
  private static checkRateLimit(userId: string): void {
    const now = Date.now();
    const userRequests = requestCounts.get(userId);

    if (userRequests) {
      // Check if the timestamp is within the current hour
      const isWithinHour = now - userRequests.timestamp < 60 * 60 * 1000;

      if (isWithinHour && userRequests.count >= MAX_REQUESTS_PER_HOUR) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      // Reset count if outside the hour
      if (!isWithinHour) {
        requestCounts.set(userId, { count: 0, timestamp: now });
      }
    } else {
      // First request for this user
      requestCounts.set(userId, { count: 0, timestamp: now });
    }
  }

  /**
   * Increment request count for a user
   */
  private static incrementRequestCount(userId: string): void {
    const userRequests = requestCounts.get(userId);
    if (userRequests) {
      userRequests.count += 1;
    }
  }

  /**
   * Clean up old cached images
   */
  static cleanupCache(): void {
    const now = Date.now();
    const maxAge = MAX_CACHE_AGE_DAYS * 24 * 60 * 60 * 1000; // Convert days to milliseconds

    // Check each cached image
    for (const [key, cachedImage] of imageCache.entries()) {
      if (now - cachedImage.timestamp > maxAge) {
        // Remove from cache
        imageCache.delete(key);

        // Delete file if it exists
        if (fs.existsSync(cachedImage.path)) {
          fs.unlinkSync(cachedImage.path);
        }
      }
    }
  }
}

// Set up periodic cache cleanup
setInterval(() => {
  AIImageService.cleanupCache();
}, 24 * 60 * 60 * 1000); // Run once a day
