declare module 'ethers' {
  export class JsonRpcProvider {
    constructor(url: string);
  }

  export class Wallet {
    constructor(privateKey: string, provider: JsonRpcProvider);
  }

  export class Contract {
    constructor(address: string, abi: any, signerOrProvider: Wallet | JsonRpcProvider);
    
    // Add contract methods we need
    endTournament(tournamentId: number, winners: number[], percentages: number[]): Promise<{
      hash: string;
      wait(): Promise<{
        blockNumber: number;
      }>;
    }>;
  }
} 