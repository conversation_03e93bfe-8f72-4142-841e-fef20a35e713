import { PowerupType, MessageType, RenderInstructionType } from '../../../shared/schema';
import { WebSocketServer, WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { sendBattleMessage, sendBattleMessageToUser } from '../../unifiedWebSocket';
// @ts-ignore - Ethers doesn't have proper types installed
import { ethers } from 'ethers';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Use ES Module compatible path resolution
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Calculate root directory path (3 levels up from current file)
const rootDir = path.resolve(__dirname, '../../../');

// Load contract ABI with absolute path from root directory
const contractAbiPath = path.join(rootDir, 'contracts/abi/PetSpecterNFT.json');
let contractAbi: any;

try {
  console.log(`Loading contract ABI from: ${contractAbiPath}`);
  contractAbi = JSON.parse(fs.readFileSync(contractAbiPath, 'utf8'));
  console.log('Contract ABI loaded successfully');
} catch (error) {
  console.error(`Error loading contract ABI from ${contractAbiPath}:`, error);
  console.error('Current directory:', process.cwd());
  try {
    console.error('Available files in contracts/abi:', fs.readdirSync(path.join(rootDir, 'contracts/abi')));
  } catch (e) {
    console.error('Error listing contract directory:', e);
  }
  // Create an empty object to prevent further errors
  contractAbi = {};
}

// Contract address from environment variables
const contractAddress = process.env.PET_SPECTER_NFT_ADDRESS;
const providerUrl = process.env.PROVIDER_URL || 'https://polygon-amoy.g.alchemy.com/v2/demo';
const privateKey = process.env.CONTRACT_OWNER_PRIVATE_KEY;

/**
 * Interface for a tournament participant
 */
interface TournamentParticipant {
  id: string;
  userId: string;
  petId: string;
  petName: string;
  petLevel: number;
  powerups: PowerupType[];
  health: number;
  maxHealth: number;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
}

/**
 * Interface for a battle
 */
interface Battle {
  id: string;
  tournamentId: string;
  participants: TournamentParticipant[];
  spectators: string[];
  status: 'pending' | 'in_progress' | 'completed';
  startTime?: number;
  endTime?: number;
  winnerId?: string;
  battleLog: any[];
  nextUpdateTime: number;
}

/**
 * TournamentBattleService
 *
 * Manages tournament battles on the server side.
 * Handles battle simulation, scheduling, and client updates.
 */
export class TournamentBattleService {
  private wss: WebSocketServer;
  private battles: Map<string, Battle> = new Map();
  private battleQueue: string[] = [];
  private tournaments: Map<string, any> = new Map();
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL_MS = 100; // 10 updates per second
  private readonly BATTLE_INTERVAL_MS = 60000; // 1 minute between battles
  private readonly TEST_BATTLE_INTERVAL_MS = 5000; // 5 seconds for test battles
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private contract: ethers.Contract;

  /**
   * Constructor
   * @param wss WebSocket server instance
   */
  constructor(wss: WebSocketServer) {
    this.wss = wss;
    this.startUpdateLoop();
    
    // Initialize blockchain connection
    try {
      this.provider = new ethers.JsonRpcProvider(providerUrl);
      
      if (!privateKey) {
        console.warn('CONTRACT_OWNER_PRIVATE_KEY not set - contract functions will not work');
        this.wallet = new ethers.Wallet('0x0000000000000000000000000000000000000000000000000000000000000001', this.provider);
      } else {
        this.wallet = new ethers.Wallet(privateKey, this.provider);
      }
      
      if (!contractAddress) {
        console.warn('PET_SPECTER_NFT_ADDRESS not set - contract functions will not work');
      } else {
        this.contract = new ethers.Contract(contractAddress, contractAbi, this.wallet);
        console.log('Blockchain connection initialized with contract at', contractAddress);
      }
    } catch (error) {
      console.error('Failed to initialize blockchain connection:', error);
      // Create empty objects to prevent errors
      this.provider = {} as ethers.JsonRpcProvider;
      this.wallet = {} as ethers.Wallet;
      this.contract = {} as ethers.Contract;
    }
  }

  /**
   * Start the update loop for battle simulation
   */
  private startUpdateLoop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    this.updateInterval = setInterval(() => {
      this.updateBattles();
      this.checkBattleQueue();
    }, this.UPDATE_INTERVAL_MS);

    console.log('Tournament battle service update loop started');
  }

  /**
   * Get active battles
   * @returns Array of active battles
   */
  public getActiveBattles(): any[] {
    const activeBattles = [];

    for (const [id, battle] of this.battles.entries()) {
      if (battle.status === 'in_progress' || battle.status === 'pending') {
        activeBattles.push({
          id,
          tournamentId: battle.tournamentId,
          status: battle.status,
          participants: battle.participants.length,
          spectators: battle.spectators.length,
          startTime: battle.startTime
        });
      }
    }

    console.log(`Found ${activeBattles.length} active battles`);
    return activeBattles;
  }

  /**
   * Get a battle by ID
   * @param battleId The battle ID
   * @returns The battle or undefined if not found
   */
  public getBattle(battleId: string): any {
    const battle = this.battles.get(battleId);
    if (!battle) return undefined;

    return {
      id: battle.id,
      tournamentId: battle.tournamentId,
      status: battle.status,
      startTime: battle.startTime,
      endTime: battle.endTime,
      participants: battle.participants.map(p => ({
        id: p.id,
        userId: p.userId,
        petId: p.petId,
        petName: p.petName,
        petLevel: p.petLevel,
        health: p.health,
        maxHealth: p.maxHealth,
        position: p.position,
        rotation: p.rotation
      })),
      spectators: battle.spectators,
      battleLog: battle.battleLog,
      winnerId: battle.winnerId
    };
  }

  /**
   * Get battle state for sending to clients
   * @param battleId The battle ID
   * @returns The battle state or undefined if not found
   */
  public getBattleState(battleId: string): any {
    const battle = this.battles.get(battleId);
    if (!battle) return undefined;

    return {
      battleId,
      status: battle.status,
      participants: battle.participants.map(p => ({
        id: p.id,
        petName: p.petName,
        petLevel: p.petLevel,
        health: p.health,
        maxHealth: p.maxHealth,
        position: p.position,
        rotation: p.rotation
      })),
      spectators: battle.spectators.length,
      battleLog: battle.battleLog
    };
  }

  /**
   * Create a test battle
   * @param userId The user ID creating the test battle
   * @param existingBattleId Optional existing battle ID to use
   * @returns The battle ID
   */
  public createTestBattle(userId: string, existingBattleId?: string): string {
    // Use provided battle ID or generate a new one
    const battleId = existingBattleId || uuidv4();

    // Check if battle already exists
    if (this.battles.has(battleId)) {
      console.log(`Test battle ${battleId} already exists, adding user ${userId} as spectator`);
      this.addSpectatorToBattle(battleId, userId);

      // Send current battle state to the user
      setTimeout(() => {
        this.sendBattleUpdateToUser(battleId, userId);

        // Get existing participants and send spawn instructions
        const battle = this.battles.get(battleId);
        if (battle && battle.participants.length > 0) {
          const spawnInstructions = battle.participants.map(p => ({
            type: RenderInstructionType.SPAWN_ENTITY,
            data: {
              entityId: p.id,
              entityType: 'pet',
              position: p.position || { x: 0, y: 1, z: 0 },
              rotation: p.rotation || { x: 0, y: 0, z: 0 },
              name: p.petName,
              health: p.health,
              maxHealth: p.maxHealth
            }
          }));

          console.log(`Sending ${spawnInstructions.length} spawn instructions to user ${userId} for existing battle ${battleId}`);
          this.sendRenderInstructionsToUser(battleId, userId, spawnInstructions);
        }
      }, 1000);

      return battleId;
    }

    // Generate a unique tournament ID for testing
    const tournamentId = `test_${Date.now()}`;

    // Create test participants with better positions and rotations
    const participants: TournamentParticipant[] = [
      {
        id: uuidv4(),
        userId: 'test-user-1',
        petId: 'test-pet-1',
        petName: 'Test Pet 1',
        petLevel: 5,
        powerups: [PowerupType.FIRE, PowerupType.SHIELD],
        health: 100,
        maxHealth: 100,
        position: { x: -15, y: 5, z: 0 },
        rotation: { x: 0, y: 0, z: 0 }
      },
      {
        id: uuidv4(),
        userId: 'test-user-2',
        petId: 'test-pet-2',
        petName: 'Test Pet 2',
        petLevel: 5,
        powerups: [PowerupType.ICE, PowerupType.SHIELD],
        health: 100,
        maxHealth: 100,
        position: { x: 15, y: 5, z: 0 },
        rotation: { x: 0, y: Math.PI, z: 0 }
      }
    ];

    // Create battle in pending state first
    const battle: Battle = {
      id: battleId,
      tournamentId,
      participants,
      spectators: [userId],
      status: 'pending', // Start as pending
      battleLog: [],
      nextUpdateTime: Date.now() + this.TEST_BATTLE_INTERVAL_MS // Start in 5 seconds instead of 1 minute
    };

    // Add battle to map
    this.battles.set(battleId, battle);

    console.log(`Created test battle ${battleId} with ${participants.length} participants. Will start in ${this.TEST_BATTLE_INTERVAL_MS/1000} seconds.`);
    console.log(`Added user ${userId} as spectator to battle ${battleId}`);

    // Add to battle queue
    this.battleQueue.push(battleId);

    // Add initial battle log entry
    battle.battleLog.push({
      type: 'system',
      message: 'Battle created. Pets are taking their positions...',
      timestamp: Date.now()
    });

    // Send initial spawn instructions for all participants
    const spawnInstructions = participants.map(p => ({
      type: RenderInstructionType.SPAWN_ENTITY,
      data: {
        entityId: p.id,
        entityType: 'pet',
        position: p.position,
        rotation: p.rotation,
        name: p.petName,
        health: p.health,
        maxHealth: p.maxHealth
      }
    }));

    // Send battle update immediately
    this.sendBattleUpdateToUser(battleId, userId);

    // Delay sending spawn instructions to ensure spectator has joined
    setTimeout(() => {
      console.log(`Sending initial spawn instructions for battle ${battleId}`);
      this.sendRenderInstructions(battleId, spawnInstructions);

      // Also send directly to the creator who is a spectator
      this.sendRenderInstructionsToUser(battleId, userId, spawnInstructions);

      // Send another battle update after spawn instructions
      setTimeout(() => {
        this.sendBattleUpdateToUser(battleId, userId);
      }, 1000);
    }, 1000);

    return battleId;
  }

  /**
   * Add a spectator to a battle
   * @param battleId The battle ID
   * @param userId The user ID to add as spectator
   * @returns Whether the operation was successful
   */
  public addSpectator(battleId: string, userId: string): boolean {
    return this.addSpectatorToBattle(battleId, userId);
  }

  /**
   * Add a spectator to a battle (alias for addSpectator for compatibility)
   * @param battleId The battle ID
   * @param userId The user ID to add as spectator
   * @returns Whether the operation was successful
   */
  public addSpectatorToBattle(battleId: string, userId: string): boolean {
    const battle = this.battles.get(battleId);

    if (!battle) {
      console.error(`Battle ${battleId} not found`);
      return false;
    }

    // CRITICAL FIX: Add spectator if not already in the list
    if (!battle.spectators.includes(userId)) {
      battle.spectators.push(userId);
      console.log(`Added spectator ${userId} to battle ${battleId}. Total spectators: ${battle.spectators.length}`);
      console.log(`Current spectators for battle ${battleId}:`, battle.spectators);

      // Send initial battle state to the new spectator
      setTimeout(() => {
        console.log(`Sending initial battle state to new spectator ${userId}`);
        this.sendBattleUpdateToUser(battleId, userId);

        // Send initial spawn instructions for all participants
        if (battle.participants.length > 0) {
          const spawnInstructions = battle.participants.map(p => ({
            type: RenderInstructionType.SPAWN_ENTITY,
            data: {
              entityId: p.id,
              entityType: 'pet',
              position: p.position || { x: 0, y: 1, z: 0 }, // Provide default position if missing
              rotation: p.rotation || { x: 0, y: 0, z: 0 }, // Provide default rotation if missing
              name: p.petName || `Pet ${p.id}`, // Provide default name if missing
              health: p.health || 100, // Provide default health if missing
              maxHealth: p.maxHealth || 100 // Provide default maxHealth if missing
            }
          }));

          console.log(`Sending ${spawnInstructions.length} spawn instructions directly to spectator ${userId}`);
          this.sendRenderInstructionsToUser(battleId, userId, spawnInstructions);
        }
      }, 1000); // Delay to ensure WebSocket connection is established
    } else {
      console.log(`User ${userId} is already spectating battle ${battleId}. Total spectators: ${battle.spectators.length}`);
      console.log(`Current spectators for battle ${battleId}:`, battle.spectators);

      // Send updated battle state anyway
      this.sendBattleUpdateToUser(battleId, userId);
    }

    return true;
  }

  /**
   * Remove a spectator from a battle
   * @param battleId The battle ID
   * @param userId The user ID to remove as spectator
   * @returns Whether the operation was successful
   */
  public removeSpectator(battleId: string, userId: string): boolean {
    return this.removeSpectatorFromBattle(battleId, userId);
  }

  /**
   * Remove a spectator from a battle (alias for removeSpectator for compatibility)
   * @param battleId The battle ID
   * @param userId The user ID to remove as spectator
   * @returns Whether the operation was successful
   */
  public removeSpectatorFromBattle(battleId: string, userId: string): boolean {
    const battle = this.battles.get(battleId);

    if (!battle) {
      console.error(`Battle ${battleId} not found`);
      return false;
    }

    // Remove spectator if in the list
    const index = battle.spectators.indexOf(userId);
    if (index !== -1) {
      battle.spectators.splice(index, 1);
      console.log(`Removed spectator ${userId} from battle ${battleId}`);
    }

    return true;
  }

  /**
   * Update spectator position in a battle
   * @param battleId The battle ID
   * @param userId The user ID of the spectator
   * @param position The new position
   * @returns Whether the operation was successful
   */
  public updateSpectatorPosition(battleId: string, userId: string, position: { x: number; y: number; z: number }): boolean {
    const battle = this.battles.get(battleId);

    if (!battle) {
      console.error(`Battle ${battleId} not found`);
      return false;
    }

    // Check if user is a spectator
    if (!battle.spectators.includes(userId)) {
      console.error(`User ${userId} is not a spectator of battle ${battleId}`);
      return false;
    }

    // We could store spectator positions if needed for future features
    // For now, just log the update
    console.log(`Updated position for spectator ${userId} in battle ${battleId}: (${position.x}, ${position.y}, ${position.z})`);

    return true;
  }

  /**
   * Update battles
   * This method is called periodically to update all active battles
   */
  private updateBattles(): void {
    const now = Date.now();

    for (const [battleId, battle] of this.battles.entries()) {
      // Skip battles that are not in progress
      if (battle.status !== 'in_progress') {
        continue;
      }

      // Skip battles that don't need an update yet
      if (battle.nextUpdateTime > now) {
        continue;
      }

      // Update battle
      this.updateBattle(battleId, battle);

      // Set next update time
      battle.nextUpdateTime = now + 1000; // Update every second
    }
  }

  /**
   * Update a single battle
   * @param battleId The battle ID
   * @param battle The battle to update
   */
  private updateBattle(battleId: string, battle: Battle): void {
    // Simulate battle turn
    this.simulateBattleTurn(battleId);

    console.log(`Updated battle ${battleId}`);

    // Send battle update to all spectators
    this.broadcastBattleUpdate(battleId);
  }

  /**
   * Send battle update to a specific user
   * @param battleId The battle ID
   * @param userId The user ID to send the update to
   */
  private sendBattleUpdateToUser(battleId: string, userId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) return;

    // Create battle update data
    const updateData = {
      battleId,
      status: battle.status,
      participants: battle.participants.map(p => ({
        id: p.id,
        petName: p.petName,
        petLevel: p.petLevel,
        health: p.health,
        maxHealth: p.maxHealth,
        position: p.position,
        rotation: p.rotation
      })),
      spectators: battle.spectators.length,
      battleLog: battle.battleLog
    };

    // Send to the specific user using the unified WebSocket implementation
    const sent = sendBattleMessageToUser(battleId, userId, MessageType.TournamentBattleUpdate, updateData);

    if (sent) {
      console.log(`Sent battle update to user ${userId} for battle ${battleId}`);
    } else {
      console.log(`No active connection found for user ${userId} to send battle update`);
    }
  }

  /**
   * Check battle queue
   * This method is called periodically to check if any battles in the queue should be started
   */
  private checkBattleQueue(): void {
    if (this.battleQueue.length === 0) {
      return;
    }

    console.log(`Checking battle queue: ${this.battleQueue.length} battles waiting`);

    const now = Date.now();
    const battleIdsToStart: string[] = [];

    // Check each battle in the queue
    for (const battleId of this.battleQueue) {
      const battle = this.battles.get(battleId);

      if (!battle) {
        // Battle not found, remove from queue
        console.warn(`Battle ${battleId} not found in battles map, removing from queue`);
        continue;
      }

      if (battle.status !== 'pending') {
        // Battle already started or completed, remove from queue
        console.warn(`Battle ${battleId} has status ${battle.status}, removing from queue`);
        continue;
      }

      if (battle.nextUpdateTime <= now) {
        // Time to start the battle
        battleIdsToStart.push(battleId);
      }
    }

    // Start battles
    for (const battleId of battleIdsToStart) {
      this.startBattle(battleId);

      // Remove from queue
      const index = this.battleQueue.indexOf(battleId);
      if (index !== -1) {
        this.battleQueue.splice(index, 1);
      }
    }
  }

  /**
   * Start a battle
   * @param battleId The battle ID to start
   */
  private startBattle(battleId: string): void {
    const battle = this.battles.get(battleId);

    if (!battle) {
      console.error(`Battle ${battleId} not found`);
      return;
    }

    if (battle.status !== 'pending') {
      console.warn(`Battle ${battleId} has status ${battle.status}, cannot start`);
      return;
    }

    // Update battle status
    battle.status = 'in_progress';
    battle.startTime = Date.now();
    battle.nextUpdateTime = Date.now() + 1000; // First update in 1 second

    console.log(`Battle ${battleId} started`);

    // Send battle update to all spectators
    for (const userId of battle.spectators) {
      this.sendBattleUpdateToUser(battleId, userId);
    }
  }

  /**
   * Create a tournament
   * @param tournamentData The tournament data
   * @returns The tournament ID
   */
  public createTournament(tournamentData: any): string {
    // Generate a unique ID for the tournament
    const tournamentId = uuidv4();

    // Create the tournament
    const tournament = {
      id: tournamentId,
      name: tournamentData.name,
      creatorId: tournamentData.creatorId,
      maxParticipants: tournamentData.maxParticipants,
      entryFee: tournamentData.entryFee,
      participants: [],
      status: 'registration',
      createdAt: Date.now(),
      battles: []
    };

    // Add to tournaments map
    this.tournaments.set(tournamentId, tournament);

    console.log(`Tournament ${tournamentId} created by ${tournamentData.creatorId}`);

    return tournamentId;
  }

  /**
   * Get all tournaments
   * @returns Array of tournaments
   */
  public getTournaments(): any[] {
    const tournaments = [];

    for (const [id, tournament] of this.tournaments.entries()) {
      tournaments.push({
        id,
        name: tournament.name,
        creatorId: tournament.creatorId,
        maxParticipants: tournament.maxParticipants,
        currentParticipants: tournament.participants.length,
        participants: tournament.participants.length, // Add this for backward compatibility
        entryFee: tournament.entryFee,
        status: tournament.status,
        createdAt: tournament.createdAt
      });
    }

    console.log('Returning tournaments with participant counts:', tournaments.map(t => `${t.name}: ${t.currentParticipants}/${t.maxParticipants}`));
    return tournaments;
  }

  /**
   * Join a tournament
   * @param tournamentId The tournament ID
   * @param participantData The participant data
   * @returns Whether the operation was successful
   */
  public joinTournament(tournamentId: string, participantData: any): boolean {
    const tournament = this.tournaments.get(tournamentId);

    if (!tournament) {
      console.error(`Tournament ${tournamentId} not found`);
      return false;
    }

    if (tournament.status !== 'registration') {
      console.error(`Tournament ${tournamentId} is not in registration phase`);
      return false;
    }

    if (tournament.participants.length >= tournament.maxParticipants) {
      console.error(`Tournament ${tournamentId} is full`);
      return false;
    }

    // Check if user already joined (fix type)
    const existingParticipant = tournament.participants.find((p: any) => p.userId === participantData.userId);
    if (existingParticipant) {
      console.error(`User ${participantData.userId} already joined tournament ${tournamentId}`);
      return false;
    }

    // Add participant
    tournament.participants.push({
      userId: participantData.userId,
      petId: participantData.petId,
      powerups: participantData.powerups,
      joinedAt: Date.now()
    });

    console.log(`User ${participantData.userId} joined tournament ${tournamentId}`);

    // Check if tournament is full and should start
    if (tournament.participants.length >= tournament.maxParticipants) {
      console.log(`Tournament ${tournamentId} is full, starting...`);
      this.startTournament(tournamentId);
    }

    return true;
  }

  /**
   * Start a tournament
   * @param tournamentId The tournament ID
   * @returns Whether the operation was successful
   */
  private startTournament(tournamentId: string): boolean {
    const tournament = this.tournaments.get(tournamentId);

    if (!tournament) {
      console.error(`Tournament ${tournamentId} not found`);
      return false;
    }

    if (tournament.status !== 'registration') {
      console.error(`Tournament ${tournamentId} is not in registration phase`);
      return false;
    }

    // Update tournament status
    tournament.status = 'in_progress';
    tournament.startTime = Date.now();

    // Create initial battles
    this.createTournamentBattles(tournamentId);

    console.log(`Tournament ${tournamentId} started`);

    return true;
  }

  /**
   * Create battles for a tournament
   * @param tournamentId The tournament ID
   */
  private createTournamentBattles(tournamentId: string): void {
    const tournament = this.tournaments.get(tournamentId);

    if (!tournament) {
      console.error(`Tournament ${tournamentId} not found`);
      return;
    }

    // Shuffle participants
    const participants = [...tournament.participants];
    for (let i = participants.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [participants[i], participants[j]] = [participants[j], participants[i]];
    }

    // Create battles (pairs of participants)
    for (let i = 0; i < participants.length; i += 2) {
      // If we have an odd number of participants, the last one gets a bye
      if (i + 1 >= participants.length) {
        console.log(`Participant ${participants[i].userId} gets a bye`);
        continue;
      }

      // Create battle participants
      const battleParticipants: TournamentParticipant[] = [
        {
          id: uuidv4(),
          userId: participants[i].userId,
          petId: participants[i].petId,
          petName: `Pet ${i+1}`,
          petLevel: 5,
          powerups: participants[i].powerups,
          health: 100,
          maxHealth: 100,
          position: { x: -20, y: 5, z: 0 },
          rotation: { x: 0, y: 0, z: 0 }
        },
        {
          id: uuidv4(),
          userId: participants[i+1].userId,
          petId: participants[i+1].petId,
          petName: `Pet ${i+2}`,
          petLevel: 5,
          powerups: participants[i+1].powerups,
          health: 100,
          maxHealth: 100,
          position: { x: 20, y: 5, z: 0 },
          rotation: { x: 0, y: 0, z: 0 }
        }
      ];

      // Create battle
      const battleId = this.createBattle(tournamentId, battleParticipants);

      // Add battle to tournament
      tournament.battles.push(battleId);

      console.log(`Created battle ${battleId} for tournament ${tournamentId} with participants ${participants[i].userId} and ${participants[i+1].userId}`);
    }
  }

  /**
   * Create a battle
   * @param tournamentId The tournament ID
   * @param participants The battle participants
   * @returns The battle ID
   */
  private createBattle(tournamentId: string, participants: TournamentParticipant[]): string {
    // Generate a unique ID for the battle
    const battleId = uuidv4();

    // Create the battle
    const battle: Battle = {
      id: battleId,
      tournamentId,
      participants,
      spectators: [],
      status: 'pending',
      battleLog: [],
      nextUpdateTime: Date.now() + this.BATTLE_INTERVAL_MS // Wait 1 minute before starting
    };

    // Add battle to map
    this.battles.set(battleId, battle);

    // Add to queue
    this.battleQueue.push(battleId);

    console.log(`Created battle ${battleId} for tournament ${tournamentId} with ${participants.length} participants`);

    return battleId;
  }

  /**
   * Get active battles for a tournament
   * @param tournamentId The tournament ID
   * @returns Array of active battles
   */
  public getActiveBattlesForTournament(tournamentId: string): any[] {
    const activeBattles = [];

    for (const [id, battle] of this.battles.entries()) {
      if (battle.tournamentId === tournamentId && (battle.status === 'in_progress' || battle.status === 'pending')) {
        activeBattles.push({
          id,
          tournamentId: battle.tournamentId,
          status: battle.status,
          participants: battle.participants.length,
          spectators: battle.spectators.length,
          startTime: battle.startTime
        });
      }
    }

    console.log(`Found ${activeBattles.length} active battles for tournament ${tournamentId}`);
    return activeBattles;
  }

  /**
   * Simulate a turn in the battle
   * @param battleId The battle ID
   */
  private simulateBattleTurn(battleId: string): void {
    const battle = this.battles.get(battleId);

    if (!battle || battle.status !== 'in_progress') return;

    // Get active participants (those with health > 0)
    const activePets = battle.participants.filter(p => p.health > 0);

    // Check if battle is over (only one or zero active pets)
    if (activePets.length <= 1) {
      this.endBattle(battleId, activePets[0]?.id);
      return;
    }

    // For each active pet, decide on an action
    activePets.forEach(attacker => {
      // Skip if pet has no health
      if (attacker.health <= 0) return;

      // Choose a random target that is not self
      const possibleTargets = activePets.filter(p => p.id !== attacker.id);
      if (possibleTargets.length === 0) return;

      const targetIndex = Math.floor(Math.random() * possibleTargets.length);
      const target = possibleTargets[targetIndex];

      // Decide on action: move, attack, or use powerup
      const actionRoll = Math.random();

      if (actionRoll < 0.4) {
        // Move (40% chance)
        this.movePet(battleId, attacker.id);
      } else if (actionRoll < 0.9) {
        // Attack (50% chance)
        this.attackPet(battleId, attacker.id, target.id);
      } else {
        // Use powerup (10% chance)
        this.usePowerup(battleId, attacker.id);
      }
    });

    // Send updates to all clients
    this.broadcastBattleUpdate(battleId);
  }

  /**
   * Move a pet in the battle
   * @param battleId The battle ID
   * @param petId The pet ID
   */
  private movePet(battleId: string, petId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) return;

    const pet = battle.participants.find(p => p.id === petId);
    if (!pet || !pet.position) return;

    // Calculate a new position
    // Move randomly but stay within arena bounds
    const moveDistance = 2;
    const arenaRadius = 15;

    // Random direction
    const angle = Math.random() * Math.PI * 2;
    const dx = Math.cos(angle) * moveDistance;
    const dz = Math.sin(angle) * moveDistance;

    // Calculate new position
    let newX = pet.position.x + dx;
    let newZ = pet.position.z + dz;

    // Check arena bounds
    const distanceFromCenter = Math.sqrt(newX * newX + newZ * newZ);
    if (distanceFromCenter > arenaRadius) {
      // Scale back to arena boundary
      const scale = arenaRadius / distanceFromCenter;
      newX *= scale;
      newZ *= scale;
    }

    // Update position
    pet.position.x = newX;
    pet.position.z = newZ;

    // Update rotation to face movement direction
    if (dx !== 0 || dz !== 0) {
      pet.rotation = {
        x: 0,
        y: Math.atan2(dz, dx),
        z: 0
      };
    }

    // Add to battle log
    battle.battleLog.push({
      type: 'move',
      message: `${pet.petName} moves to a new position`,
      timestamp: Date.now(),
      petId: pet.id
    });

    // Send render instructions
    this.sendRenderInstructions(battleId, [{
      type: RenderInstructionType.MOVE_ENTITY,
      data: {
        entityId: pet.id,
        position: pet.position,
        rotation: pet.rotation
      }
    }]);
  }

  /**
   * Attack another pet
   * @param battleId The battle ID
   * @param attackerId The attacker pet ID
   * @param targetId The target pet ID
   */
  private attackPet(battleId: string, attackerId: string, targetId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) return;

    const attacker = battle.participants.find(p => p.id === attackerId);
    const target = battle.participants.find(p => p.id === targetId);

    if (!attacker || !target) return;

    // Calculate damage
    const baseDamage = 5 + Math.floor(Math.random() * 10); // 5-14 damage
    const levelBonus = Math.floor(attacker.petLevel / 2);
    let damage = baseDamage + levelBonus;

    // Check for powerups
    const attackType = attacker.powerups.includes(PowerupType.FIRE) ? 'fire' :
                       attacker.powerups.includes(PowerupType.ICE) ? 'ice' : 'normal';

    // Apply damage modifiers based on powerups
    if (attackType === 'fire') {
      damage = Math.floor(damage * 1.5); // Fire does 50% more damage
    } else if (attackType === 'ice') {
      damage = Math.floor(damage * 0.8); // Ice does 20% less damage but slows
    }

    // Check if target has shield
    if (target.powerups.includes(PowerupType.SHIELD)) {
      damage = Math.floor(damage * 0.5); // Shield reduces damage by 50%

      // Add to battle log
      battle.battleLog.push({
        type: 'shield',
        message: `${target.petName}'s shield absorbs some of the damage`,
        timestamp: Date.now(),
        petId: target.id
      });
    }

    // Apply damage
    target.health = Math.max(0, target.health - damage);

    // Add to battle log
    battle.battleLog.push({
      type: 'attack',
      message: `${attacker.petName} attacks ${target.petName} for ${damage} damage with ${attackType} attack`,
      timestamp: Date.now(),
      attackerId: attacker.id,
      targetId: target.id,
      damage,
      attackType
    });

    // Send render instructions
    this.sendRenderInstructions(battleId, [
      {
        type: RenderInstructionType.ATTACK,
        data: {
          attackerId: attacker.id,
          targetId: target.id,
          attackType
        }
      },
      {
        type: RenderInstructionType.DAMAGE,
        data: {
          entityId: target.id,
          damage,
          currentHealth: target.health,
          maxHealth: target.maxHealth
        }
      }
    ]);

    // Check if target is defeated
    if (target.health <= 0) {
      // Add to battle log
      battle.battleLog.push({
        type: 'defeat',
        message: `${target.petName} has been defeated`,
        timestamp: Date.now(),
        petId: target.id
      });
    }
  }

  /**
   * Use a powerup
   * @param battleId The battle ID
   * @param petId The pet ID
   */
  private usePowerup(battleId: string, petId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) return;

    const pet = battle.participants.find(p => p.id === petId);
    if (!pet || pet.powerups.length === 0) return;

    // Choose a random powerup from the pet's available powerups
    const powerupIndex = Math.floor(Math.random() * pet.powerups.length);
    const powerupType = pet.powerups[powerupIndex];

    // Add to battle log
    battle.battleLog.push({
      type: 'powerup',
      message: `${pet.petName} activates ${PowerupType[powerupType]} powerup`,
      timestamp: Date.now(),
      petId: pet.id,
      powerupType
    });

    // Send render instructions
    this.sendRenderInstructions(battleId, [{
      type: RenderInstructionType.USE_POWERUP,
      data: {
        entityId: pet.id,
        powerupType
      }
    }]);
  }

  /**
   * End a battle
   * @param battleId The battle ID
   * @param winnerId The winner pet ID
   */
  private endBattle(battleId: string, winnerId?: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) return;

    // Update battle status
    battle.status = 'completed';
    battle.endTime = Date.now();
    battle.winnerId = winnerId;

    // Find winner details
    const winner = battle.participants.find(p => p.id === winnerId);

    // Add to battle log
    if (winner) {
      battle.battleLog.push({
        type: 'victory',
        message: `${winner.petName} wins the battle!`,
        timestamp: Date.now(),
        petId: winner.id
      });

      // Send render instructions
      this.sendRenderInstructions(battleId, [{
        type: RenderInstructionType.VICTORY,
        data: {
          winnerId: winner.id,
          rewards: 'Tournament points and experience'
        }
      }]);

      // Check if this is the final battle of a tournament
      this.checkTournamentCompletion(battle.tournamentId, winner);
    } else {
      battle.battleLog.push({
        type: 'draw',
        message: 'The battle ends in a draw',
        timestamp: Date.now()
      });
    }

    // Broadcast final update
    this.broadcastBattleUpdate(battleId);

    console.log(`Battle ${battleId} ended. Winner: ${winner?.petName || 'None (draw)'}`);
  }

  /**
   * Check if a tournament is complete and distribute prizes if needed
   * @param tournamentId The tournament ID
   * @param finalBattleWinner The winner of the final battle
   */
  private async checkTournamentCompletion(tournamentId: string, finalBattleWinner: TournamentParticipant): Promise<void> {
    const tournament = this.tournaments.get(tournamentId);
    if (!tournament) return;

    // Skip for test tournaments
    if (tournamentId.startsWith('test_')) return;

    // Check if all battles are completed (fix type)
    const allBattles = tournament.battles || [];
    const ongoingBattles = allBattles.filter((battleId: string) => {
      const battle = this.battles.get(battleId);
      return battle && battle.status !== 'completed';
    });

    // If there are still ongoing battles, tournament is not yet complete
    if (ongoingBattles.length > 0) return;

    console.log(`Tournament ${tournamentId} completed. Distributing prizes...`);

    // Find the tournament winner (highest ranked) - in this case, the final battle winner
    const winnerPetId = finalBattleWinner.petId;
    
    // In a real implementation, you'd have a more complex logic to determine winners
    // For now, we'll use winner-takes-all (70% to the winner)
    
    try {
      if (!contractAddress || !privateKey) {
        console.error('Cannot distribute prizes: contract address or private key not set');
        return;
      }

      // Convert from string tournamentId to number if needed (contract expects uint256)
      const contractTournamentId = parseInt(tournamentId.replace('tournament_', ''), 10);
      
      // For winner-takes-all, we have a single winner getting 70%
      const winners = [parseInt(winnerPetId, 10)]; // Convert to number as contract expects uint256
      const percentages = [70]; // Winner gets 70% (required by the contract)
            
      console.log(`Calling contract endTournament with tournamentId: ${contractTournamentId}, winners: [${winners}], percentages: [${percentages}]`);
      
      // Call the contract to end the tournament and distribute prizes
      const tx = await this.contract.endTournament(
        contractTournamentId,
        winners,
        percentages
      );
      
      console.log(`Transaction sent: ${tx.hash}`);
      
      // Wait for transaction confirmation
      const receipt = await tx.wait();
      console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
      
      // Update tournament status
      tournament.status = 'completed';
      tournament.endTime = Date.now();
      tournament.winner = winnerPetId;
      
      console.log(`Tournament ${tournamentId} prizes distributed. Winner: ${winnerPetId}`);
    } catch (error) {
      console.error(`Error distributing prizes for tournament ${tournamentId}:`, error);
    }
  }

  /**
   * Send battle update to a specific client
   * @param battleId The battle ID
   * @param userId The user ID to send to
   */
  private sendBattleUpdate(battleId: string, userId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) {
      console.error(`Cannot send battle update: Battle ${battleId} not found`);
      return;
    }

    // Find the client connection
    let sent = false;
    this.wss.clients.forEach(client => {
      if (client.readyState === 1 && (client as any).userId === userId) {
        try {
          // Create a message with all the necessary battle data
          const message = {
            type: MessageType.TournamentBattleUpdate,
            data: {
              battle: {
                id: battle.id,
                tournamentId: battle.tournamentId,
                participants: battle.participants.map(p => ({
                  id: p.id,
                  userId: p.userId,
                  petId: p.petId,
                  petName: p.petName,
                  petLevel: p.petLevel,
                  powerups: p.powerups,
                  health: p.health,
                  maxHealth: p.maxHealth,
                  position: p.position,
                  rotation: p.rotation
                })),
                status: battle.status,
                startTime: battle.startTime,
                endTime: battle.endTime,
                winnerId: battle.winnerId,
                battleLog: battle.battleLog
              }
            },
            timestamp: Date.now(),
            sender: 'server'
          };

          client.send(JSON.stringify(message));
          sent = true;
          console.log(`Sent battle update to user ${userId} for battle ${battleId}`);

          // Send initial spawn instructions for all participants
          if (battle.participants.length > 0) {
            const spawnInstructions = battle.participants.map(p => ({
              type: RenderInstructionType.SPAWN_ENTITY,
              data: {
                entityId: p.id,
                entityType: 'pet',
                position: p.position,
                rotation: p.rotation,
                name: p.petName,
                health: p.health,
                maxHealth: p.maxHealth
              }
            }));

            // Send spawn instructions immediately
            this.sendRenderInstructionsToUser(battleId, userId, spawnInstructions);
            console.log(`Sent initial spawn instructions to user ${userId} for battle ${battleId}`);
          }
        } catch (error) {
          console.error(`Error sending battle update to user ${userId}:`, error);
        }
      }
    });

    if (!sent) {
      console.warn(`No active connection found for user ${userId} to send battle update`);
    }
  }

  /**
   * Broadcast battle update to all participants and spectators
   * @param battleId The battle ID
   */
  private broadcastBattleUpdate(battleId: string): void {
    const battle = this.battles.get(battleId);
    if (!battle) {
      console.error(`Cannot broadcast battle update: Battle ${battleId} not found`);
      return;
    }

    // Get all user IDs to send to
    const userIds = [
      ...battle.participants.map(p => p.userId),
      ...battle.spectators
    ];

    console.log(`Broadcasting battle update for battle ${battleId} to ${userIds.length} users`);
    console.log(`Battle status: ${battle.status}, Participants: ${battle.participants.length}, Spectators: ${battle.spectators.length}`);

    // Create battle update data
    const updateData = {
      battleId,
      status: battle.status,
      participants: battle.participants.map(p => ({
        id: p.id,
        petName: p.petName,
        petLevel: p.petLevel,
        health: p.health,
        maxHealth: p.maxHealth,
        position: p.position,
        rotation: p.rotation
      })),
      spectators: battle.spectators.length,
      battleLog: battle.battleLog
    };

    // Send to all clients in the battle using the unified WebSocket implementation
    sendBattleMessage(battleId, MessageType.TournamentBattleUpdate, updateData);
    console.log(`Sent battle update to all users in battle ${battleId}`);

    // For each user, also try to send directly as a fallback
    let sentCount = 0;
    for (const userId of userIds) {
      try {
        // Send directly to each user as a fallback
        const sent = sendBattleMessageToUser(battleId, userId, MessageType.TournamentBattleUpdate, updateData);
        if (sent) {
          sentCount++;
        }
      } catch (error) {
        console.error(`Error sending battle update to user ${userId}:`, error);
      }
    }

    console.log(`Directly sent battle update to ${sentCount}/${userIds.length} users for battle ${battleId}`);
  }

  /**
   * Send render instructions to all clients in a battle
   * @param battleId The battle ID
   * @param instructions The render instructions
   */
  private sendRenderInstructions(battleId: string, instructions: any[]): void {
    console.log(`Tournament Battle: Attempting to send render instructions for battle ${battleId}`);

    const battle = this.battles.get(battleId);
    if (!battle) {
      console.error(`Tournament Battle: Cannot send render instructions: Battle ${battleId} not found`);
      return;
    }

    console.log(`Tournament Battle: Found battle ${battleId} with ${battle.participants.length} participants and ${battle.spectators.length} spectators`);

    // Get all user IDs to send to
    const userIds = [
      ...battle.participants.map(p => p.userId),
      ...battle.spectators
    ];

    // Log the render instructions
    console.log(`Sending render instructions for battle ${battleId} to ${userIds.length} users:`,
                JSON.stringify(instructions).substring(0, 100) + '...');
    console.log(`Spectators: ${battle.spectators.join(', ')}`);

    // Create message data
    const messageData = {
      battleId: battle.id,
      instructions
    };

    // Send to all clients in the battle using the unified WebSocket implementation
    sendBattleMessage(battleId, MessageType.TournamentBattleRenderInstructions, messageData);
    console.log(`Sent render instructions to all users in battle ${battleId}`);

    // For each user, also try to send directly as a fallback
    let sentCount = 0;
    for (const userId of userIds) {
      try {
        // Send directly to each user as a fallback
        const sent = sendBattleMessageToUser(battleId, userId, MessageType.TournamentBattleRenderInstructions, messageData);
        if (sent) {
          sentCount++;
        }
      } catch (error) {
        console.error(`Error sending render instructions to user ${userId}:`, error);
      }
    }

    console.log(`Directly sent render instructions to ${sentCount}/${userIds.length} users for battle ${battleId}`);
  }

  /**
   * Send render instructions to a specific user
   * @param battleId The battle ID
   * @param userId The user ID to send to
   * @param instructions The render instructions
   */
  private sendRenderInstructionsToUser(battleId: string, userId: string, instructions: any[]): void {
    const battle = this.battles.get(battleId);
    if (!battle) {
      console.error(`Cannot send render instructions: Battle ${battleId} not found`);
      return;
    }

    // Log the render instructions with more details
    console.log(`Sending ${instructions.length} render instructions for battle ${battleId} to user ${userId}`);
    console.log(`First instruction type: ${instructions[0]?.type}, entityId: ${instructions[0]?.data?.entityId}`);

    // Create message data
    const messageData = {
      battleId: battle.id,
      instructions
    };

    // Send to the specific user using the unified WebSocket implementation
    const sent = sendBattleMessageToUser(battleId, userId, MessageType.TournamentBattleRenderInstructions, messageData);

    if (sent) {
      console.log(`Sent render instructions to user ${userId} for battle ${battleId}`);
    } else {
      console.warn(`No active connection found for user ${userId} to send render instructions`);
      console.warn(`Battle ${battleId} has ${battle.spectators.length} spectators: ${battle.spectators.join(', ')}`);
    }
  }
}
