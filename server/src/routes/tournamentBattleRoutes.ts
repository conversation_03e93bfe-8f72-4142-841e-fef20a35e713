import express from 'express';
import { TournamentBattleService } from '../services/TournamentBattleService';
import { getTournamentBattleService } from '../../unifiedWebSocket';

// Reference to the tournament battle service
let tournamentBattleService: TournamentBattleService;

const router = express.Router();

/**
 * Get the tournament battle service
 * This is a helper function to get the tournament battle service from the unified WebSocket implementation
 */
const getService = (): TournamentBattleService | null => {
  if (!tournamentBattleService) {
    // Initialize reference to the tournament battle service (only once)
    tournamentBattleService = getTournamentBattleService();
  }
  return tournamentBattleService;
};

/**
 * Create a test tournament battle
 * POST /api/tournament-battles/test
 */
router.post('/test', async (req, res) => {
  try {
    // Get user ID from request (in a real app, this would come from authentication)
    const userId = req.body.userId || 'anonymous';

    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Create a test battle
    const battleId = service.createTestBattle(userId);

    console.log(`Created test battle ${battleId} for user ${userId}. Battle will start in 60 seconds.`);

    // Return the battle ID
    return res.status(200).json({ battleId });
  } catch (error) {
    console.error('Error creating test tournament battle:', error);
    return res.status(500).json({ error: 'Failed to create test tournament battle' });
  }
});

/**
 * Get active tournament battles
 * GET /api/tournament-battles/active
 */
router.get('/active', async (req, res) => {
  try {
    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Get active battles
    const activeBattles = service.getActiveBattles();

    console.log(`Returning ${activeBattles.length} active battles`);

    // Return the active battles
    return res.status(200).json(activeBattles);
  } catch (error) {
    console.error('Error getting active tournament battles:', error);
    return res.status(500).json({ error: 'Failed to get active tournament battles' });
  }
});

/**
 * Create a tournament
 * POST /api/tournament-battles/tournaments
 */
router.post('/tournaments', async (req, res) => {
  try {
    // Get user ID from request (in a real app, this would come from authentication)
    const userId = req.body.userId || 'anonymous';
    const name = req.body.name || 'Tournament';
    const maxParticipants = req.body.maxParticipants || 4;
    const entryFee = req.body.entryFee || '2';
    const petId = req.body.petId;
    const powerups = req.body.powerups;

    // Validate required fields
    if (!petId) {
      return res.status(400).json({ error: 'Pet ID is required' });
    }

    if (!powerups || !Array.isArray(powerups) || powerups.length !== 2) {
      return res.status(400).json({ error: 'Exactly 2 powerups are required' });
    }

    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Create a tournament
    const tournamentId = service.createTournament({
      name,
      creatorId: userId,
      maxParticipants,
      entryFee
    });

    console.log(`Created tournament ${tournamentId} by user ${userId}`);

    // Automatically register the creator's pet in the tournament
    const success = service.joinTournament(tournamentId, {
      userId,
      petId,
      powerups
    });

    if (!success) {
      console.error(`Failed to auto-register creator's pet in tournament ${tournamentId}`);
      // Continue anyway, as the tournament was created successfully
    } else {
      console.log(`Creator's pet ${petId} automatically registered in tournament ${tournamentId}`);
    }

    // Return the tournament ID
    return res.status(200).json({ tournamentId });
  } catch (error) {
    console.error('Error creating tournament:', error);
    return res.status(500).json({ error: 'Failed to create tournament' });
  }
});

/**
 * Get active tournaments
 * GET /api/tournament-battles/tournaments
 */
router.get('/tournaments', async (req, res) => {
  try {
    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Get active tournaments
    const tournaments = service.getTournaments();

    console.log(`Returning ${tournaments.length} tournaments`);

    // Return the tournaments
    return res.status(200).json(tournaments);
  } catch (error) {
    console.error('Error getting tournaments:', error);
    return res.status(500).json({ error: 'Failed to get tournaments' });
  }
});

/**
 * Join a tournament
 * POST /api/tournament-battles/tournaments/:id/join
 */
router.post('/tournaments/:id/join', async (req, res) => {
  try {
    const tournamentId = req.params.id;
    const userId = req.body.userId || 'anonymous';
    const petId = req.body.petId;
    const powerups = req.body.powerups;

    if (!petId) {
      return res.status(400).json({ error: 'Pet ID is required' });
    }

    if (!powerups || !Array.isArray(powerups) || powerups.length !== 2) {
      return res.status(400).json({ error: 'Exactly 2 powerups are required' });
    }

    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Join the tournament
    const success = service.joinTournament(tournamentId, {
      userId,
      petId,
      powerups
    });

    if (!success) {
      return res.status(400).json({ error: 'Failed to join tournament' });
    }

    console.log(`User ${userId} joined tournament ${tournamentId} with pet ${petId}`);

    // Return success
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error joining tournament:', error);
    return res.status(500).json({ error: 'Failed to join tournament' });
  }
});

/**
 * Get active battles for a tournament
 * GET /api/tournament-battles/tournaments/:id/battles
 */
router.get('/tournaments/:id/battles', async (req, res) => {
  try {
    const tournamentId = req.params.id;

    // Get the tournament battle service
    const service = getService();
    if (!service) {
      return res.status(500).json({ error: 'Tournament battle service not initialized' });
    }

    // Get active battles for the tournament
    const battles = service.getActiveBattlesForTournament(tournamentId);

    console.log(`Returning ${battles.length} active battles for tournament ${tournamentId}`);

    // Return the battles
    return res.status(200).json(battles);
  } catch (error) {
    console.error('Error getting active battles for tournament:', error);
    return res.status(500).json({ error: 'Failed to get active battles for tournament' });
  }
});

export default router;
