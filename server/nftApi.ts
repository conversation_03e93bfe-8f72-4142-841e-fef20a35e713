import express from 'express';
import { PetStorage } from './petStorage';
import path from 'path';
import fs from 'fs/promises';
import { db } from './db';
import { petSpecters } from '../shared/petSchema';
import { eq } from 'drizzle-orm';
import axios from 'axios'; // Add axios for HTTP requests
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Create router
const router = express.Router();
const petStorage = new PetStorage();

// Use ES Module compatible path resolution
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// NFT metadata directory
const NFT_METADATA_DIR = path.join(rootDir, 'uploads', 'nft-metadata');
const NFT_IMAGES_DIR = path.join(rootDir, 'uploads', 'nft-images');

// Ensure directories exist
async function ensureDirectoriesExist() {
  try {
    await fs.mkdir(NFT_METADATA_DIR, { recursive: true });
    await fs.mkdir(NFT_IMAGES_DIR, { recursive: true });
    console.log(`NFT directories created at: ${NFT_METADATA_DIR} and ${NFT_IMAGES_DIR}`);
  } catch (err) {
    console.error('Error creating NFT directories:', err);
  }
}

// Call this on server startup
ensureDirectoriesExist();

// Get metadata for a specific token
router.get('/metadata/:tokenId.json', async (req, res) => {
  try {
    const tokenId = parseInt(req.params.tokenId);
    if (isNaN(tokenId)) {
      return res.status(400).json({ error: 'Invalid token ID' });
    }

    // Try to load from cached file first
    const metadataPath = path.join(NFT_METADATA_DIR, `${tokenId}.json`);
    try {
      const metadataFile = await fs.readFile(metadataPath, 'utf-8');
      const metadata = JSON.parse(metadataFile);
      return res.json(metadata);
    } catch (err) {
      // File doesn't exist, continue to database lookup
    }

    // Look up pet specter in database
    const petSpecter = await petStorage.getPetSpecterByNftId(tokenId);
    
    if (!petSpecter) {
      return res.status(404).json({ error: 'Pet specter not found' });
    }

    // Generate image URL - this will be relative to the domain
    const imageFilename = `${tokenId}.png`;
    const imageUrl = `${req.protocol}://${req.get('host')}/nft/images/${imageFilename}`;

    // Create metadata
    const metadata = {
      name: petSpecter.name || `Pet Specter #${tokenId}`,
      description: `A ${(petSpecter.metadata as any)?.tier ? `${(petSpecter.metadata as any)?.tier} Tier ` : ''} ${(petSpecter.metadata as any)?.type || 'Unknown'} Pet Specter from Specter Shift game`,
      image: imageUrl,
      external_url: `${req.protocol}://${req.get('host')}/game?petId=${petSpecter.id}`,
      attributes: [
        {
          trait_type: 'Type',
          value: (petSpecter.metadata as any)?.type || 'Unknown'
        },
        {
          trait_type: 'Tier',
          value: (petSpecter.metadata as any)?.tier || 'COMMON'
        },
        {
          trait_type: 'Color',
          value: (petSpecter.metadata as any)?.color || 'Unknown'
        },
        {
          trait_type: 'Level',
          value: petSpecter.level?.toString() || '1'
        },
        {
          trait_type: 'XP',
          value: petSpecter.xp?.toString() || '0'
        }
      ]
    };

    // Cache the metadata
    try {
      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (cacheErr) {
      console.error(`Error caching metadata for token ${tokenId}:`, cacheErr);
      // Continue anyway
    }

    res.json(metadata);
  } catch (error) {
    console.error('Error serving NFT metadata:', error);
    res.status(500).json({ error: 'Failed to retrieve NFT metadata' });
  }
});

// Serve NFT images
router.get('/images/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const tokenId = parseInt(filename.split('.')[0]);
    
    if (isNaN(tokenId)) {
      return res.status(400).send('Invalid token ID');
    }

    const imagePath = path.join(NFT_IMAGES_DIR, filename);
    
    // Check if image file exists
    try {
      await fs.access(imagePath);
      // File exists, serve it
      return res.sendFile(imagePath);
    } catch (err) {
      // File doesn't exist, try to generate it
    }

    // Get pet specter data
    const petSpecter = await petStorage.getPetSpecterByNftId(tokenId);
    
    if (!petSpecter) {
      return res.status(404).send('Pet specter not found');
    }

    // If we have a texture URL, fetch and save it
    if ((petSpecter.metadata as any)?.texture) {
      try {
        // If texture is a data URL, save it directly
        if ((petSpecter.metadata as any)?.texture.startsWith('data:image')) {
          // Extract base64 data
          const base64Data = (petSpecter.metadata as any)?.texture.split(',')[1];
          if (base64Data) {
            const imageBuffer = Buffer.from(base64Data, 'base64');
            await fs.writeFile(imagePath, imageBuffer);
            console.log(`Saved data URL image for token ${tokenId}`);
            return res.sendFile(imagePath);
          }
        } else {
          // Texture is a URL, fetch it and save locally
          try {
            console.log(`Fetching image from URL for token ${tokenId}: ${(petSpecter.metadata as any)?.texture}`);
            const response = await axios.get((petSpecter.metadata as any)?.texture, { responseType: 'arraybuffer' });
            const imageBuffer = Buffer.from(response.data);
            await fs.writeFile(imagePath, imageBuffer);
            console.log(`Saved remote image for token ${tokenId}`);
            return res.sendFile(imagePath);
          } catch (fetchErr) {
            console.error(`Error fetching image from URL for token ${tokenId}:`, fetchErr);
            // Fall through to default image handling
          }
        }
      } catch (saveErr) {
        console.error(`Error saving image for token ${tokenId}:`, saveErr);
      }
    }

    // If we reach here, we couldn't serve a proper image
    // Try to serve a default image if available
    const defaultImagePath = path.join(rootDir, 'client', 'public', 'assets', 'sprites', 'default-pet.png');
    try {
      await fs.access(defaultImagePath);
      // Default image exists, use it instead
      const defaultImageBuffer = await fs.readFile(defaultImagePath);
      await fs.writeFile(imagePath, defaultImageBuffer);
      console.log(`Using default image for token ${tokenId}`);
      return res.sendFile(imagePath);
    } catch (defaultErr) {
      // No default image either
      console.error(`No default image available for token ${tokenId}`);
      res.status(404).send('Image not found');
    }
  } catch (error) {
    console.error('Error serving NFT image:', error);
    res.status(500).send('Internal server error');
  }
});

// Endpoint to update an NFT's metadata (for admins/owners)
router.post('/metadata/:tokenId', async (req, res) => {
  try {
    // This should have authentication/authorization
    // For simplicity, we're not implementing it here
    
    const tokenId = parseInt(req.params.tokenId);
    if (isNaN(tokenId)) {
      return res.status(400).json({ error: 'Invalid token ID' });
    }

    const { name, type, tier, color, level, xp } = req.body;
    
    // Update pet specter in database
    const petSpecter = await petStorage.getPetSpecterByNftId(tokenId);
    
    if (!petSpecter) {
      return res.status(404).json({ error: 'Pet specter not found' });
    }

    // Update fields
    const updates: any = {};
    if (name) updates.name = name;
    if (level !== undefined) updates.level = level;
    if (xp !== undefined) updates.xp = xp;

    // Update metadata in the database if tier, type, or color are provided in the request body
    if (tier || type || color) {
      // Ensure metadata is an object before trying to spread it
      const currentMetadata = typeof petSpecter.metadata === 'object' && petSpecter.metadata !== null ? petSpecter.metadata : {};
      updates.metadata = {
        ...currentMetadata as object, // Spread existing metadata
        ...(tier && { tier }), // Add tier if provided
        ...(type && { type }), // Add type if provided
        ...(color && { color }), // Add color if provided
      };
    }

    // Update in database
    await db.update(petSpecters)
      .set(updates)
      .where(eq(petSpecters.tokenId, tokenId.toString()));

    // Update metadata file
    const metadataPath = path.join(NFT_METADATA_DIR, `${tokenId}.json`);
    try {
      let metadata;
      try {
        const metadataFile = await fs.readFile(metadataPath, 'utf-8');
        metadata = JSON.parse(metadataFile);
      } catch (err) {
        // File doesn't exist, create new metadata
        metadata = {
          name: name || `Pet Specter #${tokenId}`,
          description: `A ${tier ? `${tier} Tier ` : ''} ${type || 'Unknown'} Pet Specter from Specter Shift game`,
          image: `${req.protocol}://${req.get('host')}/nft/images/${tokenId}.png`,
          external_url: `${req.protocol}://${req.get('host')}/game?petId=${petSpecter.id}`,
          attributes: []
        };
      }

      // Update attributes
      metadata.name = name || metadata.name;
      metadata.description = `A ${tier ? `${tier} Tier ` : ''} ${type || 'Unknown'} Pet Specter from Specter Shift game`;
      
      // Update or add attributes
      const attributeMap: Record<string, any> = {};
      metadata.attributes.forEach((attr: any) => {
        attributeMap[attr.trait_type] = attr;
      });

      if (type) attributeMap['Type'] = { trait_type: 'Type', value: type };
      if (tier) attributeMap['Tier'] = { trait_type: 'Tier', value: tier };
      if (color) attributeMap['Color'] = { trait_type: 'Color', value: color };
      if (level !== undefined) attributeMap['Level'] = { trait_type: 'Level', value: level.toString() };
      if (xp !== undefined) attributeMap['XP'] = { trait_type: 'XP', value: xp.toString() };

      // Convert back to array
      metadata.attributes = Object.values(attributeMap);

      // Save updated metadata
      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));

      res.json({ success: true, metadata });
    } catch (err) {
      console.error(`Error updating metadata for token ${tokenId}:`, err);
      res.status(500).json({ error: 'Failed to update metadata' });
    }
  } catch (error) {
    console.error('Error updating NFT metadata:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router; 