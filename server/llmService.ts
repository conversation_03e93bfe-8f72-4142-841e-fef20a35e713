import Groq from 'groq-sdk';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Check for API key
if (!process.env.GROQ_API_KEY) {
  console.error('GROQ_API_KEY environment variable is missing. Please check your .env file.');
  throw new Error('GROQ_API_KEY environment variable is missing');
}

// Constants
const CACHE_DIR = process.env.LLM_CACHE_DIR || './cache/llm';
const MAX_CACHE_AGE_DAYS = parseInt(process.env.MAX_CACHE_AGE_DAYS || '30', 10);
const MAX_REQUESTS_PER_HOUR = 30; // Rate limiting - 30 requests per minute as specified

// Create cache directory if it doesn't exist
if (!fs.existsSync(CACHE_DIR)) {
  fs.mkdirSync(CACHE_DIR, { recursive: true });
}

// Rate limiting
const requestCounts: Map<string, { count: number, timestamp: number }> = new Map();

// Global request queue for sequential processing
interface QueuedRequest {
  id: string;
  userId: string;
  timestamp: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  type: 'prompt' | 'analysis';
}

class RequestQueue {
  private queue: QueuedRequest[] = [];
  private processing: boolean = false;

  // Add a request to the queue
  public addRequest(userId: string, type: 'prompt' | 'analysis'): string {
    const requestId = crypto.randomUUID();

    this.queue.push({
      id: requestId,
      userId,
      timestamp: Date.now(),
      status: 'pending',
      type
    });

    console.log(`Added ${type} request ${requestId} to queue. Queue length: ${this.queue.length}`);

    // Start processing if not already processing
    if (!this.processing) {
      this.processNextRequest();
    }

    return requestId;
  }

  // Get position in queue for a request
  public getPositionInQueue(requestId: string): number {
    const index = this.queue.findIndex(req => req.id === requestId);
    return index === -1 ? -1 : index;
  }

  // Get status of a request
  public getRequestStatus(requestId: string): QueuedRequest | null {
    return this.queue.find(req => req.id === requestId) || null;
  }

  // Mark a request as processing
  public markAsProcessing(requestId: string): void {
    const request = this.queue.find(req => req.id === requestId);
    if (request) {
      request.status = 'processing';
    }
  }

  // Mark a request as completed and remove from queue
  public markAsCompleted(requestId: string): void {
    const index = this.queue.findIndex(req => req.id === requestId);
    if (index !== -1) {
      this.queue[index].status = 'completed';
      // Remove completed request after a short delay to allow status checks
      setTimeout(() => {
        this.queue = this.queue.filter(req => req.id !== requestId);
      }, 5000);
    }
  }

  // Mark a request as failed
  public markAsFailed(requestId: string): void {
    const request = this.queue.find(req => req.id === requestId);
    if (request) {
      request.status = 'failed';
      // Remove failed request after a short delay
      setTimeout(() => {
        this.queue = this.queue.filter(req => req.id !== requestId);
      }, 5000);
    }
  }

  // Process the next request in the queue
  private async processNextRequest(): Promise<void> {
    if (this.queue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;

    // Find the next pending request
    const nextRequest = this.queue.find(req => req.status === 'pending');

    if (!nextRequest) {
      // No pending requests, check again after a short delay
      setTimeout(() => this.processNextRequest(), 1000);
      return;
    }

    // Mark as processing
    this.markAsProcessing(nextRequest.id);

    // Wait for rate limiting (ensure we don't exceed 30 requests per minute)
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay between requests

    // Signal that this request can now proceed (the actual processing happens in the LLM methods)
    console.log(`Request ${nextRequest.id} is now allowed to proceed`);

    // Move to the next request after a delay
    // The actual request will mark itself as completed when done
    setTimeout(() => this.processNextRequest(), 2000);
  }

  // Get all requests in the queue
  public getAllRequests(): QueuedRequest[] {
    return [...this.queue];
  }
}

// Create a global request queue
const requestQueue = new RequestQueue();

/**
 * LLM Service for prompt enhancement and image analysis
 */
export class LLMService {
  /**
   * Enhance a user's pet description with LLM to create an optimal prompt for image generation
   */
  static async enhancePromptForPetGeneration(
    preferences: {
      specterType: string;
      description: string;
      color?: string;
      style?: string;
    },
    userId: string
  ): Promise<{ enhancedPrompt: string, queuePosition: number, requestId: string }> {
    // Add to request queue
    const requestId = requestQueue.addRequest(userId, 'prompt');

    // Get position in queue
    const queuePosition = requestQueue.getPositionInQueue(requestId);

    // If not at the front of the queue, return position info
    if (queuePosition > 0) {
      return {
        enhancedPrompt: '',
        queuePosition,
        requestId
      };
    }

    try {
      // Check rate limiting
      this.checkRateLimit(userId);

      // Create a cache key based on preferences
      const cacheKey = this.createCacheKey(preferences);
      const cachePath = path.join(CACHE_DIR, `${cacheKey}.json`);

      // Check if response is already in cache
      if (fs.existsSync(cachePath)) {
        const cachedData = JSON.parse(fs.readFileSync(cachePath, 'utf8'));
        if (Date.now() - cachedData.timestamp < MAX_CACHE_AGE_DAYS * 24 * 60 * 60 * 1000) {
          console.log(`Using cached LLM response for ${cacheKey}`);
          // Mark request as completed
          requestQueue.markAsCompleted(requestId);
          return {
            enhancedPrompt: cachedData.response,
            queuePosition: 0,
            requestId
          };
        }
      }

      // Construct the prompt for the LLM
      const prompt = this.constructPromptEnhancementPrompt(preferences);

      // Create a new Groq client for this request to ensure fresh context
      const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

      // Call Groq API with a fresh client instance
      const chatCompletion = await groq.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "You are an expert at creating prompts for AI image generation. Your task is to enhance user descriptions into detailed, effective prompts that will generate high-quality game characters in the specified art style. The character should be a full body portrait, floating in front of a gray background."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        model: "llama-3.1-8b-instant",
      });

      // Extract the enhanced prompt from the response
      const enhancedPrompt = chatCompletion.choices[0]?.message?.content || '';

      if (!enhancedPrompt) {
        throw new Error('Failed to generate prompt with LLM');
      }

      // Cache the response
      const cacheData = {
        response: enhancedPrompt,
        timestamp: Date.now()
      };
      fs.writeFileSync(cachePath, JSON.stringify(cacheData));

      // Increment request count for rate limiting
      this.incrementRequestCount(userId);

      // Mark request as completed
      requestQueue.markAsCompleted(requestId);

      return {
        enhancedPrompt,
        queuePosition: 0,
        requestId
      };
    } catch (error) {
      // Mark request as failed
      requestQueue.markAsFailed(requestId);
      throw error;
    }
  }

  /**
   * Analyze an NFT image to extract features for pet generation
   */
  static async analyzeNFTImage(
    imageUrl: string,
    userId: string
  ): Promise<{
    analysis?: {
      recommendedType: string;
      colorPalette: string[];
      description: string;
      style: string;
    };
    queuePosition: number;
    requestId: string;
  }> {
    // Add to request queue
    const requestId = requestQueue.addRequest(userId, 'analysis');

    // Get position in queue
    const queuePosition = requestQueue.getPositionInQueue(requestId);

    // If not at the front of the queue, return position info
    if (queuePosition > 0) {
      return {
        queuePosition,
        requestId
      };
    }

    try {
      // Check rate limiting
      this.checkRateLimit(userId);

      // Create a cache key based on the image URL
      const cacheKey = `nft-${crypto.createHash('md5').update(imageUrl).digest('hex')}`;
      const cachePath = path.join(CACHE_DIR, `${cacheKey}.json`);

      // Check if response is already in cache
      if (fs.existsSync(cachePath)) {
        const cachedData = JSON.parse(fs.readFileSync(cachePath, 'utf8'));
        if (Date.now() - cachedData.timestamp < MAX_CACHE_AGE_DAYS * 24 * 60 * 60 * 1000) {
          console.log(`Using cached NFT analysis for ${cacheKey}`);
          // Mark request as completed
          requestQueue.markAsCompleted(requestId);
          return {
            analysis: cachedData.response,
            queuePosition: 0,
            requestId
          };
        }
      }

      // Construct the prompt for the LLM
      const prompt = `
        Analyze this NFT image at ${imageUrl} and create a unique ghost-like pet specter with traits and abilities
        that reflect the visual elements, colors, and theme of the original NFT. The pet should maintain the essence
        of the original while adapting to the spectral theme of our game.

        Please provide the following information in JSON format:
        1. recommendedType: A number from 1 to 5 representing the class (e.g., "Class 1", "Class 2", etc.)
        2. colorPalette: An array of hex color codes extracted from the image (limit to 3 colors)
        3. description: A brief description of the pet specter (50 words or less)
        4. style: A single word describing the art style (e.g., pixel, cartoon, realistic, abstract)

        Format your response as valid JSON only, with no additional text.
      `;

      console.log('Analyzing NFT image with prompt:', prompt);

      // Create a new Groq client for this request to ensure fresh context
      const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

      // Call Groq API with a fresh client instance
      const chatCompletion = await groq.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "You are an expert at analyzing images and extracting features for game character generation. Your responses should be in valid JSON format only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        model: "llama-3.1-8b-instant",
      });

      // Extract the JSON response
      const jsonResponse = chatCompletion.choices[0]?.message?.content || '';
      if (!jsonResponse) {
        throw new Error('LLM returned empty response');
      }

      console.log('LLM response for NFT analysis:', jsonResponse);

      // Extract JSON from the response (in case the LLM added extra text)
      const jsonMatch = jsonResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in LLM response');
      }

      // Parse the JSON response
      const analysis = JSON.parse(jsonMatch[0]);

      // Validate the analysis has all required fields
      if (!analysis.recommendedType || !analysis.colorPalette || !analysis.description || !analysis.style) {
        throw new Error('LLM response missing required fields');
      }

      // Cache the response
      const cacheData = {
        response: analysis,
        timestamp: Date.now()
      };
      fs.writeFileSync(cachePath, JSON.stringify(cacheData));

      // Increment request count for rate limiting
      this.incrementRequestCount(userId);

      // Mark request as completed
      requestQueue.markAsCompleted(requestId);

      return {
        analysis,
        queuePosition: 0,
        requestId
      };
    } catch (error) {
      // Mark request as failed
      requestQueue.markAsFailed(requestId);
      throw error;
    }
  }

  /**
   * Construct a prompt for enhancing user descriptions
   */
  private static constructPromptEnhancementPrompt(preferences: {
    specterType: string;
    description: string;
    color?: string;
    style?: string;
  }): string {
    const { description, style } = preferences;
    const artStyle = style || 'pixel'; // Default to pixel art if no style is provided

    return `
      Describe this ${artStyle} character's appearance, floating above a gray background. Not a side-facing character. Not a request to create an image. Not a resolution. A ${artStyle} STYLE character. Not "against a neutral background". THE CHARACTER IS FLOATING IN FRONT OF A GRAY BACKGROUND. SAY IT. VERBATIM. FLOATING IN FRONT OF A GRAY BACKGROUND. Describe the character.

      Here are the details:
      - Description: ${description}
      ${style ? `- Style: ${style}` : ''}

      Create a detailed description of a ${artStyle} version of the character floating in front of a gray background.
      The prompt should include only the description of what the character visually looks like without any backgrounds etc. besides a gray background.
      Return ONLY the enhanced prompt, with no additional explanation or commentary. Do not use color codes or anything except a visual description.

      Example: User: Napoleon as a Super Saiyan.
      Correct example: ${artStyle} art of Napoleon as a Super Saiyan floating in front of a gray background, localized lightning, glowing eyes.

      Incorrect example (DO NOT DO THIS):Generate a 2D ${artStyle} art image depicting a Class 1 character inspired by Napoleon as a Super Saiyan, floating against a uniform gray background (RGB: 128, 128, 128). The character should be a vibrant blue colour (#3399ff) and be created in a retro, chiptune-inspired style. The image should feature a ${artStyle} art pixelation effect and be free from background noise or aliasing. The character's pose and facial expression should evoke the confident and powerful personality of a Super Saiyan, while maintaining a whimsical and mystical feel fitting for a Class 1 character.
    `;
  }

  /**
   * Create a cache key based on input data
   */
  private static createCacheKey(data: any): string {
    const dataString = JSON.stringify(data);
    return crypto.createHash('md5').update(dataString).digest('hex');
  }

  /**
   * Create a cache key for a request ID
   * This is a public method used by the API to retrieve cached results
   */
  static createCacheKeyForRequestId(requestId: string): string {
    // For now, just return the request ID as the cache key
    // In a real implementation, you would look up the actual cache key associated with this request
    return requestId;
  }

  /**
   * Check rate limit for a user
   */
  private static checkRateLimit(userId: string): void {
    const now = Date.now();
    const userRequests = requestCounts.get(userId);

    if (userRequests) {
      // Check if the timestamp is within the current minute (changed from hour to minute)
      const isWithinMinute = now - userRequests.timestamp < 60 * 1000;

      if (isWithinMinute && userRequests.count >= MAX_REQUESTS_PER_HOUR) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      // Reset count if outside the minute
      if (!isWithinMinute) {
        requestCounts.set(userId, { count: 0, timestamp: now });
      }
    } else {
      // First request for this user
      requestCounts.set(userId, { count: 0, timestamp: now });
    }
  }

  /**
   * Increment request count for a user
   */
  private static incrementRequestCount(userId: string): void {
    const userRequests = requestCounts.get(userId);
    if (userRequests) {
      userRequests.count += 1;
    }
  }

  /**
   * Clean up old cached responses
   */
  static cleanupCache(): void {
    const now = Date.now();
    const maxAge = MAX_CACHE_AGE_DAYS * 24 * 60 * 60 * 1000; // Convert days to milliseconds

    // Get all files in the cache directory
    const files = fs.readdirSync(CACHE_DIR);

    // Check each cached file
    for (const file of files) {
      const filePath = path.join(CACHE_DIR, file);

      try {
        const stats = fs.statSync(filePath);
        if (now - stats.mtimeMs > maxAge) {
          // Delete old file
          fs.unlinkSync(filePath);
        }
      } catch (error) {
        console.error(`Error checking cache file ${file}:`, error);
      }
    }
  }
}

// Set up periodic cache cleanup
setInterval(() => {
  LLMService.cleanupCache();
}, 24 * 60 * 60 * 1000); // Run once a day

// Export the request queue for API access
export { requestQueue };
