import express, { type Express } from "express";
import fs from "fs";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import { createServer as createViteServer, createLogger } from "vite";
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
import { type Server } from "http";
import viteConfig from "../vite.config";
import { nanoid } from "nanoid";

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: {
      server,
      clientPort: 5001, // Use the same port as the server
      path: '/__vite_hmr',
      // Use HTTP polling instead of WebSocket
      protocol: 'http'
    },
    allowedHosts: true as const
  };

  const vite = await createViteServer({
    ...viteConfig,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        console.error("[vite] Error:", msg);
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  // Use Vite's middlewares for static assets and HMR
  app.use(vite.middlewares);

  // Special handling for API routes to avoid serving the SPA
  app.use(/^\/(?!api|ws|uploads).*/, async (req, res, next) => {
    const url = req.originalUrl;
    console.log(`Serving client app for URL: ${url}`);

    try {
      const clientTemplate = path.resolve(
        __dirname,
        "..",
        "client",
        "index.html",
      );

      // Always reload the index.html file from disk in case it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");

      // Add a cache-busting parameter to the main script
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`,
      );

      // Transform the HTML with Vite
      const page = await vite.transformIndexHtml(url, template);

      // Send the transformed HTML
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });
}

export function serveStatic(app: Express) {
  // Use fileURLToPath and dirname to get the correct path in ES modules
  const currentDir = dirname(fileURLToPath(import.meta.url));
  const distPath = path.resolve(currentDir, "..", "dist/public");

  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`,
    );
  }

  app.use(express.static(distPath));

  // fall through to index.html if the file doesn't exist
  app.use("*", (_req, res) => {
    res.sendFile(path.resolve(distPath, "index.html"));
  });
}
