import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Pool } from 'pg';
import * as dotenv from 'dotenv';
import { exec } from 'child_process';
import { promisify } from 'util';

// Load environment variables
dotenv.config();

const execPromise = promisify(exec);

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Create a Drizzle instance
const db = drizzle(pool);

async function generateMigrations() {
  console.log('Generating database migrations...');
  
  try {
    // Generate migrations using drizzle-kit
    const { stdout, stderr } = await execPromise('npx drizzle-kit generate:pg --schema=./shared/petSchema.ts,./shared/schema.ts --out=./migrations');
    
    console.log('Migration generation output:');
    console.log(stdout);
    
    if (stderr) {
      console.error('Migration generation errors:');
      console.error(stderr);
    }
    
    console.log('✅ Migration generation completed');
    
    // Close the pool
    await pool.end();
    
    return true;
  } catch (error) {
    console.error('❌ Migration generation failed:', error);
    
    // Close the pool
    await pool.end();
    
    return false;
  }
}

// Run migration generation if this file is executed directly
if (process.argv[1] === import.meta.url) {
  generateMigrations().then(success => {
    process.exit(success ? 0 : 1);
  });
}

export { generateMigrations };
