import {
  type PetSpecter,
  type InsertPetSpecter,
  type NftTransaction,
  type InsertNftTransaction,
  type WalletBalance,
  type InsertWalletBalance
} from "@shared/petSchema";

export class MemPetStorage {
  private petSpecters: Map<number, PetSpecter>;
  private nftTransactions: Map<number, NftTransaction>;
  private walletBalances: Map<string, WalletBalance>;

  private petSpecterCurrentId: number;
  private nftTransactionCurrentId: number;

  constructor() {
    this.petSpecters = new Map();
    this.nftTransactions = new Map();
    this.walletBalances = new Map();

    this.petSpecterCurrentId = 1;
    this.nftTransactionCurrentId = 1;
  }

  // Pet Specter methods
  async getPetSpecterById(id: number): Promise<PetSpecter | undefined> {
    return this.petSpecters.get(id);
  }

  async getPetSpecterByGameId(gameId: string): Promise<PetSpecter | undefined> {
    return Array.from(this.petSpecters.values()).find(
      (specter) => specter.gameId === gameId
    );
  }

  async getPetSpecterByTokenId(tokenId: string): Promise<PetSpecter | undefined> {
    return Array.from(this.petSpecters.values()).find(
      (specter) => specter.tokenId === tokenId
    );
  }

  async getPetSpectersByWalletAddress(walletAddress: string): Promise<PetSpecter[]> {
    return Array.from(this.petSpecters.values())
      .filter(specter => specter.walletAddress === walletAddress)
      .sort((a, b) => {
        if (!a.lastActive || !b.lastActive) return 0;
        return b.lastActive.getTime() - a.lastActive.getTime();
      });
  }

  async getPetSpectersByOwnerId(ownerId: number): Promise<PetSpecter[]> {
    return Array.from(this.petSpecters.values())
      .filter(specter => specter.ownerId === ownerId)
      .sort((a, b) => {
        if (!a.lastActive || !b.lastActive) return 0;
        return b.lastActive.getTime() - a.lastActive.getTime();
      });
  }

  async createPetSpecter(petSpecter: InsertPetSpecter): Promise<PetSpecter> {
    const id = this.petSpecterCurrentId++;
    const now = new Date();

    const newPetSpecter = {
      ...petSpecter,
      id,
      createdAt: now,
      lastActive: now
    } as PetSpecter;

    this.petSpecters.set(id, newPetSpecter);
    console.log(`Created pet specter with ID=${id}, name=${petSpecter.name}, type=${petSpecter.specterType}`);
    return newPetSpecter;
  }

  async updatePetSpecter(id: number, updates: Partial<PetSpecter>): Promise<PetSpecter | undefined> {
    const specter = this.petSpecters.get(id);
    if (!specter) return undefined;

    // Update lastActive timestamp
    updates.lastActive = new Date();

    const updatedSpecter = { ...specter, ...updates };
    this.petSpecters.set(id, updatedSpecter);
    return updatedSpecter;
  }

  async linkPetSpecterToNFT(gameId: string, tokenId: string, walletAddress: string): Promise<PetSpecter | undefined> {
    const specter = Array.from(this.petSpecters.values()).find(
      (specter) => specter.gameId === gameId
    );

    if (!specter) return undefined;

    const updatedSpecter = {
      ...specter,
      tokenId,
      walletAddress,
      lastActive: new Date()
    };

    this.petSpecters.set(specter.id, updatedSpecter);
    return updatedSpecter;
  }

  async deletePetSpecter(id: number): Promise<boolean> {
    return this.petSpecters.delete(id);
  }

  // NFT Transaction methods
  async getNftTransactionByHash(txHash: string): Promise<NftTransaction | undefined> {
    return Array.from(this.nftTransactions.values()).find(
      (tx) => tx.txHash === txHash
    );
  }

  async getNftTransactionsByTokenId(tokenId: string): Promise<NftTransaction[]> {
    return Array.from(this.nftTransactions.values())
      .filter(tx => tx.tokenId === tokenId)
      .sort((a, b) => {
        if (!a.timestamp || !b.timestamp) return 0;
        return b.timestamp.getTime() - a.timestamp.getTime();
      });
  }

  async getNftTransactionsByWalletAddress(walletAddress: string): Promise<NftTransaction[]> {
    return Array.from(this.nftTransactions.values())
      .filter(tx => tx.walletAddress === walletAddress)
      .sort((a, b) => {
        if (!a.timestamp || !b.timestamp) return 0;
        return b.timestamp.getTime() - a.timestamp.getTime();
      });
  }

  async createNftTransaction(transaction: InsertNftTransaction): Promise<NftTransaction> {
    const id = this.nftTransactionCurrentId++;

    const newTransaction = {
      ...transaction,
      id,
      timestamp: new Date()
    } as NftTransaction;

    this.nftTransactions.set(id, newTransaction);
    console.log(`Created NFT transaction with hash=${transaction.txHash}, tokenId=${transaction.tokenId}`);
    return newTransaction;
  }

  async updateNftTransactionStatus(txHash: string, status: string): Promise<NftTransaction | undefined> {
    const transaction = Array.from(this.nftTransactions.values()).find(
      (tx) => tx.txHash === txHash
    );

    if (!transaction) return undefined;

    const updatedTransaction = { ...transaction, status };
    this.nftTransactions.set(transaction.id, updatedTransaction);
    return updatedTransaction;
  }

  // Wallet Balance methods
  async getWalletBalance(walletAddress: string): Promise<WalletBalance | undefined> {
    return this.walletBalances.get(walletAddress);
  }

  async createOrUpdateWalletBalance(balance: InsertWalletBalance): Promise<WalletBalance> {
    // Check if wallet exists
    const existing = this.walletBalances.get(balance.walletAddress);

    if (existing) {
      // Update existing wallet
      const updatedBalance = {
        ...existing,
        ...balance,
        lastUpdated: new Date()
      };

      this.walletBalances.set(balance.walletAddress, updatedBalance);
      return updatedBalance;
    } else {
      // Create new wallet balance
      const newBalance = {
        ...balance,
        lastUpdated: new Date()
      } as WalletBalance;

      this.walletBalances.set(balance.walletAddress, newBalance);
      return newBalance;
    }
  }

  // Methods for finding pet specters by metadata
  async getPetSpectersByMetadataOrangeID(orangeID: string): Promise<PetSpecter[]> {
    return Array.from(this.petSpecters.values())
      .filter(specter => {
        if (!specter.metadata || typeof specter.metadata !== 'object') return false;

        // Check if orangeID is in metadata
        try {
          const metadata = specter.metadata as any; // Cast to any for easier access with checks
          
          // Check for orangeIDUser.id (with checks)
          if (metadata.orangeIDUser && typeof metadata.orangeIDUser === 'object' && metadata.orangeIDUser.id === orangeID) {
            return true;
          }

          // Check for direct orangeID property
          if (metadata.orangeID === orangeID) {
            return true;
          }

          return false;
        } catch (e) {
          console.error('Error checking metadata for orangeID:', e);
          return false;
        }
      })
      .sort((a, b) => {
        if (!a.lastActive || !b.lastActive) return 0;
        return b.lastActive.getTime() - a.lastActive.getTime();
      });
  }

  async getPetSpectersByMetadataWalletAddress(walletAddress: string): Promise<PetSpecter[]> {
    return Array.from(this.petSpecters.values())
      .filter(specter => {
        if (!specter.metadata || typeof specter.metadata !== 'object') return false;

        // Check if walletAddress is in metadata
        try {
          const metadata = specter.metadata as any; // Cast to any for easier access with checks
          
          // Check for direct ethAddress property
          if (metadata.ethAddress === walletAddress) {
            return true;
          }

          // Check for orangeIDUser.ethAddress (with checks)
          if (metadata.orangeIDUser && typeof metadata.orangeIDUser === 'object' && metadata.orangeIDUser.ethAddress === walletAddress) {
            return true;
          }

          return false;
        } catch (e) {
          console.error('Error checking metadata for walletAddress:', e);
          return false;
        }
      })
      .sort((a, b) => {
        if (!a.lastActive || !b.lastActive) return 0;
        return b.lastActive.getTime() - a.lastActive.getTime();
      });
  }
}
