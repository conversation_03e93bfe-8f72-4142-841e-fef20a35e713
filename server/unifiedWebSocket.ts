/**
 * Unified WebSocket server for all WebSocket connections
 * This handles both game WebSockets and tournament battle WebSockets
 */

import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import { v4 as uuidv4 } from 'uuid';
import { MessageType } from '@shared/schema';
import { TournamentBattleService } from './src/services/TournamentBattleService';

// Connection tracking maps
const connections = new Map<WebSocket, string>(); // WebSocket -> userId
const connectionMetadata = new Map<WebSocket, { userId: string; battleId?: string }>(); // WebSocket -> metadata
const battleConnections = new Map<string, Map<string, WebSocket>>(); // battleId -> (userId -> WebSocket)
const connectionLastActivity = new Map<WebSocket, number>(); // WebSocket -> timestamp

// Global tournament battle service instance
let tournamentBattleService: TournamentBattleService;

/**
 * Set up the WebSocket server
 * @param server HTTP server to attach the WebSocket server to
 * @returns WebSocket server instance
 */
export function setupUnifiedWebSocketServer(server: Server): WebSocketServer {
  console.log('Initializing unified WebSocket server...');

  // Create a single WebSocket server for all connections
  const wss = new WebSocketServer({
    server,
    // Disable per-message deflate to avoid compression issues
    perMessageDeflate: false,
    // Allow connections from any origin
    verifyClient: (info: any) => {
      console.log(`WebSocket: Verifying client connection from ${info.origin || 'unknown origin'}`);
      return true;
    }
  });

  // Initialize tournament battle service
  tournamentBattleService = new TournamentBattleService(wss);

  // Set up WebSocket server event handlers
  wss.on('connection', (ws: WebSocket, req: any) => {
    const connectionIp = req.socket.remoteAddress;
    console.log(`WebSocket: Incoming connection request from IP: ${connectionIp}`);
    try {
      // Parse URL parameters
      const url = new URL(req.url || '/', `http://${req.headers.host}`);
      const path = url.pathname;
      const token = url.searchParams.get('token');
      const battleId = url.searchParams.get('battleId') ?? undefined;
      const connectionId = uuidv4();

      console.log(`WebSocket: New connection accepted. Path: ${path}, Token: ${token || 'none'}, BattleID: ${battleId || 'none'}, Assigned ConnectionID: ${connectionId}`);

      // Store the token as userId for future reference
      if (token) {
        connections.set(ws, token);
        (ws as any).userId = token;
        console.log(`WebSocket: Set userId ${token} on connection ${connectionId}`);
      } else {
        // Use connectionId as userId if no token provided
        connections.set(ws, connectionId);
        (ws as any).userId = connectionId;
        console.log(`WebSocket: Using connectionId ${connectionId} as userId`);
      }

      // Store connection metadata
      const userId = token || connectionId;
      connectionMetadata.set(ws, { userId, battleId });
      (ws as any).connectionId = connectionId;

      // If this is a tournament battle connection
      if (path === '/tournament') {
        console.log(`WebSocket: Tournament battle connection detected for user ${userId}`);

        // Store battleId on the connection
        if (battleId) {
          (ws as any).battleId = battleId;
          console.log(`WebSocket: Set battleId ${battleId} on connection ${connectionId}`);

          // Create battle connection map if it doesn't exist
          if (!battleConnections.has(battleId)) {
            console.log(`WebSocket: Creating new battle connection map for battle ${battleId}`);
            battleConnections.set(battleId, new Map());
          }

          // Add this connection to the battle connections map
          const battleConnectionMap = battleConnections.get(battleId);
          if (battleConnectionMap) {
            battleConnectionMap.set(userId, ws);
            console.log(`WebSocket: Added connection ${connectionId} to battle ${battleId} for user ${userId}`);
            console.log(`WebSocket: Battle ${battleId} now has ${battleConnectionMap.size} connections`);
          }

          // Add spectator to battle if tournament battle service is available
          if (tournamentBattleService) {
            tournamentBattleService.addSpectatorToBattle(battleId, userId);
            console.log(`WebSocket: Added user ${userId} as spectator to battle ${battleId}`);
          }
        }

        // Send welcome message for tournament connections
        sendMessage(ws, MessageType.TournamentBattleJoin, {
          id: connectionId,
          message: battleId
            ? `Connected to tournament battle server for battle ${battleId}`
            : 'Connected to tournament battle server',
          isTournamentConnection: true,
          battleId,
          success: true
        });
      } else {
        // Send welcome message for game connections
        sendMessage(ws, MessageType.PlayerJoin, {
          id: connectionId,
          message: 'Connected to game server',
          isGameConnection: true
        });
      }

      // Update last activity time
      connectionLastActivity.set(ws, Date.now());

      // Handle messages
      ws.on('message', (data: WebSocket.Data) => {
        try {
          // Always update last activity time on any message received
          connectionLastActivity.set(ws, Date.now());

          // Parse message
          const message = JSON.parse(data.toString());
          const userId = (ws as any).userId || 'unknown'; // Get userId for logging

          // Handle Ping messages immediately for keep-alive
          if (message.type === MessageType.Ping) {
            console.log(`WebSocket: Received Ping from ${userId}. Sending Pong.`);
            sendMessage(ws, MessageType.Pong, {
              serverTime: Date.now(),
              clientTime: message.data?.clientTime // Echo back client time if provided
            });
            // Ping handled, no further processing needed for this message
            return; 
          }

          // Log other message types
          console.log(`WebSocket: Received message of type ${message.type} from ${userId}`);

          // Handle tournament battle messages
          if (path === '/tournament' && tournamentBattleService) {
            handleTournamentBattleMessage(ws, message);
          } else {
            // Handle game messages
            handleGameMessage(ws, message);
          }
        } catch (error) {
          console.error('WebSocket: Error parsing message:', error);
          console.error('WebSocket: Raw message data:', data.toString().substring(0, 200));
        }
      });

      // Handle connection close
      ws.on('close', (code: number, reason: string) => {
        console.log(`WebSocket: Connection ${connectionId} closed with code ${code}, reason: ${reason || 'No reason provided'}`);

        // Clean up connection tracking
        const metadata = connectionMetadata.get(ws);
        if (metadata && metadata.battleId && battleConnections.has(metadata.battleId)) {
          const battleConnectionMap = battleConnections.get(metadata.battleId);
          if (battleConnectionMap && battleConnectionMap.has(metadata.userId)) {
            battleConnectionMap.delete(metadata.userId);
            console.log(`WebSocket: Removed user ${metadata.userId} from battle ${metadata.battleId} connections`);
          }

          // Remove spectator from tournament battle
          if (tournamentBattleService) {
            tournamentBattleService.removeSpectatorFromBattle(metadata.battleId, metadata.userId);
            console.log(`WebSocket: Removed user ${metadata.userId} as spectator from battle ${metadata.battleId}`);
          }
        }

        connections.delete(ws);
        connectionMetadata.delete(ws);
        connectionLastActivity.delete(ws);
        console.log(`WebSocket: Cleaned up connection tracking for ${connectionId}`);
      });

      // Handle errors
      ws.on('error', (error: Error) => {
        console.error(`WebSocket: Error on connection ${connectionId}:`, error);
      });

      // Send an immediate ping to verify the connection is working
      if (ws.readyState === WebSocket.OPEN) {
        console.log(`WebSocket: Sending immediate ping to verify connection ${connectionId}`);
        ws.ping();
      }

      // Start ping-pong to keep connection alive
      const pingInterval = setInterval(() => {
        try {
          if (ws.readyState === WebSocket.OPEN) {
            ws.ping();
          } else {
            clearInterval(pingInterval);
          }
        } catch (error) {
          console.warn(`WebSocket: Failed to ping client ${connectionId}:`, error);
          clearInterval(pingInterval);
        }
      }, 15000); // ping every 15 seconds
    } catch (error) {
      console.error(`WebSocket: Error processing new connection from ${connectionIp}:`, error);
      // Attempt to close the connection gracefully if possible
      try {
        ws.close(1011, 'Server error processing connection');
      } catch (closeError) {
        console.error('WebSocket: Error attempting to close errored connection:', closeError);
      }
    }
  });

  // Start a cleanup interval to remove inactive connections
  setInterval(() => {
    const now = Date.now();
    const timeout = 5 * 60 * 1000; // 5 minutes

    connections.forEach((userId, ws) => {
      const lastActivity = connectionLastActivity.get(ws) || 0;
      if (now - lastActivity > timeout) {
        console.log(`WebSocket: Closing inactive connection for user ${userId}`);
        ws.close(1000, 'Connection timeout due to inactivity');
      }
    });
  }, 60000); // Check every minute

  console.log('WebSocket: Server initialized and ready for connections');
  return wss;
}

/**
 * Handle tournament battle WebSocket messages
 * @param ws WebSocket connection
 * @param message The message to handle
 */
function handleTournamentBattleMessage(ws: WebSocket, message: any): void {
  try {
    // Check if the WebSocket is still open
    if (ws.readyState !== WebSocket.OPEN) {
      console.warn(`WebSocket: Cannot process tournament message - WebSocket not open (readyState: ${ws.readyState})`);
      return;
    }

    // Get user ID and battle ID
    const userId = (ws as any).userId;
    const battleId = (ws as any).battleId || (message.data && message.data.battleId);

    if (!userId) {
      console.error('WebSocket: No user ID found for tournament battle message');
      sendError(ws, 'No user ID provided');
      return;
    }

    // Handle message based on type
    switch (message.type) {
      case MessageType.TournamentBattleJoin:
        if (battleId) {
          console.log(`WebSocket: User ${userId} joining tournament battle ${battleId}`);

          // Check if this is a test battle (UUID format)
          const isTestBattle = battleId.length >= 36;

          // Add spectator to battle
          tournamentBattleService.addSpectatorToBattle(battleId, userId);

          // Ensure connection is in the battle connections map
          if (!battleConnections.has(battleId)) {
            console.log(`WebSocket: Creating new battle connection map for battle ${battleId}`);
            battleConnections.set(battleId, new Map());
          }

          const battleConnectionMap = battleConnections.get(battleId);
          if (battleConnectionMap) {
            battleConnectionMap.set(userId, ws);
            console.log(`WebSocket: Added user ${userId} to battle ${battleId} connections (total: ${battleConnectionMap.size})`);
          }

          // Store battleId on the connection if not already set
          if (!(ws as any).battleId) {
            (ws as any).battleId = battleId;
            console.log(`WebSocket: Set battleId ${battleId} on connection for user ${userId}`);

            // Update connection metadata
            const metadata = connectionMetadata.get(ws);
            if (metadata) {
              metadata.battleId = battleId;
              connectionMetadata.set(ws, metadata);
            }
          }

          // Send current battle state
          const battleState = tournamentBattleService.getBattleState(battleId);
          if (battleState) {
            console.log(`WebSocket: Sending current battle state for battle ${battleId} to user ${userId}`);
            sendMessage(ws, MessageType.TournamentBattleUpdate, battleState);
          } else if (isTestBattle) {
            // For test battles, we might need to create the battle if it doesn't exist
            console.log(`WebSocket: Test battle ${battleId} not found, creating it for user ${userId}`);
            tournamentBattleService.createTestBattle(userId, battleId);

            // Send a confirmation message
            sendMessage(ws, MessageType.TournamentBattleJoin, {
              battleId,
              success: true,
              message: `Joined test battle ${battleId} as spectator`
            });

            // Send battle update after a short delay
            setTimeout(() => {
              const battleState = tournamentBattleService.getBattleState(battleId);
              if (battleState) {
                console.log(`WebSocket: Sending battle state for newly created test battle ${battleId} to user ${userId}`);
                sendMessage(ws, MessageType.TournamentBattleUpdate, battleState);
              }
            }, 500);
          }
        } else {
          console.error(`WebSocket: Cannot join tournament battle - no battle ID provided`);
          sendError(ws, 'No battle ID provided');
        }
        break;

      case MessageType.TournamentBattleLeave:
        if (battleId) {
          console.log(`WebSocket: User ${userId} leaving tournament battle ${battleId}`);
          tournamentBattleService.removeSpectatorFromBattle(battleId, userId);

          // Remove connection from battle connections map
          if (battleConnections.has(battleId)) {
            const battleConnectionMap = battleConnections.get(battleId);
            if (battleConnectionMap) {
              battleConnectionMap.delete(userId);
            }
          }
        }
        break;

      // case MessageType.TournamentBattleAction: // Comment out as type doesn't exist
      //   if (battleId && message.data && message.data.action) {
      //     console.log(`WebSocket: User ${userId} performing action ${message.data.action} in battle ${battleId}`);
      //     // tournamentBattleService.handlePlayerAction(battleId, userId, message.data.action, message.data.targetId);
      //     console.log("TournamentBattleService.handlePlayerAction is commented out");
      //   } else {
      //     console.error(`WebSocket: Invalid tournament battle action`);
      //     sendError(ws, 'Invalid tournament battle action');
      //   }
      //   break;

      case 'spectator_position_update':
        if (battleId && message.data && message.data.position) {
          tournamentBattleService.updateSpectatorPosition(battleId, userId, message.data.position);
        }
        break;

      case MessageType.Ping:
        // This is now handled globally before routing to this function
        console.log(`WebSocket: Received Ping in handleTournamentBattleMessage (should have been handled globally) from ${userId}.`);
        break;

      default:
        console.log(`WebSocket: Unhandled tournament battle message type: ${message.type}`);
    }
  } catch (error) {
    console.error('WebSocket: Error handling tournament battle message:', error);
  }
}

/**
 * Handle game WebSocket messages
 * @param ws WebSocket connection
 * @param message The message to handle
 */
function handleGameMessage(ws: WebSocket, message: any): void {
  try {
    // Check if the WebSocket is still open
    if (ws.readyState !== WebSocket.OPEN) {
      console.warn(`WebSocket: Cannot process game message - WebSocket not open (readyState: ${ws.readyState})`);
      return;
    }

    // Get user ID
    const userId = (ws as any).userId;

    if (!userId) {
      console.error('WebSocket: No user ID found for game message');
      sendError(ws, 'No user ID provided');
      return;
    }

    // Handle message based on type
    switch (message.type) {
      // case MessageType.PlayerMove: // Comment out as type doesn't exist
      //   console.log(`WebSocket: Player ${userId} moved`);
      //   // Handle player move
      //   break;

      // case MessageType.PlayerAction: // Comment out as type doesn't exist
      //   console.log(`WebSocket: Player ${userId} performed action`);
      //   // Handle player action
      //   break;

      case MessageType.Ping:
        // This is now handled globally before routing to this function
        console.log(`WebSocket: Received Ping in handleGameMessage (should have been handled globally) from ${userId}.`);
        break;

      default:
        console.log(`WebSocket: Unhandled game message type: ${message.type}`);
    }
  } catch (error) {
    console.error('WebSocket: Error handling game message:', error);
  }
}

/**
 * Send a message to a WebSocket client
 * @param ws WebSocket connection
 * @param type Message type
 * @param data Message data
 */
export function sendMessage(ws: WebSocket, type: string, data: any): void {
  try {
    if (ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: Date.now(),
        sender: 'server'
      };

      ws.send(JSON.stringify(message));
    }
  } catch (error) {
    console.error('WebSocket: Error sending message:', error);
  }
}

/**
 * Send an error message to a WebSocket client
 * @param ws WebSocket connection
 * @param errorMessage Error message
 */
function sendError(ws: WebSocket, errorMessage: string): void {
  sendMessage(ws, MessageType.Error, {
    message: errorMessage,
    timestamp: Date.now()
  });
}

/**
 * Send a message to all clients in a battle
 * @param battleId Battle ID
 * @param type Message type
 * @param data Message data
 */
export function sendBattleMessage(battleId: string, type: string, data: any): void {
  try {
    const battleConnectionMap = battleConnections.get(battleId);
    if (!battleConnectionMap) {
      console.warn(`WebSocket: No connections found for battle ${battleId}`);
      return;
    }

    let sentCount = 0;
    battleConnectionMap.forEach((ws, userId) => {
      if (ws.readyState === WebSocket.OPEN) {
        sendMessage(ws, type, data);
        sentCount++;
      }
    });

    console.log(`WebSocket: Sent ${type} message to ${sentCount}/${battleConnectionMap.size} clients in battle ${battleId}`);
  } catch (error) {
    console.error(`WebSocket: Error sending battle message:`, error);
  }
}

/**
 * Send a message to a specific user in a battle
 * @param battleId Battle ID
 * @param userId User ID
 * @param type Message type
 * @param data Message data
 */
export function sendBattleMessageToUser(battleId: string, userId: string, type: string, data: any): boolean {
  try {
    const battleConnectionMap = battleConnections.get(battleId);
    if (!battleConnectionMap) {
      console.warn(`WebSocket: No connections found for battle ${battleId}`);
      return false;
    }

    const ws = battleConnectionMap.get(userId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      sendMessage(ws, type, data);
      return true;
    }

    console.warn(`WebSocket: No active connection found for user ${userId} in battle ${battleId}`);
    return false;
  } catch (error) {
    console.error(`WebSocket: Error sending battle message to user:`, error);
    return false;
  }
}

/**
 * Get the tournament battle service
 * @returns Tournament battle service
 */
export function getTournamentBattleService(): TournamentBattleService {
  return tournamentBattleService;
}

/**
 * Get the number of active connections
 * @returns Number of active connections
 */
export function getActiveConnectionCount(): number {
  return connections.size;
}

/**
 * Get the number of active connections for a battle
 * @param battleId Battle ID
 * @returns Number of active connections for the battle
 */
export function getBattleConnectionCount(battleId: string): number {
  const battleConnectionMap = battleConnections.get(battleId);
  return battleConnectionMap ? battleConnectionMap.size : 0;
}

/**
 * Get all battle IDs with active connections
 * @returns Array of battle IDs
 */
export function getActiveBattleIds(): string[] {
  return Array.from(battleConnections.keys());
}

/**
 * Get all user IDs with active connections for a battle
 * @param battleId Battle ID
 * @returns Array of user IDs
 */
export function getBattleUserIds(battleId: string): string[] {
  const battleConnectionMap = battleConnections.get(battleId);
  return battleConnectionMap ? Array.from(battleConnectionMap.keys()) : [];
}
