#!/bin/bash

# Create a temporary directory for the files
mkdir -p temp_upload

# Copy the modified files
cp client/src/pages/Home.tsx temp_upload/
cp client/src/components/StatusIssuesDialog.tsx temp_upload/

# Upload the files to the VPS
echo "Uploading files to VPS..."
scp -r temp_upload/* root@145.223.73.210:/opt/spectershift/client/src/pages/
scp -r client/src/components/StatusIssuesDialog.tsx root@145.223.73.210:/opt/spectershift/client/src/components/

# Clean up
rm -rf temp_upload

# Rebuild the application on the VPS
echo "Rebuilding the application on the VPS..."
ssh root@145.223.73.210 "cd /opt/spectershift && npm run build && pm2 restart all"

echo "Deployment complete!"
