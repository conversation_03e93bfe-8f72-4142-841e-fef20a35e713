# OrangeID Integration Setup Guide

This guide explains how to set up and use the OrangeID integration in SpecterShift.

## Prerequisites

1. You need to have an OrangeID tenant ID. You can request one at [https://vibecodinglist.com/orange-id-integration](https://vibecodinglist.com/orange-id-integration).
2. You need to whitelist your project URLs via the link above to enable authentication on your domain.

## Configuration

1. Open the `.env` file in the root directory
2. Replace the placeholder tenant ID with your actual tenant ID:

```
# OrangeID Integration
VITE_ORANGE_ID_TENANT_ID=your-actual-tenant-id
```

The application uses Vite's environment variable system, which requires variables to be prefixed with `VITE_` to be accessible in the client-side code.

## How It Works

The integration provides dual authentication options:

1. **OrangeID Authentication**: Users can log in using Google, Apple, or email via OrangeID.
2. **MetaMask Wallet Connection**: Users can connect their MetaMask wallet for NFT functionality.

### Authentication Flow

- When a user logs in with OrangeID, they are redirected to the OrangeID authentication page.
- After successful authentication, they are redirected back to the application with a token.
- The application uses this token to authenticate the user.
- Users can optionally connect their wallet to use NFT functionality.

### Wallet Connection

- Users can connect their MetaMask wallet directly without using OrangeID.
- This allows them to use NFT functionality without creating an OrangeID account.

## Components

The integration consists of the following components:

- `BedrockPassportWrapper.tsx`: Wraps the application with the OrangeID provider.
- `AuthContext.tsx`: Manages authentication state for both OrangeID and MetaMask.
- `AuthCallback.tsx`: Handles the callback from OrangeID after authentication.
- `DualLoginButton.tsx`: Provides a UI for users to choose between OrangeID and MetaMask.

## Testing

To test the integration:

1. Start the application with `npm run dev`.
2. Click the "Login" button in the top right corner.
3. Choose either OrangeID or MetaMask to log in.
4. If using OrangeID, you will be redirected to the OrangeID authentication page.
5. After successful authentication, you will be redirected back to the application.

## Troubleshooting

- If you encounter issues with OrangeID authentication, make sure your tenant ID is correct and your URLs are whitelisted.
- Check the browser console for any errors related to OrangeID or authentication.
- Ensure that the callback URL in the BedrockPassportWrapper matches the URL whitelisted in the OrangeID dashboard.
