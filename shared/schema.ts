import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User schema (keeping from template)
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// High scores schema
export const highScores = pgTable("high_scores", {
  id: serial("id").primaryKey(),
  playerName: text("player_name").notNull(),
  score: integer("score").notNull(),
  gameMode: text("game_mode").default("single").notNull(), // "single", "coop", "pvp"
  teamName: text("team_name"), // Only for coop and pvp modes
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertHighScoreSchema = createInsertSchema(highScores).pick({
  playerName: true,
  score: true,
  gameMode: true,
  teamName: true,
});

export type InsertHighScore = z.infer<typeof insertHighScoreSchema>;
export type HighScore = typeof highScores.$inferSelect;

// Game modes enumeration
export enum GameMode {
  SinglePlayer = 'single',
  CoOp = 'coop',
  PvP = 'pvp'
}

// Game Arena - holds the information about a multiplayer game arena
export const gameArenas = pgTable("game_arenas", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  mode: text("mode").notNull(), // "coop" or "pvp"
  maxPlayers: integer("max_players").notNull().default(4),
  createdAt: timestamp("created_at").defaultNow(),
  startedAt: timestamp("started_at"),
  endedAt: timestamp("ended_at"),
  currentLevel: integer("current_level").default(1).notNull(),
  enemiesTotal: integer("enemies_total").default(10).notNull(),
  enemiesDefeated: integer("enemies_defeated").default(0).notNull(),
  status: text("status").default("waiting").notNull(), // "waiting", "in_progress", "completed"
  settings: jsonb("settings").default({}).notNull(), // Various game settings
});

export const insertGameArenaSchema = createInsertSchema(gameArenas).pick({
  name: true,
  mode: true,
  maxPlayers: true,
  settings: true,
});

export type InsertGameArena = z.infer<typeof insertGameArenaSchema>;
export type GameArena = typeof gameArenas.$inferSelect;

// Game Players - holds information about players in an arena
export const gamePlayers = pgTable("game_players", {
  id: serial("id").primaryKey(),
  arenaId: integer("arena_id").notNull(),
  playerId: text("player_id").notNull(), // Session or connection ID
  playerName: text("player_name").notNull(),
  teamId: integer("team_id"),
  isHost: boolean("is_host").default(false).notNull(),
  score: integer("score").default(0).notNull(),
  spectersCaptured: integer("specters_captured").default(0).notNull(),
  joinedAt: timestamp("joined_at").defaultNow(),
  lastActive: timestamp("last_active").defaultNow(),
  status: text("status").default("active").notNull(), // "active", "disconnected", "spectating"
  // Add position data for random spawning
  positionX: jsonb("position_x").default(0).notNull(),
  positionY: jsonb("position_y").default(5).notNull(),
  positionZ: jsonb("position_z").default(0).notNull()
});

export const insertGamePlayerSchema = createInsertSchema(gamePlayers).pick({
  arenaId: true,
  playerId: true,
  playerName: true,
  teamId: true,
  isHost: true,
  positionX: true,
  positionY: true,
  positionZ: true
});

export type InsertGamePlayer = z.infer<typeof insertGamePlayerSchema>;
export type GamePlayer = typeof gamePlayers.$inferSelect;

// Add this position interface for consistency
export interface Position {
  x: number;
  y: number;
  z: number;
}

// Teams - information about teams in co-op or PvP games
export const gameTeams = pgTable("game_teams", {
  id: serial("id").primaryKey(),
  arenaId: integer("arena_id").notNull(),
  name: text("name").notNull(),
  color: text("color").notNull(),
  score: integer("score").default(0).notNull(),
  spectersCaptured: integer("specters_captured").default(0).notNull(),
});

export const insertGameTeamSchema = createInsertSchema(gameTeams).pick({
  arenaId: true,
  name: true,
  color: true,
});

export type InsertGameTeam = z.infer<typeof insertGameTeamSchema>;
export type GameTeam = typeof gameTeams.$inferSelect;

// Specter types schema
export const specterTypes = pgTable("specter_types", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  color: text("color").notNull(),
  basePoints: integer("base_points").notNull(),
  teleports: boolean("teleports").default(false),
  splits: boolean("splits").default(false),
  camouflages: boolean("camouflages").default(false),
});

export const insertSpecterTypeSchema = createInsertSchema(specterTypes).pick({
  name: true,
  description: true,
  color: true,
  basePoints: true,
  teleports: true,
  splits: true,
  camouflages: true,
});

export type InsertSpecterType = z.infer<typeof insertSpecterTypeSchema>;
export type SpecterType = typeof specterTypes.$inferSelect;

// Team balancing configuration
export interface TeamBalanceConfig {
  maxTeamSizeDiff: number; // Maximum allowed difference in team sizes
  autoBalanceThreshold: number; // Score difference that triggers auto-balance suggestion
  minPlayersForBalance: number; // Minimum players needed for team balance checks
}

// Default balance settings
export const DEFAULT_TEAM_BALANCE: TeamBalanceConfig = {
  maxTeamSizeDiff: 1,
  autoBalanceThreshold: 100,
  minPlayersForBalance: 4
};


// Define message types for multiplayer communication
export enum MessageType {
  PlayerJoin = 'player_join',
  PlayerLeave = 'player_leave',
  PlayerUpdate = 'player_update',
  GameStart = 'game_start',
  GameEnd = 'game_end',
  SpecterCaptured = 'specter_captured',
  LevelComplete = 'level_complete',
  LevelAdvance = 'level_advance',
  ChatMessage = 'chat_message',
  Error = 'error',
  // New message types for weapon effects
  WeaponEffectCreated = 'weapon_effect_created',
  WeaponEffectRemoved = 'weapon_effect_removed',
  // New message types for team management
  TeamJoin = 'team_join',
  TeamCreate = 'team_create',
  TeamList = 'team_list',
  TeamBalance = 'team_balance',
  // New message types for tournament battles
  TournamentBattleUpdate = 'tournament_battle_update',
  TournamentBattleRenderInstructions = 'tournament_battle_render_instructions',
  TournamentBattleJoin = 'tournament_battle_join',
  TournamentBattleLeave = 'tournament_battle_leave',
  // Connection maintenance
  Ping = 'ping',
  Pong = 'pong'
}

// Define player state for network updates
export interface PlayerState {
  id: string;
  name: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  health: number;
  jetpackFuel: number;
  firing: boolean;
  ammoType: string;
  team?: number;
  score: number;
}

// Define game state for network updates
export interface GameState {
  id: number;
  mode: GameMode;
  status: string;
  currentLevel: number;
  enemiesTotal: number;
  enemiesDefeated: number;
  players: PlayerState[];
  teams?: { id: number; name: string; color: string; score: number }[];
}

// Define weapon effect data for networking
export interface WeaponEffectData {
  id: string;
  type: 'gravity' | 'time' | 'phase';
  position: {
    x: number;
    y: number;
    z: number;
  };
  radius: number;
  timeLeft: number;
  ownerId?: string;
}

// Define team join request data
export interface TeamJoinRequest {
  teamId: number;
  playerName: string;
}

// Define team creation request data
export interface TeamCreateRequest {
  teamName: string;
  teamColor: string;
  playerName: string;
}

// Define message structure for WebSocket communication
export interface NetworkMessage {
  type: MessageType;
  data: any;
  timestamp: number;
  sender: string;
}

// Define render instruction types for tournament battles
export enum RenderInstructionType {
  MOVE_ENTITY = 'move_entity',
  ATTACK = 'attack',
  USE_POWERUP = 'use_powerup',
  DAMAGE = 'damage',
  VICTORY = 'victory',
  SPAWN_ENTITY = 'spawn_entity',
  UPDATE_HEALTH = 'update_health',
  // Legacy types from TestTournamentBattle
  SpawnPet = 'spawn_pet',
  MovePet = 'move_pet',
  AttackAnimation = 'attack_animation',
  DefendAnimation = 'defend_animation',
  SpecialAnimation = 'special_animation',
  DamageEffect = 'damage_effect',
  HealEffect = 'heal_effect'
}

// Define render instruction interface
export interface RenderInstruction {
  type: RenderInstructionType;
  data?: any;
  targetId?: string;
  duration?: number;
  effectType?: string;
}

// Define powerup types for tournament battles
export enum PowerupType {
  FIRE = 0,
  ICE = 1,
  SHIELD = 2
}
